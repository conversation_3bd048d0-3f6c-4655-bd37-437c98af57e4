# Credential Update API - Technical Implementation Specification

## Problem Statement
- **Business Issue**: No API endpoint exists to update existing credential information after initial creation
- **Current State**: Users can create and retrieve credentials, but must delete and recreate to modify them
- **Expected Outcome**: PUT /credential/update endpoint that modifies credentials using store_id + type + platform lookup, with optimized SQL construction

## Solution Overview
- **Approach**: Add PUT endpoint that reuses existing validation models and lookup patterns from GET endpoint
- **Core Changes**: New route handler and optimized database update method with simplified SQL construction
- **Success Criteria**: Update credentials without breaking existing functionality, improved database update performance

## Technical Implementation

### Database Changes
- **Tables to Modify**: None - existing agent.credential table structure is sufficient
- **New Tables**: None required
- **Migration Scripts**: None required - using existing schema

### Code Changes

#### Files to Modify

**1. src/server/routes/credential_routes.py**
- **Modification Type**: Add new route handler
- **New Function**: `update_credential(request: CredentialCreateRequest, store_id: str, type: str, platform: str)`
- **Location**: After line 159 (after credential_callback function)

**2. src/database/tables/credential.py**
- **Modification Type**: Optimize existing update method
- **Target Function**: `update()` method (lines 92-161)
- **Optimization**: Replace dynamic SQL construction with static SQL using COALESCE
- **New Method**: `update_by_store_type_platform()` for direct lookup-based updates

#### New Files
- **None**: All functionality fits in existing file structure

#### Function Signatures

**Route Handler**:
```python
@router.put("/credential/update")
async def update_credential(
    request: CredentialCreateRequest,
    store_id: str, 
    type: str, 
    platform: str
) -> ApiResponse
```

**Database Method**:
```python
def update_by_store_type_platform(
    self,
    store_id: str,
    type: str, 
    platform: str,
    company_id: Optional[str] = None,
    params: Optional[Dict[str, Any]] = None,
    status: Optional[str] = None
) -> bool
```

### API Changes

#### Endpoints
- **New Endpoint**: PUT /credential/update
- **Method**: HTTP PUT
- **Query Parameters**: 
  - `store_id` (required): Store identifier
  - `type` (required): Credential type  
  - `platform` (required): Platform name
- **Request Body**: CredentialCreateRequest model (reused)

#### Request/Response

**Request Structure**:
```json
PUT /credential/update?store_id=store123&type=api_key&platform=amazon
Content-Type: application/json

{
  "company_id": "company456",
  "store_id": "store123", 
  "type": "api_key",
  "platform": "amazon",
  "params": {
    "access_key": "new_access_key",
    "secret_key": "new_secret_key"
  }
}
```

**Response Structure**:
```json
{
  "code": "0",
  "message": "success", 
  "data": {
    "updated": true,
    "store_id": "store123",
    "type": "api_key", 
    "platform": "amazon"
  }
}
```

**Error Responses**:
- `400`: Missing required parameters
- `404`: Credential not found
- `500`: Database operation error

#### Validation Rules
- **Query Parameters**: store_id, type, platform are required
- **Request Body**: Must conform to CredentialCreateRequest schema
- **Consistency Check**: Query parameters must match request body values
- **Company Authorization**: company_id in request body must match existing credential

### Configuration Changes
- **Settings**: None required
- **Environment Variables**: None required  
- **Feature Flags**: None required

## Implementation Sequence

### Phase 1: Database Layer Optimization
**Files**: `src/database/tables/credential.py`

**Tasks**:
1. Create optimized `update_by_store_type_platform()` method with static SQL
2. Replace dynamic SQL construction in existing `update()` method
3. Add proper error handling for constraint violations

**SQL Optimization Details**:
```sql
-- Replace dynamic SQL building with static COALESCE approach
UPDATE agent.credential 
SET 
  company_id = COALESCE(%s, company_id),
  params = COALESCE(%s, params), 
  status = COALESCE(%s, status),
  update_time = CURRENT_TIMESTAMP
WHERE store_id = %s AND type = %s AND platform = %s
```

### Phase 2: API Layer Implementation  
**Files**: `src/server/routes/credential_routes.py`

**Tasks**:
1. Add PUT /credential/update route handler
2. Implement parameter validation and consistency checks
3. Add structured logging with operation details
4. Follow existing error handling patterns

**Route Implementation Pattern**:
```python
@router.put("/credential/update")
async def update_credential(
    request: CredentialCreateRequest,
    store_id: str = Query(..., description="Store ID"),
    type: str = Query(..., description="Credential type"), 
    platform: str = Query(..., description="Platform name")
):
    try:
        # Validation, database operation, logging
        # Follow exact pattern from existing endpoints
    except Exception as e:
        # Standard error handling with ApiResponse.error()
```

### Phase 3: Integration Verification
**Files**: All modified files

**Tasks**:
1. Test API endpoint with various scenarios
2. Verify backward compatibility with existing credential operations  
3. Validate error handling and logging
4. Performance test optimized SQL queries

## Validation Plan

### Unit Tests
**Test Scenarios**:
1. **Success Cases**:
   - Update credential with all fields
   - Update credential with partial fields  
   - Update credential with only params change
   
2. **Validation Cases**:
   - Missing required query parameters
   - Parameter-body consistency validation
   - Invalid credential lookup (404 scenario)
   
3. **Database Cases**:
   - SQL injection prevention
   - Concurrent update handling
   - Performance comparison (old vs new SQL)

### Integration Tests  
**End-to-End Workflows**:
1. **Create → Get → Update → Get**: Verify complete credential lifecycle
2. **Multiple Updates**: Ensure idempotent operations
3. **Error Recovery**: Test rollback behavior on failures

### Business Logic Verification
**Validation Criteria**:
1. **Lookup Consistency**: Query parameters match get_by_store_type_platform method
2. **Model Reuse**: CredentialCreateRequest validation rules apply identically  
3. **Zero Breaking Changes**: All existing credential endpoints function normally
4. **Performance Improvement**: Update operations complete faster with optimized SQL

### SQL Performance Verification
**Before/After Comparison**:
- Measure execution time for update operations
- Verify query plan optimization (no dynamic SQL compilation)
- Test with various payload sizes and parameter combinations

## Implementation Notes

### Code Quality Standards
- **Function Length**: Keep route handler under 50 lines following existing patterns
- **Error Handling**: Use try-catch with specific error types, not generic Exception
- **Logging**: Include operation context (store_id, type, platform) in all log messages
- **SQL Security**: Use parameterized queries exclusively, no string concatenation

### Performance Considerations  
- **Database Connections**: Reuse existing psycopg connection pattern
- **Transaction Scope**: Single transaction for update operation
- **Response Time**: Target <100ms for typical update operations
- **Memory Usage**: Minimal memory footprint using existing model validation

### Security Considerations
- **Parameter Validation**: Strict type checking on all inputs
- **SQL Injection**: Parameterized queries only
- **Authorization**: Verify company_id ownership before updates
- **Data Integrity**: Ensure update atomicity

This specification provides direct implementation guidance while maintaining the OnePort Agent codebase's existing patterns and quality standards. The focus on SQL optimization addresses the core technical debt while adding the required business functionality.