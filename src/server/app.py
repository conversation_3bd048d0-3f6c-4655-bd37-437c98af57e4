import logging
from fastapi import <PERSON><PERSON><PERSON>, APIRouter
from fastapi.middleware.cors import CORSMiddleware

# Import store module for access to s3_storage after initialization
from src.store import s3_storage

# Import route modules
from src.server.routes import chat_routes, credential_routes, agent_routes, init_routes

logger = logging.getLogger(__name__)

app = FastAPI(
    title="OnePort Agent API",
    description="API for OnePort Agent",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Create ai router
ai_router = APIRouter(prefix="/ai")

# Include all route modules
ai_router.include_router(chat_routes.router, tags=["chat"])
ai_router.include_router(credential_routes.router, tags=["credentials"])
ai_router.include_router(agent_routes.router, tags=["agents"])
ai_router.include_router(init_routes.router, tags=["init"])

# Include the ai router in the main app
app.include_router(ai_router)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "OnePort Agent API"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)