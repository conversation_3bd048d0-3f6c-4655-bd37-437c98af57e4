import logging
from typing import Op<PERSON>
from uuid import uuid4

from fastapi import APIRouter, Head<PERSON>
from fastapi.responses import StreamingResponse

from src.models.chat_models import ConversationListResponse, ConversationItem, ConversationDeleteRequest, ChatRequest
from src.models.common_models import ApiResponse
from src.database import ConversationTable

logger = logging.getLogger(__name__)

# Create chat router
router = APIRouter()

@router.post("/conversation")
async def create_conversation(request: ChatRequest, x_user_id: str = Header(..., alias="X-User-ID")):
    """创建新对话并启动异步处理任务"""
    pass
    
@router.get("/conversation/get")
async def get_conversation(request: ChatRequest):
    pass

@router.get("/conversation/list")
async def list_conversations(company_id: str, limit: int = 10, offset: int = 0, x_user_id: Optional[str] = Header(None, alias="X-User-ID")):
    """List conversations filtered by company_id and optionally by user_id"""
    try:
        conversation_table = ConversationTable()
        
        # Get conversations using the new method
        conversations_data = conversation_table.get_by_company_and_user(
            company_id=company_id,
            user_id=x_user_id,
            limit=limit,
            offset=offset
        )
        
        # Convert database results to response model
        conversations = [
            ConversationItem(
                conversation_id=conv["id"],
                title=conv["title"],
                create_time=conv["create_time"],
                update_time=conv["update_time"],
                status=conv["status"],
                user_id=conv["user_id"]
            )
            for conv in conversations_data
        ]
        
        response_data = ConversationListResponse(conversations=conversations)
        return ApiResponse.success(data=response_data)
        
    except Exception as e:
        logger.error(f"Error listing conversations: {str(e)}")
        return ApiResponse.error(code="500", message=f"Failed to list conversations: {str(e)}")

@router.post("/conversation/stop")
async def stop_conversation(request: ChatRequest):
    pass

@router.post("/conversation/delete")
async def delete_conversation(request: ConversationDeleteRequest):
    """Delete a conversation by ID with company authorization"""
    try:
        conversation_table = ConversationTable()
        
        # Delete conversation with company_id filter for security
        success = conversation_table.delete(
            conversation_id=request.conversation_id,
            company_id=request.company_id
        )

        if success:
            return ApiResponse.success(message="Conversation deleted successfully")
        else:
            return ApiResponse.error(code="404", message="Conversation not found")
            
    except Exception as e:
        logger.error(f"Error deleting conversation: {str(e)}")
        return ApiResponse.error(code="500", message=f"Failed to delete conversation: {str(e)}")
