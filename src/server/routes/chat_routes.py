import logging
from typing import Optional, List

from fastapi import APIRouter, Header
from fastapi.responses import StreamingResponse

from src.models.chat_models import ConversationListResponse, ConversationItem, ConversationDeleteRequest, ChatRequest
from src.models.common_models import ApiResponse
from src.database import ConversationTable, AgentTable
from src.graph.types import SubAgent

logger = logging.getLogger(__name__)

# Create chat router
router = APIRouter()

@router.post("/conversation")
async def create_conversation(request: ChatRequest, x_user_id: str = Header(..., alias="X-User-ID")):
    """创建新对话并启动异步处理任务"""
    try:
        logger.info(f"Creating conversation for company_id: {request.company_id}, user_id: {x_user_id}")

        # 1. 根据 company_id 获取该公司的所有 agents
        agent_table = AgentTable()
        agents_data = agent_table.get_by_company(company_id=request.company_id)

        if not agents_data:
            return ApiResponse.error(code="404", message="No agents found for this company")

        # 2. 分类 agents：Personal Assistant 作为主 agent，其他作为子 agents
        main_agent = None
        sub_agents_data = []

        for agent_data in agents_data:
            if agent_data["role"] == "Personal Assistant":
                main_agent = agent_data
            elif agent_data["role"] in ["Data Collector", "Data Analyst"]:
                sub_agents_data.append(agent_data)

        if not main_agent:
            return ApiResponse.error(code="404", message="Personal Assistant agent not found for this company")

        # 3. 构建子 agents 配置
        subagents: List[SubAgent] = []
        for sub_agent_data in sub_agents_data:
            subagent: SubAgent = {
                "name": sub_agent_data["name"],
                "description": f"{sub_agent_data['role']} - {sub_agent_data.get('prompt', 'Specialized agent for data operations')}",
                "prompt": sub_agent_data.get("prompt", f"You are a {sub_agent_data['role']} agent. Help with data-related tasks."),
            }
            # 如果有指定工具，添加到子 agent
            if sub_agent_data.get("tools"):
                subagent["tools"] = sub_agent_data["tools"]
            subagents.append(subagent)

        # 4. 创建主 agent 实例
        main_agent_instructions = main_agent.get("prompt", "You are a Personal Assistant. Help users with their requests and coordinate with specialized agents when needed.")

        # 创建 agent 实例（这里暂时不实际创建，只是准备配置）
        agent_config = {
            "main_agent": main_agent,
            "subagents": subagents,
            "instructions": main_agent_instructions,
            "tools": main_agent.get("tools", [])
        }

        # 5. 创建对话记录
        conversation_table = ConversationTable()
        conversation_id = conversation_table.create(
            user_id=x_user_id,
            company_id=request.company_id,
            title="New Conversation",
            status="in_progress",
            conversation_id=request.conversation_id,
            store_ids=request.store_id
        )

        logger.info(f"Successfully created conversation {conversation_id} with main agent {main_agent['name']} and {len(subagents)} sub-agents")

        return ApiResponse.success(
            data={
                "conversation_id": conversation_id,
                "main_agent": main_agent["name"],
                "sub_agents": [sa["name"] for sa in subagents],
                "agent_config": agent_config
            },
            message="Conversation created successfully"
        )

    except Exception as e:
        logger.error(f"Error creating conversation: {str(e)}")
        return ApiResponse.error(code="500", message=f"Failed to create conversation: {str(e)}")
    
@router.get("/conversation/get")
async def get_conversation(request: ChatRequest):
    pass

@router.get("/conversation/list")
async def list_conversations(company_id: str, limit: int = 10, offset: int = 0, x_user_id: Optional[str] = Header(None, alias="X-User-ID")):
    """List conversations filtered by company_id and optionally by user_id"""
    try:
        conversation_table = ConversationTable()
        
        # Get conversations using the new method
        conversations_data = conversation_table.get_by_company_and_user(
            company_id=company_id,
            user_id=x_user_id,
            limit=limit,
            offset=offset
        )
        
        # Convert database results to response model
        conversations = [
            ConversationItem(
                conversation_id=conv["id"],
                title=conv["title"],
                create_time=conv["create_time"],
                update_time=conv["update_time"],
                status=conv["status"],
                user_id=conv["user_id"]
            )
            for conv in conversations_data
        ]
        
        response_data = ConversationListResponse(conversations=conversations)
        return ApiResponse.success(data=response_data)
        
    except Exception as e:
        logger.error(f"Error listing conversations: {str(e)}")
        return ApiResponse.error(code="500", message=f"Failed to list conversations: {str(e)}")

@router.post("/conversation/stop")
async def stop_conversation(request: ChatRequest):
    pass

@router.post("/conversation/delete")
async def delete_conversation(request: ConversationDeleteRequest):
    """Delete a conversation by ID with company authorization"""
    try:
        conversation_table = ConversationTable()
        
        # Delete conversation with company_id filter for security
        success = conversation_table.delete(
            conversation_id=request.conversation_id,
            company_id=request.company_id
        )

        if success:
            return ApiResponse.success(message="Conversation deleted successfully")
        else:
            return ApiResponse.error(code="404", message="Conversation not found")
            
    except Exception as e:
        logger.error(f"Error deleting conversation: {str(e)}")
        return ApiResponse.error(code="500", message=f"Failed to delete conversation: {str(e)}")
