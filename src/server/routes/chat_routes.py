import logging
import asyncio
import json
from typing import Optional, List
from uuid import uuid4

from fastapi import APIRouter, Header
from fastapi.responses import StreamingResponse

from src.models.chat_models import ConversationListResponse, ConversationItem, ConversationDeleteRequest, ChatRequest
from src.models.common_models import ApiResponse
from src.database import ConversationTable, AgentTable
from src.graph.types import SubAgent
from src.graph.agents import async_create_deep_agent

logger = logging.getLogger(__name__)

# Create chat router
router = APIRouter()

# 全局队列字典，用于存储每个对话的数据流
conversation_queues = {}

async def agent_task(conversation_id: str, request: ChatRequest, x_user_id: str, main_agent_data: dict, subagents_config: List[SubAgent]):
    """异步任务：创建 agent 并处理用户查询"""
    try:
        logger.info(f"Starting agent task for conversation {conversation_id}")

        # 创建队列用于流式数据传输
        queue = asyncio.Queue()
        conversation_queues[conversation_id] = queue

        # 发送初始状态
        await queue.put({
            "type": "status",
            "data": "Initializing agents...",
            "timestamp": asyncio.get_event_loop().time()
        })

        # 1. 创建主 agent 实例
        main_agent_instructions = main_agent_data.get("prompt", "You are a Personal Assistant. Help users with their requests and coordinate with specialized agents when needed.")

        # 获取主 agent 的工具
        main_agent_tools = main_agent_data.get("tools", [])

        await queue.put({
            "type": "status",
            "data": f"Creating main agent: {main_agent_data['name']}",
            "timestamp": asyncio.get_event_loop().time()
        })

        # 使用 async_create_deep_agent 创建主 agent，并配置子 agents
        agent = async_create_deep_agent(
            tools=main_agent_tools,  # 主 agent 的工具
            instructions=main_agent_instructions,
            subagents=subagents_config,  # 子 agents 配置
            # model=None,  # 使用默认模型
            # checkpointer=None  # 暂时不使用检查点
        )

        await queue.put({
            "type": "status",
            "data": f"Agent created successfully with {len(subagents_config)} sub-agents",
            "timestamp": asyncio.get_event_loop().time()
        })

        # 2. 处理用户查询
        if request.messages and len(request.messages) > 0:
            # 获取最后一条用户消息
            last_message = request.messages[-1]
            user_query = last_message.content if hasattr(last_message, 'content') else str(last_message)

            await queue.put({
                "type": "status",
                "data": f"Processing query: {user_query[:100]}...",
                "timestamp": asyncio.get_event_loop().time()
            })

            # 3. 调用 agent 处理查询
            config = {"configurable": {"thread_id": conversation_id}}

            # 流式处理 agent 响应
            async for chunk in agent.astream(
                {"messages": [{"role": "user", "content": user_query}]},
                config=config
            ):
                # 将 agent 生成的数据流式写入队列
                await queue.put({
                    "type": "agent_response",
                    "data": chunk,
                    "timestamp": asyncio.get_event_loop().time()
                })
        else:
            await queue.put({
                "type": "status",
                "data": "No user query provided, agent ready for interaction",
                "timestamp": asyncio.get_event_loop().time()
            })

        # 任务完成
        await queue.put({
            "type": "complete",
            "data": "Agent task completed successfully",
            "timestamp": asyncio.get_event_loop().time()
        })

    except Exception as e:
        logger.error(f"Error in agent task for conversation {conversation_id}: {str(e)}")
        if conversation_id in conversation_queues:
            await conversation_queues[conversation_id].put({
                "type": "error",
                "data": f"Agent task error: {str(e)}",
                "timestamp": asyncio.get_event_loop().time()
            })

async def stream_generator(conversation_id: str):
    """生成器：从队列中读取数据并流式返回"""
    try:
        if conversation_id not in conversation_queues:
            yield f"data: {json.dumps({'type': 'error', 'data': 'Conversation not found'})}\n\n"
            return

        queue = conversation_queues[conversation_id]

        while True:
            try:
                # 从队列中获取数据，设置超时
                item = await asyncio.wait_for(queue.get(), timeout=30.0)

                # 将数据转换为 SSE 格式
                yield f"data: {json.dumps(item)}\n\n"

                # 如果是完成或错误消息，结束流
                if item.get("type") in ["complete", "error"]:
                    break

            except asyncio.TimeoutError:
                # 发送心跳
                yield f"data: {json.dumps({'type': 'heartbeat', 'timestamp': asyncio.get_event_loop().time()})}\n\n"

    except Exception as e:
        logger.error(f"Error in stream generator for conversation {conversation_id}: {str(e)}")
        yield f"data: {json.dumps({'type': 'error', 'data': str(e)})}\n\n"
    finally:
        # 清理队列
        if conversation_id in conversation_queues:
            del conversation_queues[conversation_id]

@router.post("/conversation")
async def create_conversation(request: ChatRequest, x_user_id: str = Header(..., alias="X-User-ID")):
    """创建新对话并启动异步处理任务，返回流式响应"""
    try:
        logger.info(f"Creating conversation for company_id: {request.company_id}, user_id: {x_user_id}")

        # 1. 根据 company_id 获取该公司的所有 agents
        agent_table = AgentTable()
        agents_data = agent_table.get_by_company(company_id=request.company_id)

        if not agents_data:
            return ApiResponse.error(code="404", message="No agents found for this company")

        # 2. 分类 agents：Personal Assistant 作为主 agent，其他作为子 agents
        main_agent = None
        sub_agents_data = []

        for agent_data in agents_data:
            if agent_data["role"] == "Personal Assistant":
                main_agent = agent_data
            elif agent_data["role"] in ["Data Collector", "Data Analyst"]:
                sub_agents_data.append(agent_data)

        if not main_agent:
            return ApiResponse.error(code="404", message="Personal Assistant agent not found for this company")

        # 3. 构建子 agents 配置
        subagents: List[SubAgent] = []
        for sub_agent_data in sub_agents_data:
            subagent: SubAgent = {
                "name": sub_agent_data["name"],
                "description": f"{sub_agent_data['role']} - {sub_agent_data.get('prompt', 'Specialized agent for data operations')}",
                "prompt": sub_agent_data.get("prompt", f"You are a {sub_agent_data['role']} agent. Help with data-related tasks."),
            }
            # 如果有指定工具，添加到子 agent
            if sub_agent_data.get("tools"):
                subagent["tools"] = sub_agent_data["tools"]
            subagents.append(subagent)

        # 4. 创建对话记录
        conversation_table = ConversationTable()
        conversation_id = conversation_table.create(
            user_id=x_user_id,
            company_id=request.company_id,
            title="New Conversation",
            status="in_progress",
            conversation_id=request.conversation_id or str(uuid4()),
            store_ids=request.store_id
        )

        # 5. 启动异步任务
        asyncio.create_task(agent_task(
            conversation_id=conversation_id,
            request=request,
            x_user_id=x_user_id,
            main_agent_data=main_agent,
            subagents_config=subagents
        ))

        logger.info(f"Started agent task for conversation {conversation_id} with main agent {main_agent['name']} and {len(subagents)} sub-agents")

        # 6. 返回流式响应
        return StreamingResponse(
            stream_generator(conversation_id),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
            }
        )

    except Exception as e:
        logger.error(f"Error creating conversation: {str(e)}")
        return ApiResponse.error(code="500", message=f"Failed to create conversation: {str(e)}")
    
@router.get("/conversation/get")
async def get_conversation(request: ChatRequest):
    pass

@router.get("/conversation/list")
async def list_conversations(company_id: str, limit: int = 10, offset: int = 0, x_user_id: Optional[str] = Header(None, alias="X-User-ID")):
    """List conversations filtered by company_id and optionally by user_id"""
    try:
        conversation_table = ConversationTable()
        
        # Get conversations using the new method
        conversations_data = conversation_table.get_by_company_and_user(
            company_id=company_id,
            user_id=x_user_id,
            limit=limit,
            offset=offset
        )
        
        # Convert database results to response model
        conversations = [
            ConversationItem(
                conversation_id=conv["id"],
                title=conv["title"],
                create_time=conv["create_time"],
                update_time=conv["update_time"],
                status=conv["status"],
                user_id=conv["user_id"]
            )
            for conv in conversations_data
        ]
        
        response_data = ConversationListResponse(conversations=conversations)
        return ApiResponse.success(data=response_data)
        
    except Exception as e:
        logger.error(f"Error listing conversations: {str(e)}")
        return ApiResponse.error(code="500", message=f"Failed to list conversations: {str(e)}")

@router.post("/conversation/stop")
async def stop_conversation(request: ChatRequest):
    pass

@router.post("/conversation/delete")
async def delete_conversation(request: ConversationDeleteRequest):
    """Delete a conversation by ID with company authorization"""
    try:
        conversation_table = ConversationTable()
        
        # Delete conversation with company_id filter for security
        success = conversation_table.delete(
            conversation_id=request.conversation_id,
            company_id=request.company_id
        )

        if success:
            return ApiResponse.success(message="Conversation deleted successfully")
        else:
            return ApiResponse.error(code="404", message="Conversation not found")
            
    except Exception as e:
        logger.error(f"Error deleting conversation: {str(e)}")
        return ApiResponse.error(code="500", message=f"Failed to delete conversation: {str(e)}")
