import logging
from fastapi import APIRouter, HTTPException
from typing import Optional

from src.models.credential_models import (
    CredentialCreateRequest,
    CredentialResponse,
)
from src.models.common_models import ApiResponse
from src.database import CredentialTable

logger = logging.getLogger(__name__)

# Create credential router
router = APIRouter()


@router.get("/credential/list")
async def get_credential_list(
    company_id: str,
    limit: int = 10,
    offset: int = 0
):
    try:
        logger.info(f"Get credential list request: company_id={company_id}, limit={limit}, offset={offset}")
        
        # Simple validation
        if not company_id:
            raise HTTPException(status_code=400, detail="company_id is required")
        
        # Fetch credentials from database
        credential_table = CredentialTable()
        credentials = credential_table.get_by_company_id(company_id, limit=limit, offset=offset)
        
        # Group credentials by store_id
        stores_dict = {}
        for credential in credentials:
            store_id = credential["store_id"]
            if store_id not in stores_dict:
                stores_dict[store_id] = {
                    "store_id": store_id,
                    "credentials": []
                }
            
            stores_dict[store_id]["credentials"].append({
                "type": credential["type"],
                "platform": credential["platform"],
                "status": credential["status"]
            })
        
        stores_list = list(stores_dict.values())
        
        response_data = {
            "total": str(len(stores_list)),
            "stores": stores_list
        }
        
        return ApiResponse.success(data=response_data)
        
    except Exception as e:
        logger.error(f"Error getting credential list: {e}")
        return ApiResponse.error(code="500", message=f"Error getting credential list: {str(e)}")


@router.post("/credential/create")
async def create_credential(request: CredentialCreateRequest):
    try:
        logger.info(f"Create auth request: {request.model_dump()}")
        
        # Simple validation
        if not request.company_id or not request.store_id:
            raise HTTPException(status_code=400, detail="company_id and store_id are required")
        
        # Create credential in database
        credential_table = CredentialTable()
        credential_id = credential_table.create(
            type=request.type,
            store_id=request.store_id,
            company_id=request.company_id,
            params=request.params,
            status="auth",
            platform=request.platform
        )
        
        logger.info(f"Created credential with ID: {credential_id} for company_id: {request.company_id}, store_id: {request.store_id}, platform: {request.platform}")
        
        return ApiResponse.success(data={"credential_id": credential_id})
        
    except Exception as e:
        logger.error(f"Error creating credential: {e}")
        return ApiResponse.error(code="500", message=f"Error creating credential: {str(e)}")


@router.get("/credential/get")
async def get_credential(
    store_id: str,
    type: str,
    platform: str
):
    try:
        logger.info(f"Get credential request: store_id={store_id}, type={type}, platform={platform}")
        
        # Simple validation
        if not store_id or not type or not platform:
            raise HTTPException(status_code=400, detail="store_id, type, and platform are required")
        
        # Fetch credential from database
        credential_table = CredentialTable()
        credential_data = credential_table.get_by_store_type_platform(
            store_id=store_id,
            type=type,
            platform=platform
        )
        
        if not credential_data:
            return ApiResponse.error(code="404", message="Credential not found")
        
        # Format response according to API spec
        response_data = CredentialResponse(
            store_id=credential_data["store_id"],
            type=credential_data["type"],
            platform=credential_data["platform"],
            params=credential_data["params"] or {},
            status=credential_data["status"]
        )
        
        return ApiResponse.success(data=response_data.model_dump())
        
    except Exception as e:
        logger.error(f"Error getting credential: {e}")
        return ApiResponse.error(code="500", message=f"Error getting credential: {str(e)}")
    
@router.post("/credential/update")
async def update_credential(
    request: CredentialCreateRequest,
):
    try:
        logger.info(f"Update credential request: store_id={request.store_id}, type={request.type}, platform={request.platform}")
        
        # Validate required parameters
        if not request.store_id or not request.type or not request.platform:
            raise HTTPException(status_code=400, detail="store_id, type, and platform are required")
        
        # Check if credential exists and verify ownership
        credential_table = CredentialTable()
        existing_credential = credential_table.get_by_store_type_platform(
            store_id=request.store_id,
            type=request.type,
            platform=request.platform
        )
        
        if not existing_credential:
            return ApiResponse.error(code="404", message="Credential not found")
        
        # Update credential
        success = credential_table.update_by_store_type_platform(
            store_id=request.store_id,
            type=request.type,
            platform=request.platform,
            company_id=request.company_id,
            params=request.params,
            status=getattr(request, 'status', None)
        )
        
        if success:
            logger.info(f"Updated credential: store_id={request.store_id}, type={request.type}, platform={request.platform}")
            return ApiResponse.success()
        else:
            return ApiResponse.error(code="500", message="Failed to update credential")
        
    except Exception as e:
        logger.error(f"Error updating credential: {e}")
        return ApiResponse.error(code="500", message=f"Error updating credential: {str(e)}")


@router.get("/credential/callback")
async def credential_callback(
    code: Optional[str] = None,
    state: Optional[str] = None,
    error: Optional[str] = None,
):
    try:
        logger.info(f"OAuth callback: code={'***' if code else None}, state={state}, error={error}")
        
        if error:
            logger.error(f"OAuth authorization failed: {error}")
            return ApiResponse.error(code="oauth_error", message=f"Authorization failed: {error}")
        
        if not code or not state:
            logger.error(f"Missing required OAuth parameters: code={bool(code)}, state={bool(state)}")
            return ApiResponse.error(code="400", message="Missing required parameters: code and state")
        
        # TODO: Implement OAuth token exchange
        # 1. Validate state parameter to prevent CSRF attacks
        # 2. Exchange authorization code for access token
        # 3. Store credentials in database
        
        return ApiResponse.success(data={"message": "OAuth callback received", "state": state})
        
    except Exception as e:
        logger.error(f"Error processing OAuth callback: {e}")
        return ApiResponse.error(code="500", message=f"Error processing callback: {str(e)}")
