import logging
from fastapi import APIRouter, Query

from src.models.agent_models import (
    Agent,
    AgentListResponse,
    AgentCreateRequest,
    AgentUpdateRequest,
)
from src.models.common_models import ApiResponse
from src.database.tables.agent import AgentTable

logger = logging.getLogger(__name__)

# Create agent router
router = APIRouter()


@router.get("/agent/list", response_model=dict)
async def get_agent_list(
    company_id: str = Query(..., description="Company ID"),
    limit: int = Query(20, ge=1, le=100, description="Number of agents to return"),
    offset: int = Query(0, ge=0, description="Number of agents to skip")
):
    """Get list of agents for a company."""
    try:
        logger.info(f"Get agent list request: company_id={company_id}, limit={limit}, offset={offset}")
        
        if not company_id:
            return ApiResponse.error(code="400", message="company_id is required")
        
        # Fetch agents from database
        agent_table = AgentTable()
        agents_data = agent_table.get_by_company(company_id, limit, offset)
        
        # Convert to Agent objects
        agents = [Agent(**agent_data) for agent_data in agents_data]
        
        # Create response
        response_data = AgentListResponse(
            total=len(agents),
            agents=agents
        )
        
        return ApiResponse.success(data=response_data.model_dump())
        
    except Exception as e:
        logger.error(f"Error getting agent list: {e}")
        return ApiResponse.error(code="500", message=f"Error getting agent list: {str(e)}")


@router.get("/agent/get", response_model=dict)
async def get_agent_detail(agent_id: str):
    """Get detailed information about a specific agent."""
    try:
        logger.info(f"Get agent detail request: agent_id={agent_id}")
        
        if not agent_id:
            return ApiResponse.error(code="400", message="agent_id is required")
        
        # Fetch agent from database
        agent_table = AgentTable()
        agent_data = agent_table.get(agent_id)
        
        if not agent_data:
            return ApiResponse.error(code="404", message=f"Agent {agent_id} not found")
        
        # Create Agent object with full details
        agent = Agent(**agent_data)
        
        return ApiResponse.success(data=agent.model_dump())
        
    except Exception as e:
        logger.error(f"Error getting agent detail: {e}")
        return ApiResponse.error(code="500", message=f"Error getting agent detail: {str(e)}")


@router.post("/agent/create", response_model=dict)
async def create_agent(request: AgentCreateRequest):
    """Create a new agent."""
    try:
        logger.info(f"Create agent request: {request.name} for company {request.company_id}")
        
        # Create agent in database
        agent_table = AgentTable()
        agent_id = agent_table.create(
            name=request.name,
            role=request.role,
            company_id=request.company_id,
            agent_type=request.type,
            prompt=request.prompt,
            tools=request.tools,
            docs=request.docs
        )
        
        # Fetch the created agent
        agent_data = agent_table.get(agent_id)
        agent = Agent(**agent_data)
        
        logger.info(f"Successfully created agent {agent_id}")
        
        return ApiResponse.success(data=agent.model_dump(), message="Agent created successfully")
        
    except Exception as e:
        logger.error(f"Error creating agent: {e}")
        return ApiResponse.error(code="500", message=f"Error creating agent: {str(e)}")


@router.post("/agent/update", response_model=dict)
async def update_agent(request: AgentUpdateRequest):
    """Update an existing agent."""
    try:
        agent_id = request.agent_id
        logger.info(f"Update agent request: agent_id={agent_id}")
        
        if not agent_id:
            return ApiResponse.error(code="400", message="agent_id is required")
        
        # Check if agent exists
        agent_table = AgentTable()
        existing_agent = agent_table.get(agent_id)
        if not existing_agent:
            return ApiResponse.error(code="404", message=f"Agent {agent_id} not found")
        
        # Validate at least one field to update is provided
        update_data = {k: v for k, v in request.model_dump().items() if v is not None and k != "agent_id"}
        if not update_data:
            return ApiResponse.error(
                code="400", 
                message="At least one field must be provided for update"
            )
        
        # Update agent in database
        success = agent_table.update(
            agent_id=agent_id,
            prompt=request.prompt,
            tools=request.tools,
            docs=request.docs
        )
        
        if not success:
            return ApiResponse.error(code="500", message="Failed to update agent")
        
        # Fetch updated agent
        updated_agent_data = agent_table.get(agent_id)
        updated_agent = Agent(**updated_agent_data)
        
        logger.info(f"Successfully updated agent {agent_id}")
        
        return ApiResponse.success(data=updated_agent.model_dump(), message="Agent updated successfully")
        
    except Exception as e:
        logger.error(f"Error updating agent: {e}")
        return ApiResponse.error(code="500", message=f"Error updating agent: {str(e)}")