import logging
from fastapi import APIRouter, HTTPException

from src.models.common_models import ApiResponse
from src.models.init_models import InitRequest
from src.graph.agents import DEFAULT_AGENT_LIST
from src.database.tables.agent import AgentTable

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/init")
async def init(request: InitRequest):
    try:
        logger.info(f"Init request: company_id={request.company_id}")
        
        if not request.company_id:
            raise HTTPException(status_code=400, detail="company_id is required")
        
        # Initialize AgentTable
        agent_table = AgentTable()
        
        # Create default agents for the company
        for agent_config in DEFAULT_AGENT_LIST:
            try:
                agent_id = agent_table.create(
                    name=agent_config["name"],
                    role=agent_config["role"],
                    company_id=request.company_id,
                    agent_type=agent_config["tag"]
                )
                logger.info(f"Created agent: {agent_config['name']} with ID: {agent_id}")
            except Exception as agent_error:
                logger.warning(f"Failed to create agent {agent_config['name']}: {agent_error}")
                # Continue with other agents even if one fails
                continue
        
        return ApiResponse.success()
        
    except Exception as e:
        logger.error(f"Error in init: {e}")
        return ApiResponse.error(code="500", message=f"Error in init: {str(e)}")