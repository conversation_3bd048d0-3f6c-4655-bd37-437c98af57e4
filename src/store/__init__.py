"""
Database storage module for OnePort Agent.
"""
import logging

from src.store.storage import S3Storage, create_s3_storage
from src.config import get_config
from langgraph.checkpoint.redis import RedisSaver

logger = logging.getLogger(__name__)

# 全局S3存储实例
s3_storage = None

def init_storage():
    """初始化存储系统"""
    global s3_storage
    
    try:
        config = get_config()
    except RuntimeError:
        logger.warning("配置未初始化，跳过存储系统初始化")
        return

    # 创建redis checkpoint
    # if config.is_redis_enabled():
    #     try:
    #         with RedisSaver.from_conn_string(config.redis_uri) as memory:
    #             memory.setup()
    #         logger.info("Redis checkpoint initialized successfully")
    #     except Exception as e:
    #         logger.error(f"Error initializing Redis: {str(e)}")
    # else:
    #     logger.warning("Redis功能未启用，跳过Redis初始化")

    # 创建全局S3存储实例
    if config.is_s3_enabled():
        try:
            s3_config = config.get_s3_config()
            s3_storage = create_s3_storage(
                bucket_name=s3_config["bucket_name"],
                aws_access_key_id=s3_config["access_key"],
                aws_secret_access_key=s3_config["secret_key"],
                region_name=s3_config["region"],
                endpoint_url=s3_config["endpoint"]
            )
            logger.info(f"Global S3 storage initialized successfully with bucket: {s3_config['bucket_name']}")
        except Exception as e:
            logger.error(f"Error initializing S3 storage: {str(e)}")
            # Log the full error traceback for debugging
            import traceback
            logger.error(traceback.format_exc())
            s3_storage = None
    else:
        logger.warning("S3功能未启用，跳过S3存储初始化")
        s3_storage = None

# 注意：init_storage() 现在由服务启动时显式调用，不再自动初始化


__all__ = ["s3_storage", "S3Storage", "create_s3_storage", "init_storage"]
