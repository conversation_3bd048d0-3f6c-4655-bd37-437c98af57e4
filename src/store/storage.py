import boto3
import os
import logging
from typing import Optional, Dict, Any
from botocore.exceptions import ClientError, NoCredentialsError

logger = logging.getLogger(__name__)


class S3Storage:
    """S3存储服务类，提供文件上传、下载和删除功能"""
    
    def __init__(
        self,
        bucket_name: str,
        aws_access_key_id: Optional[str] = None,
        aws_secret_access_key: Optional[str] = None,
        region_name: str = "us-east-1",
        endpoint_url: Optional[str] = None
    ):
        """
        初始化S3存储客户端
        
        Args:
            bucket_name: S3存储桶名称
            aws_access_key_id: AWS访问密钥ID，如果为None则使用环境变量
            aws_secret_access_key: AWS访问密钥，如果为None则使用环境变量
            region_name: AWS区域名称，默认为us-east-1
            endpoint_url: 自定义端点URL，用于兼容S3的服务如MinIO
        """
        self.bucket_name = bucket_name
        self._endpoint_url = endpoint_url
        self._region_name = region_name
        
        # 配置AWS凭证
        session_kwargs = {
            'region_name': region_name
        }
        
        if aws_access_key_id and aws_secret_access_key:
            session_kwargs.update({
                'aws_access_key_id': aws_access_key_id,
                'aws_secret_access_key': aws_secret_access_key
            })
        
        try:
            session = boto3.Session(**session_kwargs)
            client_kwargs = {}
            if endpoint_url:
                client_kwargs['endpoint_url'] = endpoint_url
                
            self.s3_client = session.client('s3', **client_kwargs)
            
            # 验证连接和存储桶访问权限
            self._verify_bucket_access()
            
        except NoCredentialsError:
            logger.error("AWS凭证未找到。请设置环境变量或传递凭证参数。")
            raise
        except Exception as e:
            logger.error(f"初始化S3客户端失败: {str(e)}")
            raise
    
    def _verify_bucket_access(self) -> bool:
        """验证存储桶访问权限"""
        try:
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            logger.info(f"成功连接到S3存储桶: {self.bucket_name}")
            return True
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                logger.error(f"存储桶 {self.bucket_name} 不存在")
            elif error_code == '403':
                logger.error(f"没有访问存储桶 {self.bucket_name} 的权限")
            else:
                logger.error(f"验证存储桶访问权限失败: {str(e)}")
            raise
    
    def upload_file(
        self,
        local_file_path: str,
        s3_key: str,
        metadata: Optional[Dict[str, str]] = None,
        content_type: Optional[str] = None
    ) -> bool:
        """
        上传文件到S3
        
        Args:
            local_file_path: 本地文件路径
            s3_key: S3中的对象键（文件路径）
            metadata: 文件元数据
            content_type: 文件内容类型
            
        Returns:
            bool: 上传是否成功
        """
        try:
            if not os.path.exists(local_file_path):
                logger.error(f"本地文件不存在: {local_file_path}")
                return False
            
            extra_args = {}
            if metadata:
                extra_args['Metadata'] = metadata
            if content_type:
                extra_args['ContentType'] = content_type
            
            # 上传文件
            self.s3_client.upload_file(
                local_file_path,
                self.bucket_name,
                s3_key,
                ExtraArgs=extra_args if extra_args else None
            )
            
            logger.info(f"文件上传成功: {local_file_path} -> s3://{self.bucket_name}/{s3_key}")
            return True
            
        except FileNotFoundError:
            logger.error(f"本地文件未找到: {local_file_path}")
            return False
        except ClientError as e:
            logger.error(f"上传文件失败: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"上传文件时发生未知错误: {str(e)}")
            return False
    
    def upload_data(
        self,
        data: bytes,
        s3_key: str,
        metadata: Optional[Dict[str, str]] = None,
        content_type: Optional[str] = None
    ) -> bool:
        """
        直接上传数据到S3（无需本地文件）
        
        Args:
            data: 要上传的二进制数据
            s3_key: S3中的对象键（文件路径）
            metadata: 文件元数据
            content_type: 文件内容类型
            
        Returns:
            bool: 上传是否成功
        """
        try:
            from io import BytesIO
            
            # 将数据转换为文件对象
            data_stream = BytesIO(data)
            
            extra_args = {}
            if metadata:
                extra_args['Metadata'] = metadata
            if content_type:
                extra_args['ContentType'] = content_type
            
            # 直接上传数据
            self.s3_client.upload_fileobj(
                data_stream,
                self.bucket_name,
                s3_key,
                ExtraArgs=extra_args if extra_args else None
            )
            
            logger.info(f"数据上传成功: {len(data)} 字节 -> s3://{self.bucket_name}/{s3_key}")
            return True
            
        except ClientError as e:
            logger.error(f"上传数据失败: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"上传数据时发生未知错误: {str(e)}")
            return False
    
    def upload_text(
        self,
        text: str,
        s3_key: str,
        encoding: str = 'utf-8',
        metadata: Optional[Dict[str, str]] = None,
        content_type: str = 'text/plain'
    ) -> bool:
        """
        直接上传文本数据到S3
        
        Args:
            text: 要上传的文本内容
            s3_key: S3中的对象键（文件路径）
            encoding: 文本编码，默认为utf-8
            metadata: 文件元数据
            content_type: 文件内容类型，默认为text/plain
            
        Returns:
            bool: 上传是否成功
        """
        try:
            # 将文本转换为字节数据
            data = text.encode(encoding)
            
            # 设置内容类型，包含编码信息
            if content_type == 'text/plain' and encoding != 'utf-8':
                content_type = f'text/plain; charset={encoding}'
            
            return self.upload_data(
                data=data,
                s3_key=s3_key,
                metadata=metadata,
                content_type=content_type
            )
            
        except UnicodeEncodeError as e:
            logger.error(f"文本编码失败: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"上传文本时发生未知错误: {str(e)}")
            return False
    
    def upload_json(
        self,
        data: dict,
        s3_key: str,
        metadata: Optional[Dict[str, str]] = None,
        ensure_ascii: bool = False,
        indent: Optional[int] = None
    ) -> bool:
        """
        直接上传JSON数据到S3
        
        Args:
            data: 要上传的字典数据
            s3_key: S3中的对象键（文件路径）
            metadata: 文件元数据
            ensure_ascii: 是否确保ASCII编码
            indent: JSON缩进，None表示紧凑格式
            
        Returns:
            bool: 上传是否成功
        """
        try:
            import json
            
            # 将字典转换为JSON字符串
            json_text = json.dumps(data, ensure_ascii=ensure_ascii, indent=indent)
            
            return self.upload_text(
                text=json_text,
                s3_key=s3_key,
                metadata=metadata,
                content_type='application/json'
            )
            
        except (TypeError, ValueError) as e:
            logger.error(f"JSON序列化失败: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"上传JSON时发生未知错误: {str(e)}")
            return False
    
    def download_file(
        self,
        s3_key: str,
        local_file_path: str,
        create_dirs: bool = True
    ) -> bool:
        """
        从S3下载文件
        
        Args:
            s3_key: S3中的对象键（文件路径）
            local_file_path: 本地保存路径
            create_dirs: 是否自动创建目录
            
        Returns:
            bool: 下载是否成功
        """
        try:
            # 创建目录（如果需要）
            if create_dirs:
                os.makedirs(os.path.dirname(local_file_path), exist_ok=True)
            
            # 下载文件
            self.s3_client.download_file(
                self.bucket_name,
                s3_key,
                local_file_path
            )
            
            logger.info(f"文件下载成功: s3://{self.bucket_name}/{s3_key} -> {local_file_path}")
            return True
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchKey':
                logger.error(f"S3中不存在文件: {s3_key}")
            else:
                logger.error(f"下载文件失败: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"下载文件时发生未知错误: {str(e)}")
            return False
    
    def download_data(self, s3_key: str) -> Optional[bytes]:
        """
        从S3下载数据到内存（返回字节数据）
        
        Args:
            s3_key: S3中的对象键（文件路径）
            
        Returns:
            Optional[bytes]: 下载的字节数据，失败时返回None
        """
        try:
            from io import BytesIO
            
            # 创建内存缓冲区
            data_stream = BytesIO()
            
            # 下载到内存
            self.s3_client.download_fileobj(
                self.bucket_name,
                s3_key,
                data_stream
            )
            
            # 获取数据
            data = data_stream.getvalue()
            
            logger.info(f"数据下载成功: s3://{self.bucket_name}/{s3_key} ({len(data)} 字节)")
            return data
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchKey':
                logger.error(f"S3中不存在文件: {s3_key}")
            else:
                logger.error(f"下载数据失败: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"下载数据时发生未知错误: {str(e)}")
            return None
    
    def download_text(self, s3_key: str, encoding: str = 'utf-8') -> Optional[str]:
        """
        从S3下载文本数据
        
        Args:
            s3_key: S3中的对象键（文件路径）
            encoding: 文本编码，默认为utf-8
            
        Returns:
            Optional[str]: 下载的文本内容，失败时返回None
        """
        try:
            # 下载字节数据
            data = self.download_data(s3_key)
            if data is None:
                return None
            
            # 解码为文本
            text = data.decode(encoding)
            
            logger.info(f"文本下载成功: s3://{self.bucket_name}/{s3_key} ({len(text)} 字符)")
            return text
            
        except UnicodeDecodeError as e:
            logger.error(f"文本解码失败: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"下载文本时发生未知错误: {str(e)}")
            return None
    
    def download_json(self, s3_key: str) -> Optional[dict]:
        """
        从S3下载JSON数据
        
        Args:
            s3_key: S3中的对象键（文件路径）
            
        Returns:
            Optional[dict]: 下载的JSON数据，失败时返回None
        """
        try:
            import json
            
            # 下载文本数据
            text = self.download_text(s3_key)
            if text is None:
                return None
            
            # 解析JSON
            data = json.loads(text)
            
            logger.info(f"JSON下载成功: s3://{self.bucket_name}/{s3_key}")
            return data
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"下载JSON时发生未知错误: {str(e)}")
            return None
    
    def delete_file(self, s3_key: str) -> bool:
        """
        从S3删除文件
        
        Args:
            s3_key: S3中的对象键（文件路径）
            
        Returns:
            bool: 删除是否成功
        """
        try:
            # 删除文件
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=s3_key
            )
            
            logger.info(f"文件删除成功: s3://{self.bucket_name}/{s3_key}")
            return True
            
        except ClientError as e:
            logger.error(f"删除文件失败: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"删除文件时发生未知错误: {str(e)}")
            return False
    
    def file_exists(self, s3_key: str) -> bool:
        """
        检查S3中文件是否存在
        
        Args:
            s3_key: S3中的对象键（文件路径）
            
        Returns:
            bool: 文件是否存在
        """
        try:
            self.s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)
            return True
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                return False
            else:
                logger.error(f"检查文件存在性失败: {str(e)}")
                raise
    
    def get_file_info(self, s3_key: str) -> Optional[Dict[str, Any]]:
        """
        获取S3文件信息
        
        Args:
            s3_key: S3中的对象键（文件路径）
            
        Returns:
            Dict: 文件信息，包含大小、修改时间等
        """
        try:
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)
            return {
                'size': response.get('ContentLength'),
                'last_modified': response.get('LastModified'),
                'content_type': response.get('ContentType'),
                'metadata': response.get('Metadata', {}),
                'etag': response.get('ETag')
            }
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                logger.warning(f"文件不存在: {s3_key}")
                return None
            else:
                logger.error(f"获取文件信息失败: {str(e)}")
                raise
    
    def list_files(self, prefix: str = "", max_keys: int = 1000) -> list:
        """
        列出S3存储桶中的文件
        
        Args:
            prefix: 文件前缀过滤
            max_keys: 最大返回文件数量
            
        Returns:
            list: 文件列表
        """
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=prefix,
                MaxKeys=max_keys
            )
            
            files = []
            if 'Contents' in response:
                for obj in response['Contents']:
                    files.append({
                        'key': obj['Key'],
                        'size': obj['Size'],
                        'last_modified': obj['LastModified'],
                        'etag': obj['ETag']
                    })
            
            return files
            
        except ClientError as e:
            logger.error(f"列出文件失败: {str(e)}")
            return []
    
    def get_download_url(self, s3_key: str, expires_in: int = 3600) -> Optional[str]:
        """
        生成文件的预签名下载URL
        
        Args:
            s3_key: S3中的对象键（文件路径）
            expires_in: URL过期时间（秒），默认1小时
            
        Returns:
            Optional[str]: 预签名下载URL，失败时返回None
        """
        try:
            # 检查文件是否存在
            if not self.file_exists(s3_key):
                logger.warning(f"文件不存在，无法生成下载URL: {s3_key}")
                return None
            
            # 生成预签名URL
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': s3_key},
                ExpiresIn=expires_in
            )
            
            logger.info(f"生成下载URL成功: s3://{self.bucket_name}/{s3_key} (过期时间: {expires_in}秒)")
            return url
            
        except ClientError as e:
            logger.error(f"生成下载URL失败: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"生成下载URL时发生未知错误: {str(e)}")
            return None
    
    def get_upload_url(self, s3_key: str, expires_in: int = 3600, content_type: Optional[str] = None) -> Optional[str]:
        """
        生成文件的预签名上传URL
        
        Args:
            s3_key: S3中的对象键（文件路径）
            expires_in: URL过期时间（秒），默认1小时
            content_type: 文件内容类型（可选）
            
        Returns:
            Optional[str]: 预签名上传URL，失败时返回None
        """
        try:
            params = {'Bucket': self.bucket_name, 'Key': s3_key}
            if content_type:
                params['ContentType'] = content_type
            
            # 生成预签名URL
            url = self.s3_client.generate_presigned_url(
                'put_object',
                Params=params,
                ExpiresIn=expires_in
            )
            
            logger.info(f"生成上传URL成功: s3://{self.bucket_name}/{s3_key} (过期时间: {expires_in}秒)")
            return url
            
        except ClientError as e:
            logger.error(f"生成上传URL失败: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"生成上传URL时发生未知错误: {str(e)}")
            return None
    
    def get_public_url(self, s3_key: str) -> str:
        """
        获取文件的公共访问URL（适用于公共存储桶）
        
        Args:
            s3_key: S3中的对象键（文件路径）
            
        Returns:
            str: 公共访问URL
        """
        if self._endpoint_url:
            # 自定义端点（如MinIO）
            return f"{self._endpoint_url.rstrip('/')}/{self.bucket_name}/{s3_key}"
        else:
            # 标准AWS S3
            region = self._region_name
            if region == 'us-east-1':
                return f"https://{self.bucket_name}.s3.amazonaws.com/{s3_key}"
            else:
                return f"https://{self.bucket_name}.s3.{region}.amazonaws.com/{s3_key}"
    
    def get_file_urls(self, s3_key: str, expires_in: int = 3600) -> Dict[str, Optional[str]]:
        """
        获取文件的所有类型URL
        
        Args:
            s3_key: S3中的对象键（文件路径）
            expires_in: 预签名URL过期时间（秒），默认1小时
            
        Returns:
            Dict[str, Optional[str]]: 包含各种类型URL的字典
        """
        return {
            'download_url': self.get_download_url(s3_key, expires_in),
            'upload_url': self.get_upload_url(s3_key, expires_in),
            'public_url': self.get_public_url(s3_key)
        }


# 使用示例和工厂函数
def create_s3_storage(
    bucket_name: str,
    aws_access_key_id: Optional[str] = None,
    aws_secret_access_key: Optional[str] = None,
    region_name: str = "us-east-1",
    endpoint_url: Optional[str] = None
) -> S3Storage:
    """
    创建S3存储实例的工厂函数
    
    Args:
        bucket_name: S3存储桶名称
        aws_access_key_id: AWS访问密钥ID
        aws_secret_access_key: AWS访问密钥
        region_name: AWS区域名称
        endpoint_url: 自定义端点URL
        
    Returns:
        S3Storage: S3存储实例
    """
    return S3Storage(
        bucket_name=bucket_name,
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key,
        region_name=region_name,
        endpoint_url=endpoint_url
    )


# 环境变量配置示例
def create_s3_storage_from_env() -> S3Storage:
    """
    从环境变量创建S3存储实例
    
    需要设置以下环境变量:
    - AWS_S3_BUCKET_NAME
    - AWS_ACCESS_KEY_ID (可选，如果使用IAM角色)
    - AWS_SECRET_ACCESS_KEY (可选，如果使用IAM角色)
    - AWS_DEFAULT_REGION (可选，默认us-east-1)
    - AWS_ENDPOINT_URL (可选，用于MinIO等兼容服务)
    
    Returns:
        S3Storage: S3存储实例
    """
    bucket_name = os.getenv('AWS_S3_BUCKET_NAME')
    if not bucket_name:
        raise ValueError("环境变量 AWS_S3_BUCKET_NAME 必须设置")
    
    return S3Storage(
        bucket_name=bucket_name,
        aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
        aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
        region_name=os.getenv('AWS_DEFAULT_REGION', 'us-east-1'),
        endpoint_url=os.getenv('AWS_ENDPOINT_URL')
    )
