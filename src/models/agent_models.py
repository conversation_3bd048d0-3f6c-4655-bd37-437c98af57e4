from typing import List, Optional
from pydantic import BaseModel, Field, field_serializer
from datetime import datetime


class Agent(BaseModel):
    agent_id: str = Field(..., description="Agent ID")
    name: str = Field(..., description="Agent name")
    type: Optional[str] = Field(None, description="Agent type")
    role: str = Field(..., description="Agent role")
    prompt: Optional[str] = Field(None, description="Agent profile/prompt")
    tools: Optional[List[str]] = Field(default_factory=list, description="List of tool")
    docs: Optional[List[str]] = Field(default_factory=list, description="List of document")
    company_id: str = Field(..., description="Company ID")
    create_time: Optional[datetime] = Field(None, description="Creation timestamp")
    update_time: Optional[datetime] = Field(None, description="Update timestamp")


class AgentListResponse(BaseModel):
    total: int = Field(..., description="Total number of agents")
    agents: List[Agent] = Field(..., description="List of agents")


class AgentCreateRequest(BaseModel):
    name: str = Field(..., description="Agent name")
    role: str = Field(..., description="Agent role")
    company_id: str = Field(..., description="Company ID")
    type: Optional[str] = Field(None, description="Agent type")
    prompt: Optional[str] = Field(None, description="Agent profile/prompt")
    tools: Optional[List[str]] = Field(default_factory=list, description="List of tool UUIDs")
    docs: Optional[List[str]] = Field(default_factory=list, description="List of document UUIDs")


class AgentUpdateRequest(BaseModel):
    agent_id: str = Field(..., description="Agent ID")
    prompt: Optional[str] = Field(None, description="Agent prompt")
    tools: Optional[List[str]] = Field(None, description="List of tool UUIDs")
    docs: Optional[List[str]] = Field(None, description="List of document UUIDs")