from typing import List, Optional, Union
import base64
import io
from datetime import datetime

import pandas as pd
from pydantic import BaseModel, Field

from src.store import s3_storage


class ContentItem(BaseModel):
    type: str = Field(..., description="The type of content (text, image, etc.)")
    text: Optional[str] = Field(None, description="The text content if type is 'text'")
    image_url: Optional[str] = Field(
        None, description="The image URL if type is 'image'"
    )


class ChatMessage(BaseModel):
    role: str = Field(
        ..., description="The role of the message sender (user or assistant)"
    )
    content: Union[str, List[ContentItem]] = Field(
        ...,
        description="The content of the message, either a string or a list of content items",
    )


class ChatRequest(BaseModel):
    messages: Optional[List[ChatMessage]] = Field(
        [], description="History of messages between the user and the assistant"
    )
    conversation_id: Optional[str] = Field(
        None, description="A specific conversation identifier"
    )
    company_id: Optional[str] = Field(
        ..., description="The company id of the request"
    )
    store_id: Optional[List] = Field(
        [], description="The store id of the request"
    )
    file_paths: Optional[List[str]] = Field(
        [], description="The file paths of the request"
    )
    enable_audio_output: Optional[bool] = Field(
        False, description="Whether to enable audio output"
    )


class ConversationItem(BaseModel):
    conversation_id: str = Field(..., description="Conversation unique identifier")
    title: Optional[str] = Field(None, description="Conversation title")
    create_time: datetime = Field(..., description="Creation time")
    update_time: datetime = Field(..., description="Last update time")
    status: str = Field(..., description="Conversation status (in_progress, completed)")
    user_id: Optional[str] = Field(None, description="User ID who created the conversation")


class ConversationListResponse(BaseModel):
    conversations: List[ConversationItem] = Field(..., description="List of conversations")


class ConversationDeleteRequest(BaseModel):
    conversation_id: str = Field(..., description="Conversation ID to delete")
    company_id: str = Field(..., description="Company ID for authorization")