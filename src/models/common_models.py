from typing import Optional, Generic, TypeVar
from pydantic import BaseModel, Field

T = TypeVar('T')


class ApiResponse(BaseModel, Generic[T]):
    """通用API响应结构"""
    code: str = Field("0", description="响应码，0表示成功")
    message: str = Field("success", description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")

    @classmethod
    def success(cls, data: T = None, message: str = "success") -> "ApiResponse[T]":
        """创建成功响应"""
        return cls(code="0", message=message, data=data)
    
    @classmethod
    def error(cls, code: str = "1", message: str = "error", data: T = None) -> "ApiResponse[T]":
        """创建错误响应"""
        return cls(code=code, message=message, data=data)