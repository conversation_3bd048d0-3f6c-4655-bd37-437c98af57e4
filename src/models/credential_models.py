from pydantic import BaseModel, Field
from typing import Optional


class CredentialCreateRequest(BaseModel):
    company_id: str = Field(..., description="Company ID")
    store_id: str = Field(..., description="Store ID")
    type: str = Field(..., description="Authentication type")
    platform: str = Field(..., description="Platform")
    params: dict = Field(..., description="Authentication parameters")
    status: Optional[str] = Field(None, description="Authentication status (auth/unauth) - optional for updates")


class CredentialResponse(BaseModel):
    store_id: str = Field(..., description="Store ID")
    type: str = Field(..., description="Authentication type")
    platform: str = Field(..., description="Platform")
    params: dict = Field(..., description="Authentication parameters")
    status: str = Field(..., description="Authentication status (auth/unauth)")