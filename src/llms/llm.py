from pathlib import Path
from typing import Any, Dict, Optional
import logging

from langchain_openai import ChatOpenAI

from src.config import get_config

logger = logging.getLogger(__name__)

# Cache for LLM instances
_llm_cache: dict[str, ChatOpenAI] = {}

# Model configurations - 统一AI网关，所有模型使用相同的API key和base_url
MODEL_CONFIGS = {
    # OpenAI models
    "gpt-5": {"model": "gpt-5"},
    "gpt-5-mini": {"model": "gpt-5-mini"},
    # Google Gemini models (通过AI网关)
    "gemini-2.5-pro": {"model": "gemini-2.5-pro"},
    "gemini-2.5-flash": {"model": "gemini-2.5-flash"},
    # Anthropic Claude models (通过AI网关)
    "claude-3.5-haiku": {"model": "claude-3-5-haiku-latest"},
    "claude-3.7-sonnet": {"model": "claude-3-7-sonnet-latest"},
    "claude-4-sonnet": {"model": "claude-sonnet-4-20250514"},
    "claude-4-opus": {"model": "claude-opus-4-20250514"},
    "claude-4.1-opus": {"model": "claude-opus-4-1-20250805"}
}


def _create_llm_instance(model_name: str, config_dict: dict) -> Optional[ChatOpenAI]:
    """创建LLM实例 - 统一AI网关，所有模型使用相同配置"""
    app_config = get_config()
    model = config_dict["model"]
    
    if not app_config.llm_api_key:
        logger.warning(f"No LLM API key configured, skipping {model_name}")
        return None
    
    kwargs = {
        "model": model,
        "api_key": app_config.llm_api_key
    }
    
    # 设置base_url（如果配置了）
    if app_config.llm_base_url:
        kwargs["base_url"] = app_config.llm_base_url
    
    try:
        return ChatOpenAI(**kwargs)
    except Exception as e:
        logger.warning(f"Failed to create {model_name}: {e}")
        return None


def initialize_models() -> None:
    """初始化所有配置的模型"""
    logger.info("Initializing LLM models...")
    
    for model_name, model_config in MODEL_CONFIGS.items():
        llm_instance = _create_llm_instance(model_name, model_config)
        if llm_instance:
            _llm_cache[model_name] = llm_instance
            logger.info(f"✅ {model_name} initialized successfully")
        else:
            logger.warning(f"❌ Failed to initialize {model_name}")
    
    logger.info(f"Initialized {len(_llm_cache)} models: {list(_llm_cache.keys())}")


def get_llm(model_name: str) -> Optional[ChatOpenAI]:
    """
    通过模型名称获取LLM实例
    
    Args:
        model_name: 模型名称
        
    Returns:
        对应的ChatOpenAI实例，如果不存在则返回None
    """
    if not _llm_cache:
        initialize_models()
    
    return _llm_cache.get(model_name)


def list_available_models() -> list[str]:
    """返回所有可用的模型名称"""
    if not _llm_cache:
        initialize_models()
    return list(_llm_cache.keys())


def get_default_model() -> Optional[ChatOpenAI]:
    """获取默认模型（优先级：gpt-5-mini > gpt-5 > 第一个可用模型）"""
    if not _llm_cache:
        initialize_models()
        
    priority_models = ["gpt-5-mini"]
    
    for model in priority_models:
        if model in _llm_cache:
            return _llm_cache[model]
    
    # 如果优先级模型都不可用，返回第一个可用模型
    if _llm_cache:
        return next(iter(_llm_cache.values()))
    
    return None

