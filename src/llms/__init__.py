"""
LLM module for OnePort Agent.

This module provides LLM initialization and model management functions.
"""
import logging
import traceback
from src.config import get_config

from .llm import initialize_models, list_available_models, get_llm, get_default_model

logger = logging.getLogger(__name__)

def init_llm():
    """初始化llm模块!"""
    try:
        config = get_config()
    except RuntimeError:
        logger.warning("配置未初始化，跳过llm模块初始化")
        return
    
    # Check if LLM is enabled
    if config.is_llm_enabled():
        try:
            # Mask the API key for logging
            masked_api_key = config.llm_api_key[:8] + "..." if config.llm_api_key else None
            base_url_info = f" with base_url: {config.llm_base_url}" if config.llm_base_url else ""
            
            logger.info(f"Initializing LLM models with API key: {masked_api_key}{base_url_info}")
            initialize_models()
            available_models = list_available_models()
            
            if available_models:
                logger.info(f"LLM models initialized successfully: {available_models}")
            else:
                logger.error("LLM models initialization completed but no models available")
                raise RuntimeError("No LLM models available")
                
        except Exception as e:
            logger.error(f"Error initializing LLM models: {str(e)}")
            # Log the full error traceback for debugging
            logger.error(traceback.format_exc())
            raise
    else:
        logger.warning("LLM模块功能未启用，跳过LLM初始化")

__all__ = [
    'init_llm',
    'initialize_models',
    'list_available_models',
    'get_llm',
    'get_llm_by_type',
    'get_default_model'
]