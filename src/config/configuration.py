import os
import logging
from dataclasses import dataclass, fields
from typing import Any, Optional, Dict
from enum import Enum

from langchain_core.runnables import RunnableConfig
from dotenv import load_dotenv




logger = logging.getLogger(__name__)


@dataclass(kw_only=True)
class Configuration:
    """统一的配置类，包含所有环境变量配置项"""

    # 基础配置
    max_search_results: int = 3
    mcp_settings: dict = None


    # 音频配置 (Dashscope)
    dashscope_api_key: Optional[str] = None
    dashscope_voice_type: str = "longhan_v2"


    # 数据存储配置
    postgres_uri: Optional[str] = None
    redis_uri: Optional[str] = None

    # S3 存储配置
    s3_ak: Optional[str] = None
    s3_sk: Optional[str] = None
    s3_endpoint: Optional[str] = None
    s3_region: Optional[str] = None
    s3_bucket_name: Optional[str] = None

    # 搜索引擎配置 (只支持Tavily)
    tavily_api_key: Optional[str] = None

    # 爬虫配置
    crawl_type: str = "jina"
    crawl_api_key: Optional[str] = None


    # LangSmith 监控配置
    langsmith_tracing: bool = True
    langsmith_endpoint: str = "https://api.smith.langchain.com"
    langsmith_api_key: Optional[str] = None
    langsmith_project: str = "oneport-agent"

    # LLM配置 - 统一AI网关，通过模型名称区分
    llm_api_key: Optional[str] = None
    llm_base_url: Optional[str] = None

    # 超时配置
    mcp_connection_timeout: int = 30
    mcp_read_timeout: int = 60
    stream_timeout: int = 300
    stream_keepalive_interval: int = 30
    agent_execution_timeout: int = 180
    agent_recursion_limit: int = 100
    tool_call_timeout: int = 120
    db_operation_timeout: int = 30

    def __post_init__(self):
        """配置验证和后处理"""
        self.validate_required_configs()
        self.validate_optional_configs()

    def validate_required_configs(self):
        """验证必需的配置项"""
        required_configs = []
        

        if required_configs:
            error_msg = f"缺少必需的配置项：{', '.join(required_configs)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg)

    def validate_optional_configs(self):
        """验证可选配置项并给出警告"""
        warnings = []

        if not self.dashscope_api_key:
            warnings.append("DASHSCOPE_API_KEY 未设置，音频功能将被禁用")


        if not self.postgres_uri:
            warnings.append("POSTGRES_URI 未设置，数据库功能将被禁用")

        if not self.redis_uri:
            warnings.append("REDIS_URI 未设置，Redis功能将被禁用")

        if not all([self.s3_ak, self.s3_sk, self.s3_endpoint, self.s3_region, self.s3_bucket_name]):
            warnings.append("S3 配置不完整，S3存储功能将被禁用")
            
        if not self.llm_api_key:
            warnings.append("LLM_API_KEY 未设置，LLM功能将被禁用")

        for warning in warnings:
            logger.warning(warning)

    @classmethod
    def load_from_env(cls) -> "Configuration":
        """从环境变量加载配置"""
        load_dotenv(override=True)
        
        return cls(
            # 基础配置
            max_search_results=int(os.getenv("MAX_SEARCH_RESULTS", "3")),
            
            
            # 音频配置
            dashscope_api_key=os.getenv("DASHSCOPE_API_KEY"),
            dashscope_voice_type=os.getenv("DASHSCOPE_VOICE_TYPE", "longhan_v2"),
            
            
            # 数据存储配置
            postgres_uri=os.getenv("POSTGRES_URI"),
            redis_uri=os.getenv("REDIS_URI"),
            
            # S3 存储配置
            s3_ak=os.getenv("S3_AK"),
            s3_sk=os.getenv("S3_SK"),
            s3_endpoint=os.getenv("S3_ENDPOINT"),
            s3_region=os.getenv("S3_REGION"),
            s3_bucket_name=os.getenv("S3_BUCKET_NAME"),
            
            # 搜索引擎配置 (只支持Tavily)
            tavily_api_key=os.getenv("TAVILY_API_KEY"),
            
            # 爬虫配置
            crawl_type=os.getenv("CRAWL_TYPE", "jina"),
            crawl_api_key=os.getenv("CRAWL_API_KEY"),
            
            
            # LangSmith 监控配置
            langsmith_tracing=os.getenv("LANGSMITH_TRACING", "true").lower() == "true",
            langsmith_endpoint=os.getenv("LANGSMITH_ENDPOINT", "https://api.smith.langchain.com"),
            langsmith_api_key=os.getenv("LANGSMITH_API_KEY"),
            langsmith_project=os.getenv("LANGSMITH_PROJECT", "oneport-agent"),
            
            # LLM配置
            llm_api_key=os.getenv("LLM_API_KEY"),
            llm_base_url=os.getenv("LLM_BASE_URL"),
            
            # 超时配置
            mcp_connection_timeout=int(os.getenv("MCP_CONNECTION_TIMEOUT", "30")),
            mcp_read_timeout=int(os.getenv("MCP_READ_TIMEOUT", "60")),
            stream_timeout=int(os.getenv("STREAM_TIMEOUT", "300")),
            stream_keepalive_interval=int(os.getenv("STREAM_KEEPALIVE_INTERVAL", "30")),
            agent_execution_timeout=int(os.getenv("AGENT_EXECUTION_TIMEOUT", "180")),
            agent_recursion_limit=int(os.getenv("AGENT_RECURSION_LIMIT", "100")),
            tool_call_timeout=int(os.getenv("TOOL_CALL_TIMEOUT", "120")),
            db_operation_timeout=int(os.getenv("DB_OPERATION_TIMEOUT", "30"))
        )

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "Configuration":
        """Create a Configuration instance from a RunnableConfig."""
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )
        values: dict[str, Any] = {
            f.name: os.environ.get(f.name.upper(), configurable.get(f.name))
            for f in fields(cls)
            if f.init
        }
        return cls(**{k: v for k, v in values.items() if v})


    def get_s3_config(self) -> Dict[str, str]:
        """获取S3配置"""
        if not all([self.s3_ak, self.s3_sk, self.s3_endpoint, self.s3_region, self.s3_bucket_name]):
            raise RuntimeError("S3配置不完整")
        return {
            "access_key": self.s3_ak,
            "secret_key": self.s3_sk,
            "endpoint": self.s3_endpoint,
            "region": self.s3_region,
            "bucket_name": self.s3_bucket_name
        }

    def is_audio_enabled(self) -> bool:
        """检查音频功能是否可用"""
        return bool(self.dashscope_api_key)


    def is_database_enabled(self) -> bool:
        """检查数据库功能是否可用"""
        return bool(self.postgres_uri)

    def is_redis_enabled(self) -> bool:
        """检查Redis功能是否可用"""
        return bool(self.redis_uri)

    def is_s3_enabled(self) -> bool:
        """检查S3功能是否可用"""
        return all([self.s3_ak, self.s3_sk, self.s3_endpoint, self.s3_region, self.s3_bucket_name])
    
    def is_llm_enabled(self) -> bool:
        """检查LLM功能是否可用"""
        return bool(self.llm_api_key)
