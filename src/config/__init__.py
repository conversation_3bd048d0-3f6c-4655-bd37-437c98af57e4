from .configuration import Configuration
from .loader import load_yaml_config

import logging

logger = logging.getLogger(__name__)

# 全局配置实例
config: Configuration = None

def init_config() -> Configuration:
    """初始化配置，应在服务启动时调用"""
    global config
    try:
        config = Configuration.load_from_env()
        logger.info("配置加载成功")
        return config
    except Exception as e:
        logger.error(f"配置加载失败: {e}")
        raise

def get_config() -> Configuration:
    """获取全局配置实例"""
    global config
    if config is None:
        raise RuntimeError("配置未初始化，请先调用 init_config()")
    return config

# Team configuration
TEAM_MEMBER_CONFIGURATIONS = {
    "researcher": {
        "name": "researcher",
        "desc": (
            "Responsible for searching and collecting relevant information, understanding user needs and conducting research analysis"
        ),
        "desc_for_llm": (
            "Uses search engines and web crawlers to gather information from the internet. "
            "Outputs a Markdown report summarizing findings. Researcher can not do math or programming."
        ),
        "is_optional": False,
    },
    "coder": {
        "name": "coder",
        "desc": (
            "Responsible for code implementation, debugging and optimization, handling technical programming tasks"
        ),
        "desc_for_llm": (
            "Executes Python or Bash commands, performs mathematical calculations, and outputs a Markdown report. "
            "Must be used for all mathematical computations."
        ),
        "is_optional": True,
    },
}

# 兼容旧名称（避免外部依赖破坏）
TEAM_MEMBER_CONFIGRATIONS = TEAM_MEMBER_CONFIGURATIONS
TEAM_MEMBERS = list(TEAM_MEMBER_CONFIGURATIONS.keys())


__all__ = [
    "Configuration",
    "init_config", 
    "get_config",
    "TEAM_MEMBERS",
    "TEAM_MEMBER_CONFIGURATIONS",
    "load_yaml_config",
]
