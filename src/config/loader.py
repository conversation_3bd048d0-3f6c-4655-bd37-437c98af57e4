import os
import io
import yaml
from typing import Dict, Any

def replace_env_vars(value: str) -> str:
    """Replace environment variables in string values."""
    if not isinstance(value, str):
        return value
    if value.startswith("$"):
        env_var = value[1:]
        return os.getenv(env_var, value)
    return value


def process_dict(config: Dict[str, Any]) -> Dict[str, Any]:
    """Recursively process dictionary to replace environment variables."""
    result = {}
    for key, value in config.items():
        if isinstance(value, dict):
            result[key] = process_dict(value)
        elif isinstance(value, str):
            result[key] = replace_env_vars(value)
        else:
            result[key] = value
    return result


_config_cache: Dict[str, Dict[str, Any]] = {}


def load_yaml_config(file_path: str) -> Dict[str, Any]:
    """
    加载配置：
    - 仅支持从本地 YAML 文件加载
    加载后执行环境变量占位符替换（仅字符串，形如 $VAR），并做结果缓存
    """
    # 计算缓存键
    if not file_path:
        return {}
        
    abs_path = os.path.abspath(file_path)
    cache_key = f"file:{abs_path}"

    # 命中缓存
    if cache_key in _config_cache:
        return _config_cache[cache_key]

    # 检查文件是否存在
    if not os.path.exists(file_path):
        _config_cache[cache_key] = {}
        return {}

    # 读取文件内容
    try:
        with open(file_path, "r") as f:
            config_data = f.read()
    except Exception:
        _config_cache[cache_key] = {}
        return {}

    # 空内容处理
    if not config_data:
        _config_cache[cache_key] = {}
        return {}

    # 解析 YAML
    try:
        loaded = yaml.safe_load(io.StringIO(config_data)) or {}
    except Exception:
        _config_cache[cache_key] = {}
        return {}

    # 确保返回字典类型，再递归进行环境变量替换
    if not isinstance(loaded, dict):
        loaded = {}
    processed = process_dict(loaded)

    # 写入缓存并返回
    _config_cache[cache_key] = processed
    return processed
