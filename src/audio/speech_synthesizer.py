import dashscope
from dashscope import Generation
from dashscope.audio.tts_v2 import *
import os
from src.config import get_config
import asyncio
from src.store import s3_storage

def _get_dashscope_config():
    """获取Dashscope配置"""
    config = get_config()
    if not config.is_audio_enabled():
        raise RuntimeError("音频功能未启用，请检查DASHSCOPE_API_KEY配置")
    
    dashscope.api_key = config.dashscope_api_key
    return config


class Callback(ResultCallback):
    def __init__(self, message_id: str):
        self.message_id = message_id
        # 创建音频文件目录
        self.audio_dir = "workspace/audio_file"
        os.makedirs(self.audio_dir, exist_ok=True)
        # 音频文件路径
        self.audio_file_path = os.path.join(self.audio_dir, f"{message_id}.mp3")
        # 打开文件用于写入音频数据
        self.audio_file = open(self.audio_file_path, "wb")

    def on_open(self):
        pass

    def _cleanup_file(self):
        """关闭并删除临时音频文件"""
        if hasattr(self, 'audio_file') and self.audio_file:
            self.audio_file.close()
            self.audio_file = None
        
        # 删除文件
        if hasattr(self, 'audio_file_path') and os.path.exists(self.audio_file_path):
            try:
                os.remove(self.audio_file_path)
                print(f'已删除临时音频文件: {self.audio_file_path}')
            except OSError as e:
                print(f'删除音频文件失败: {e}')

    def on_complete(self):
        print(f'音频合成完成，文件路径: {self.audio_file_path}')
        s3_storage.upload_file(self.audio_file_path, f"audio_file/{self.message_id}.mp3")
        # 关闭并删除文件
        self._cleanup_file()

    def on_error(self, message: str):
        print(f'speech synthesis task failed, {message}')
        # 发生错误时关闭并删除文件
        self._cleanup_file()

    def on_close(self):
        # 确保文件被关闭并删除
        self._cleanup_file()

    def on_event(self, message):
        pass

    def on_data(self, data: bytes) -> None:
        # 将音频数据写入文件
        if hasattr(self, 'audio_file') and self.audio_file:
            self.audio_file.write(data)


class StreamingSynthesizer:
    def __init__(self, message_id: str):
        config = _get_dashscope_config()
        self.callback = Callback(message_id)
        self.synthesizer = SpeechSynthesizer(
            model='cosyvoice-v2',
            voice=config.dashscope_voice_type,
            callback=self.callback,
        )

    def streaming_call(self, text: str):
        self.synthesizer.streaming_call(text)

    def streaming_complete(self):
        self.synthesizer.streaming_complete()

    def streaming_cancel(self):
        self.synthesizer.streaming_cancel()

    def async_streaming_complete(self):
        self.synthesizer.async_streaming_complete()