from langchain_core.tools import tool, InjectedToolCallId
from langgraph.types import Command
from langchain_core.messages import ToolMessage
from typing import Annotated
from src.graph.state import Todo
from ..decorators import log_io
from .prompt import WRITE_TODOS_DESCRIPTION

@tool(description=WRITE_TODOS_DESCRIPTION)
@log_io
def write_todos(
    todos: Annotated[list[Todo], "The updated todo list"], 
    tool_call_id: Annotated[str, InjectedToolCallId]
) -> Command:
    return Command(
        update={
            "todos": todos,
            "messages": [
                ToolMessage(f"Updated todo list to {todos}", tool_call_id=tool_call_id)
            ],
        }
    )