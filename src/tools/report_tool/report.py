from langchain_core.tools import tool, InjectedToolCallId
from langgraph.types import Command
from langchain_core.messages import ToolMessage
from typing import Annotated
from src.graph.state import Todo
from ..decorators import log_io
from .prompt import REPORT_DESCRIPTION

@tool(description=REPORT_DESCRIPTION)
@log_io
def generate_report(
    report: Annotated[str, "report content"], 
    tool_call_id: Annotated[str, InjectedToolCallId]
) -> Command:
    return Command(
        update={
            "report": report,
            "messages": [
                ToolMessage(f"generate report {report}", tool_call_id=tool_call_id)
            ],
        }
    )