#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书SKU基础信息数据获取工具

从指定的飞书多维表格中读取SKU基础信息数据，包括最新SKU、PM、公司SKU下的产品名、Spotter后台产品名等字段，
并将数据存储到PostgreSQL数据库中。
"""

from __future__ import annotations

import json
import logging
import os
import sys
import requests
import re
from datetime import datetime
from typing import Annotated, Any, Dict, List, Optional

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 导入psycopg（用于数据库操作）
try:
    import psycopg
except ImportError:
    psycopg = None

# 导入langchain工具（用于工具装饰器）
try:
    from langchain_core.tools import tool
    from src.tools.decorators import log_io
except ImportError:
    # 如果作为独立脚本运行，创建模拟装饰器
    def tool(description):
        def decorator(func):
            return func
        return decorator
    
    def log_io(func):
        return func

logger = logging.getLogger(__name__)

# 飞书API配置
FEISHU_BASE_URL = "https://open.feishu.cn"

# SKU基础信息表格配置
SKU_BASIC_INFO_TABLE_CONFIG = {
    "app_token": "VEGnbUM8KaH2a0sv7YGcc8IHnJc",
    "table_id": "tbltsEKo4KMTsrHq", 
    "view_id": "vewDo5yOp2",
    "description": "SKU基础信息表格"
}


class FeishuAPIError(Exception):
    """飞书API异常"""
    pass


def get_tenant_access_token() -> str:
    """获取飞书租户访问令牌"""
    # 从环境变量读取飞书应用凭据
    app_id = os.getenv("FEISHU_APP_ID")
    app_secret = os.getenv("FEISHU_APP_SECRET")
    
    if not app_id or not app_secret:
        raise FeishuAPIError("请设置 FEISHU_APP_ID 和 FEISHU_APP_SECRET 环境变量")
    
    url = f"{FEISHU_BASE_URL}/open-apis/auth/v3/tenant_access_token/internal"
    data = {
        "app_id": app_id,
        "app_secret": app_secret
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        response.raise_for_status()
        result = response.json()
        
        if result.get('code') != 0:
            raise FeishuAPIError(f"获取访问令牌失败: {result.get('msg', '未知错误')}")
        
        # 处理两种可能的响应格式
        if 'data' in result and 'tenant_access_token' in result['data']:
            return result['data']['tenant_access_token']
        elif 'tenant_access_token' in result:
            return result['tenant_access_token']
        else:
            raise FeishuAPIError(f"API响应格式错误，无法找到tenant_access_token: {result}")
            
    except requests.exceptions.RequestException as e:
        raise FeishuAPIError(f"请求访问令牌失败: {str(e)}")
    except Exception as e:
        raise FeishuAPIError(f"获取访问令牌失败: {str(e)}")


def extract_field_value(field_value: Any) -> str:
    """提取字段值，处理各种数据类型"""
    if field_value is None:
        return ""
    
    if isinstance(field_value, str):
        return field_value.strip()
    
    if isinstance(field_value, list):
        # 如果是列表，取第一个元素的name字段，或者直接连接
        if field_value and isinstance(field_value[0], dict) and "name" in field_value[0]:
            return field_value[0]["name"]
        return ", ".join(str(item) for item in field_value)
    
    if isinstance(field_value, dict):
        # 如果是字典，尝试获取name字段
        return field_value.get("name", str(field_value))
    
    return str(field_value)


def extract_pm_name(pm_field: Any) -> str:
    """专门提取PM字段中的name值"""
    if pm_field is None:
        return ""
    
    if isinstance(pm_field, list) and pm_field:
        # 如果是列表且不为空，取第一个元素的name字段
        first_pm = pm_field[0]
        if isinstance(first_pm, dict) and "name" in first_pm:
            return first_pm["name"]
        return str(first_pm)
    
    if isinstance(pm_field, dict) and "name" in pm_field:
        # 如果是字典且包含name字段
        return pm_field["name"]
    
    return str(pm_field)


def is_valid_sku(sku: str) -> bool:
    """验证SKU格式是否有效"""
    if not sku:
        return False
    # SKU不能为空且长度合理
    return len(sku.strip()) > 0 and len(sku.strip()) <= 100


def get_bitable_records_list(
    tenant_token: str,
    app_token: str,
    table_id: str,
    view_id: str,
    page_size: int = 100,
    max_pages: int = 30
) -> List[Dict[str, Any]]:
    """获取多维表格记录列表"""
    all_items = []
    page_token = None
    
    logger.info(f"开始获取飞书SKU基础信息表格数据...")
    
    for page_num in range(max_pages):
        url = f"{FEISHU_BASE_URL}/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records"
        params = {
            'view_id': view_id,
            'page_size': page_size
        }
        if page_token:
            params['page_token'] = page_token
        
        headers = {
            'Authorization': f'Bearer {tenant_token}'
        }
        
        try:
            logger.info(f"正在获取第 {page_num + 1} 页数据...")
            
            response = requests.get(url, params=params, headers=headers, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('code') != 0:
                raise FeishuAPIError(f"获取记录失败: {result.get('msg', '未知错误')}")
            
            data = result.get('data', {})
            items = data.get('items', [])
            logger.info(f"第 {page_num + 1} 页获取到 {len(items)} 条记录")
            
            all_items.extend(items)
            
            # 检查是否还有更多数据
            if not data.get('has_more', False):
                logger.info("没有更多数据，结束分页")
                break
                
            page_token = data.get('page_token')
            if not page_token:
                logger.info("没有下一页token，结束分页")
                break
                
        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求失败: {str(e)}")
            raise FeishuAPIError(f"网络请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"解析响应失败: {str(e)}")
            raise FeishuAPIError(f"解析响应失败: {str(e)}")
    
    logger.info(f"总共获取到 {len(all_items)} 条记录")
    return all_items


@tool(description="获取飞书多维表格中的SKU基础信息数据。读取最新SKU、PM、公司SKU下的产品名、Spotter后台产品名字段，并存储到数据库。")
@log_io
def feishu_sku_basic_info(
    company_id: Annotated[str, "企业ID"],
    store_id: Annotated[str, "店铺ID"],
) -> str:
    """
    获取飞书SKU基础信息数据并存储到数据库
    
    Args:
        company_id: 企业ID
        store_id: 店铺ID
        
    Returns:
        JSON格式的SKU基础信息数据
    """
    try:
        # 获取表格配置
        config = SKU_BASIC_INFO_TABLE_CONFIG
        logger.info(f"使用配置读取SKU基础信息: {config['description']}")
        
        # 获取访问令牌
        tenant_token = get_tenant_access_token()
        
        # 获取原始数据
        actual_page_size = 100 # 默认页面大小
        actual_max_pages = 30 # 默认最大页数
            
        items = get_bitable_records_list(
            tenant_token, 
            config["app_token"], 
            config["table_id"], 
            config["view_id"], 
            actual_page_size, 
            actual_max_pages
        )
        
        logger.info(f"原始数据记录数: {len(items)}")
        
        # 解析和结构化数据
        parsed_items = []
        valid_sku_count = 0
        
        for item in items:
            fields = item.get("fields", {})
            
            # 提取SKU字段
            sku = extract_field_value(fields.get("最新SKU", ""))
            
            # 如果SKU无效，跳过
            if not is_valid_sku(sku):
                continue
            
            if is_valid_sku(sku):
                valid_sku_count += 1
            
            # 提取其他关键字段
            parsed_item = {
                "record_id": item.get("record_id", ""),
                "sku": sku,
                "product_name_chinese": extract_field_value(fields.get("公司SKU下的产品名", "")),
                "product_nickname_chinese": extract_field_value(fields.get("Spotter后台产品名", "")),
                "product_manager": extract_pm_name(fields.get("PM", "")),
            }
            
            parsed_items.append(parsed_item)
        
        logger.info(f"解析完成，有效记录数: {len(parsed_items)}")
        logger.info(f"有效SKU数: {valid_sku_count}")
        
        # 存储到数据库
        stored_count = 0
        if parsed_items:
            try:
                stored_count = store_sku_basic_info_to_database(
                    parsed_items, company_id, store_id
                )
                logger.info(f"成功存储 {stored_count} 条SKU基础信息记录到数据库")
            except Exception as e:
                logger.error(f"数据库存储失败: {e}")
                stored_count = 0
        
        # 构建返回数据
        result = {
            "success": True,
            "table_type": "sku_basic_info",
            "table_url": f"https://oxuiah4gj3.feishu.cn/base/{config['app_token']}?table={config['table_id']}&view={config['view_id']}",
            "summary": {
                "total_records": len(parsed_items),
                "valid_sku_count": valid_sku_count,
                "database_stored": stored_count
            },
            "execution_time": datetime.now().isoformat()
        }
        
        logger.info(f"成功获取SKU基础信息: {len(parsed_items)}条记录，{valid_sku_count}个有效SKU，存储{stored_count}条到数据库")
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        logger.exception(f"获取飞书SKU基础信息失败: {str(e)}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "table_url": f"https://oxuiah4gj3.feishu.cn/base/{SKU_BASIC_INFO_TABLE_CONFIG['app_token']}?table={SKU_BASIC_INFO_TABLE_CONFIG['table_id']}&view={SKU_BASIC_INFO_TABLE_CONFIG['view_id']}"
        }, ensure_ascii=False)


def store_sku_basic_info_to_database(
    sku_data: List[Dict], 
    company_id: str, 
    store_id: str
) -> int:
    """
    将SKU基础信息数据存储到数据库
    
    Args:
        sku_data: SKU数据列表
        company_id: 公司ID
        store_id: 店铺ID
        
    Returns:
        存储的记录数
    """
    if not sku_data:
        return 0
    
    postgres_uri = os.getenv("POSTGRES_URI")
    if not postgres_uri:
        raise ValueError("POSTGRES_URI 环境变量未设置")
    
    logger.info(f"开始处理 {len(sku_data)} 条SKU基础信息数据...")
    
    # 存储到数据库
    try:
        with psycopg.connect(postgres_uri) as conn:
            with conn.cursor() as cur:
                stored_count = 0
                batch_size = 50  # 每批插入50条记录
                batch_data = []
                
                for item in sku_data:
                    sku = item.get("sku", "")
                    
                    # 只检查sku是否为空
                    if not sku:
                        continue
                    
                    # 构建主键：company_id-store_id-sku
                    company_id_store_id_sku = f"{company_id}-{store_id}-{sku}"
                    
                    # 构建数据值
                    values = [
                        company_id_store_id_sku,  # 主键
                        company_id,                # company_id
                        store_id,                  # store_id
                        sku,                      # sku
                        item.get("product_name_chinese", ""),      # product_name_Chinese
                        item.get("product_nickname_chinese", ""),  # product_nickname_Chinese
                        item.get("product_manager", ""),          # product_manager
                        datetime.now()                           # data_load_time
                    ]
                    
                    batch_data.append(values)
                    
                    # 批量插入
                    if len(batch_data) >= batch_size:
                        placeholders = ', '.join(['%s'] * len(values))
                        cur.executemany(f"""
                            INSERT INTO amazon_data.sku_basic_info_table (
                                company_id_store_id_sku, company_id, store_id, sku,
                                product_name_chinese, product_nickname_chinese, product_manager, data_load_time
                            ) VALUES ({placeholders})
                            ON CONFLICT (company_id_store_id_sku) DO UPDATE SET
                                product_name_chinese = EXCLUDED.product_name_chinese,
                                product_nickname_chinese = EXCLUDED.product_nickname_chinese,
                                product_manager = EXCLUDED.product_manager,
                                data_load_time = NOW()
                        """, batch_data)
                        
                        stored_count += len(batch_data)
                        batch_data = []
                        logger.info(f"已存储 {stored_count} 条记录...")
                
                # 插入剩余的数据
                if batch_data:
                    placeholders = ', '.join(['%s'] * len(batch_data[0]))
                    cur.executemany(f"""
                        INSERT INTO amazon_data.sku_basic_info_table (
                            company_id_store_id_sku, company_id, store_id, sku,
                            product_name_chinese, product_nickname_chinese, product_manager, data_load_time
                        ) VALUES ({placeholders})
                        ON CONFLICT (company_id_store_id_sku) DO UPDATE SET
                            product_name_chinese = EXCLUDED.product_name_chinese,
                            product_nickname_chinese = EXCLUDED.product_nickname_chinese,
                            product_manager = EXCLUDED.product_manager,
                            data_load_time = NOW()
                    """, batch_data)
                    
                    stored_count += len(batch_data)
                
                conn.commit()
                logger.info(f"成功存储 {stored_count} 条SKU基础信息记录到数据库")
                return stored_count
                
    except Exception as e:
        logger.error(f"数据库存储失败: {e}")
        raise e


def main():
    """主函数，用于测试工具"""
    print("🚀 Feishu SKU Basic Info 测试工具")
    print("=" * 60)
    print("选择测试模式:")
    print("1. 读取飞书数据并存储到数据库")
    print("2. 只读取飞书数据（不存储）")
    print()
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            print("🧪 开始读取飞书数据并存储到数据库...")
            test_with_database()
        elif choice == "2":
            print("🧪 开始只读取飞书数据...")
            test_read_only()
        else:
            print("❌ 无效选择，请输入 1 或 2")
            
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def test_with_database():
    """测试读取飞书数据并存储到数据库"""
    try:
        # 测试参数
        company_id = "test_company"
        store_id = "test_store"
        
        # 使用invoke方法调用工具
        result = feishu_sku_basic_info.invoke({
            "company_id": company_id,
            "store_id": store_id,
        })
        
        data = json.loads(result)
        
        if data.get("success"):
            print("✅ 成功读取飞书数据并存储到数据库")
            summary = data.get('summary', {})
            print(f"📊 总记录数: {summary.get('total_records', 0)}")
            print(f"💾 数据库存储: {summary.get('database_stored', 0)}")
            print(f"🔍 有效SKU数: {summary.get('valid_sku_count', 0)}")
        else:
            print(f"❌ 失败: {data.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def test_read_only():
    """测试只读取飞书数据（不存储到数据库）"""
    try:
        # 测试参数
        company_id = "test_company"
        store_id = "test_store"
        
        # 使用invoke方法调用工具
        result = feishu_sku_basic_info.invoke({
            "company_id": company_id,
            "store_id": store_id,
        })
        
        data = json.loads(result)
        
        if data.get("success"):
            print("✅ 成功读取飞书数据")
            summary = data.get('summary', {})
            print(f"📊 总记录数: {summary.get('total_records', 0)}")
            print(f"🔍 有效SKU数: {summary.get('valid_sku_count', 0)}")
        else:
            print(f"❌ 失败: {data.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    main()
