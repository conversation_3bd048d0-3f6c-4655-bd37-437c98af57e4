#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Feishu Ordered Not Shipped Table Tool

独立的飞书已下单未发货表工具，从数据获取到解析的完整流程。
"""

import os
import json
import logging
import requests
import re
from decimal import Decimal
from typing import Optional, List, Dict, Any, Annotated
from urllib.parse import urlencode
from langchain.tools import tool
from ..decorators import log_io
from datetime import datetime

logger = logging.getLogger(__name__)

# 飞书API基础URL
FEISHU_BASE_URL = "https://open.feishu.cn"

class FeishuAPIError(Exception):
    """飞书API异常"""
    pass

# 已下单未发货表配置
ORDERED_NOT_SHIPPED_TABLE_CONFIG = {
    "app_token": os.getenv('FEISHU_ORDERED_NOT_SHIPPED_APP_TOKEN', 'VEGnbUM8KaH2a0sv7YGcc8IHnJc'),
    "table_id": os.getenv('FEISHU_ORDERED_NOT_SHIPPED_TABLE_ID', 'tblx07vC1UBGxuoG'),
    "view_id": os.getenv('FEISHU_ORDERED_NOT_SHIPPED_VIEW_ID', 'vewqQCcLoJ'),
    "description": "已下单未发货表"
}

# 已下单未发货表字段映射
ORDERED_NOT_SHIPPED_COLUMN_MAPPING = {
    "asin": ["ASIN"],
    "ordered_waiting_for_ship": ["已下单未发货-AMZ"],
}


def get_tenant_access_token() -> str:
    """获取飞书租户访问令牌"""
    app_id = os.getenv('FEISHU_APP_ID')
    app_secret = os.getenv('FEISHU_APP_SECRET')
    
    if not app_id or not app_secret:
        raise FeishuAPIError("请设置 FEISHU_APP_ID 和 FEISHU_APP_SECRET 环境变量")
    
    url = f"{FEISHU_BASE_URL}/open-apis/auth/v3/tenant_access_token/internal"
    data = {
        "app_id": app_id,
        "app_secret": app_secret
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        response.raise_for_status()
        result = response.json()
        if result.get('code') != 0:
            raise FeishuAPIError(f"获取访问令牌失败: {result.get('msg', '未知错误')}")
        return result['data']['tenant_access_token']
    except requests.exceptions.RequestException as e:
        raise FeishuAPIError(f"网络请求失败: {str(e)}")
    except json.JSONDecodeError as e:
        raise FeishuAPIError(f"解析响应失败: {str(e)}")


def get_bitable_records_list(
    tenant_token: str,
    app_token: str,
    table_id: str,
    view_id: str,
    page_size: int = 100,
    max_pages: int = 10
) -> List[Dict[str, Any]]:
    """获取多维表格记录列表"""
    all_items = []
    page_token = None
    
    for page in range(max_pages):
        url = f"{FEISHU_BASE_URL}/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records"
        params = {
            'view_id': view_id,
            'page_size': page_size
        }
        if page_token:
            params['page_token'] = page_token
            
        headers = {
            'Authorization': f'Bearer {tenant_token}'
        }
        
        try:
            response = requests.get(url, params=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()
            
            if result.get('code') != 0:
                raise FeishuAPIError(f"获取记录失败: {result.get('msg', '未知错误')}")
            
            data = result.get('data', {})
            items = data.get('items', [])
            all_items.extend(items)
            
            # 检查是否还有更多数据
            if not data.get('has_more', False):
                break
                
            page_token = data.get('page_token')
            if not page_token:
                break
                
        except requests.exceptions.RequestException as e:
            raise FeishuAPIError(f"网络请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            raise FeishuAPIError(f"解析响应失败: {str(e)}")
    
    return all_items


def extract_field_value(field_data: Any) -> str:
    """提取字段值"""
    if isinstance(field_data, list) and len(field_data) > 0:
        return field_data[0].get("text", "")
    elif isinstance(field_data, dict):
        return field_data.get("text", "")
    else:
        return str(field_data) if field_data is not None else ""


@tool(description="获取飞书已下单未发货表数据。支持按ASIN等条件搜索，自动解析和结构化数据。返回订单信息，包含ASIN、已下单未发货数量等关键字段。")
@log_io
def feishu_ordered_not_shipped_table(
    search_keyword: Annotated[Optional[str], "搜索关键词：ASIN等"] = None,
    page_size: Annotated[Optional[int], "每页记录数，默认100，最大500"] = 100,
    max_pages: Annotated[Optional[int], "最大页数，默认10页"] = 10,
) -> str:
    """
    获取飞书已下单未发货表数据
    
    Args:
        search_keyword: 搜索关键词
        page_size: 每页记录数
        max_pages: 最大页数
        
    Returns:
        JSON格式的已下单未发货表数据
    """
    try:
        # 获取表格配置
        config = ORDERED_NOT_SHIPPED_TABLE_CONFIG
        if not config["app_token"] or not config["table_id"]:
            return json.dumps({
                "success": False,
                "error": "已下单未发货表未配置，请在环境变量中设置 FEISHU_ORDERED_NOT_SHIPPED_APP_TOKEN 和 FEISHU_ORDERED_NOT_SHIPPED_TABLE_ID"
            }, ensure_ascii=False)
        
        # 获取访问令牌
        tenant_token = get_tenant_access_token()
        
        # 获取原始数据
        if search_keyword:
            actual_page_size = max(page_size, 500)  # 至少500条
            actual_max_pages = max(max_pages, 5)     # 至少5页
        else:
            actual_page_size = page_size
            actual_max_pages = max_pages
            
        items = get_bitable_records_list(
            tenant_token, 
            config["app_token"], 
            config["table_id"], 
            config["view_id"], 
            actual_page_size, 
            actual_max_pages
        )
        
        # 如果有搜索关键词，进行筛选
        if search_keyword:
            kw = search_keyword.strip()
            filtered_items = []
            
            for item in items:
                fields = item.get("fields", {})
                matched = False
                
                # ASIN 匹配
                m_asin = re.search(r'B0[A-Z0-9]{8}', kw.upper())
                if m_asin:
                    asin_value = m_asin.group(0)
                    if "ASIN" in fields:
                        field_value = fields["ASIN"]
                        field_text = extract_field_value(field_value)
                        if asin_value.upper() in field_text.upper():
                            matched = True
                
                if matched:
                    filtered_items.append(item)
            
            items = filtered_items
        
        # 解析和结构化数据
        parsed_items = []
        total_ordered_waiting = 0
        
        for item in items:
            fields = item.get("fields", {})
            
            # 提取关键字段
            asin = extract_field_value(fields.get("ASIN", ""))
            ordered_waiting_str = extract_field_value(fields.get("已下单未发货-AMZ", "0"))
            
            # 解析数量
            try:
                ordered_waiting = int(ordered_waiting_str) if ordered_waiting_str.isdigit() else 0
            except (ValueError, TypeError):
                ordered_waiting = 0
            
            total_ordered_waiting += ordered_waiting
            
            parsed_item = {
                "record_id": item.get("record_id", ""),
                "asin": asin,
                "ordered_waiting_for_ship": ordered_waiting,
                "raw_fields": fields  # 保留原始字段数据
            }
            
            parsed_items.append(parsed_item)
        
        # 统计信息
        total_records = len(parsed_items)
        unique_asins = len(set(item["asin"] for item in parsed_items if item["asin"]))
        asins_with_orders = len([item for item in parsed_items if item["ordered_waiting_for_ship"] > 0])
        
        # 按ASIN分组统计
        asin_stats = {}
        for item in parsed_items:
            asin = item["asin"]
            if asin:
                if asin not in asin_stats:
                    asin_stats[asin] = {"total_waiting": 0, "records": 0}
                asin_stats[asin]["total_waiting"] += item["ordered_waiting_for_ship"]
                asin_stats[asin]["records"] += 1
        
        # 转换为列表格式并按等待数量排序
        asin_summary = [
            {
                "asin": asin,
                "total_waiting_for_ship": stats["total_waiting"],
                "records_count": stats["records"]
            }
            for asin, stats in asin_stats.items()
        ]
        asin_summary.sort(key=lambda x: x["total_waiting_for_ship"], reverse=True)
        
        # 构建返回数据
        result = {
            "success": True,
            "table_type": "ordered_not_shipped",
            "search_keyword": search_keyword,
            "summary": {
                "total_records": total_records,
                "unique_asins": unique_asins,
                "asins_with_orders": asins_with_orders,
                "total_ordered_waiting_for_ship": total_ordered_waiting,
                "avg_waiting_per_asin": total_ordered_waiting / unique_asins if unique_asins > 0 else 0
            },
            "top_waiting_asins": asin_summary[:10],  # 前10个等待数量最多的ASIN
            "data": parsed_items[:100],  # 限制返回前100条记录
            "execution_time": datetime.now().isoformat()
        }
        
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        logger.exception(f"获取飞书已下单未发货表失败: {str(e)}")
        return json.dumps({
            "success": False,
            "error": str(e)
        }, ensure_ascii=False)
