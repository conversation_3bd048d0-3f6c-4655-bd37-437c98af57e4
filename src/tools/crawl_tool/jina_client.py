import logging
import os
import requests
from dotenv import load_dotenv

logger = logging.getLogger(__name__)
load_dotenv(override=True)

class JinaClient:
    def crawl(self, url: str, return_format: str = "html") -> str:
        headers = {"X-Return-Format": return_format}
        
        api_key = os.getenv("JINA_API_KEY")
        if api_key:
            headers["Authorization"] = f"Bearer {api_key}"
        
        response = requests.post("https://r.jina.ai/", headers=headers, json={"url": url})
        response.raise_for_status()
        return response.text
