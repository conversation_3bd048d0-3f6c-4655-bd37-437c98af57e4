import logging
from typing import Annotated

from langchain_core.tools import tool
from ..decorators import log_io
from ...config.configuration import Configuration

from . import Crawler, CrawlerEngine

logger = logging.getLogger(__name__)


@tool
@log_io
def crawl_tool(
    url: Annotated[str, "The url to crawl."],
) -> str:
    """Use this to crawl a url and get a readable content in markdown format."""
    config = Configuration.load_from_env()
    
    if config.crawl_type == "firecrawl":
        engine = CrawlerEngine.FIRECRAWL
        crawler = Crawler(engine=engine, api_key=config.crawl_api_key)
    else:
        engine = CrawlerEngine.JINA
        crawler = Crawler(engine=engine)
    
    article = crawler.crawl(url)
    return article.to_markdown()
