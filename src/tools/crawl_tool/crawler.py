from enum import Enum
from .article import Article
from .jina_client import JinaClient
from .firecrawl_client import FirecrawlClient
from .readability_extractor import ReadabilityExtractor


class CrawlerEngine(Enum):
    JINA = "jina"
    FIRECRAWL = "firecrawl"


class Crawler:
    def __init__(self, engine: CrawlerEngine = CrawlerEngine.JINA, api_key: str = None):
        if engine == CrawlerEngine.JINA:
            self._client = JinaClient()
        elif engine == CrawlerEngine.FIRECRAWL:
            if not api_key:
                raise ValueError("api_key is required for Firecrawl engine")
            self._client = FirecrawlClient(api_key)
        else:
            raise ValueError(f"Unsupported engine: {engine}")
        
        self._extractor = ReadabilityExtractor()
    
    def crawl(self, url: str) -> Article:
        html = self._client.crawl(url, return_format="markdown")
        article = self._extractor.extract_article(html)
        article.url = url
        return article
