import logging
from firecrawl import FirecrawlApp

logger = logging.getLogger(__name__)


class FirecrawlClient:
    def __init__(self, api_key: str):
        if not api_key:
            raise ValueError("api_key is required. Get key from https://firecrawl.dev")
        self.app = FirecrawlApp(api_key=api_key)
    
    def crawl(self, url: str, return_format: str = "markdown") -> str:
        formats = [return_format]
        result = self.app.scrape_url(url, formats=formats)
        
        if return_format == "markdown":
            return result.markdown
        return result.html 