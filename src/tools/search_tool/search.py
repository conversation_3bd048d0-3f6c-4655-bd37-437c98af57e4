import logging
from typing import Annotated

from langchain_core.tools import tool
from ..decorators import log_io
from ...config.configuration import Configuration

from .tavily_search.tavily_search_results_with_images import (
    TavilySearchResultsWithImages,
)
from .prompt import WEB_SEARCH_DESCRIPTION

logger = logging.getLogger(__name__)


@tool(description=WEB_SEARCH_DESCRIPTION)
@log_io
def web_search(
    query: Annotated[str, "The search query to execute"],
    max_results: Annotated[int, "Maximum number of search results to return"] = 5,
) -> str:
    """Search the web for information using Tavily search with images and raw content."""
    config = Configuration.load_from_env()
    
    # Create Tavily search tool with specified parameters
    search_tool = TavilySearchResultsWithImages(
        name="web_tavily_search",
        max_results=max_results,
        include_raw_content=True,
        include_images=True,
        include_image_descriptions=True,
        api_key=config.tavily_api_key,
    )
    
    try:
        # Execute the search
        results = search_tool.invoke({"query": query})
        return results
    except Exception as e:
        logger.error(f"Web search failed for query '{query}': {str(e)}")
        return f"Error performing web search: {str(e)}"


def get_web_search_tool(max_search_results: int):
    """Get Tavily web search tool with specified max results - legacy function for backward compatibility"""
    from ..decorators import create_logged_tool
    LoggedTavilySearch = create_logged_tool(TavilySearchResultsWithImages)
    
    return LoggedTavilySearch(
        name="web_tavily_search",
        max_results=max_search_results,
        include_raw_content=True,
        include_images=True,
        include_image_descriptions=True,
    )