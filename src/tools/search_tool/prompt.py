WEB_SEARCH_DESCRIPTION = """
Use this to search the web and get relevant information from the internet. This tool provides comprehensive web search capabilities with image support.

Usage:
- The query parameter should be a clear, specific search query
- Results include both text content and relevant images when available
- The tool returns structured search results with titles, URLs, snippets, and raw content
- Raw content includes the full text of web pages for detailed analysis
- Image results include URLs and descriptions when available

Features:
- Searches across multiple web sources for comprehensive results
- Returns structured data including titles, URLs, content snippets
- Includes raw page content for detailed information extraction
- Provides relevant images with descriptions
- Handles various content types and formats

Best practices:
- Use specific, targeted search queries for better results
- Consider the context when formulating search terms
- Results are ordered by relevance and quality
"""