GENERATE_BARCODE_DESCRIPTION = """
Generate a barcode with specified format and save it as image file.

This tool can generate various types of barcodes including Code128, Code39, EAN13, etc.
Returns JSON format with message and file_path for frontend processing.

Supported barcode formats:
- code128: Universal barcode supporting ASCII character set
- code39: Simple alphanumeric barcode
- ean13: European Article Number (13 digits)
- ean8: European Article Number (8 digits)  
- upca: Universal Product Code (12 digits)
- isbn13: International Standard Book Number (13 digits)
- isbn10: International Standard Book Number (10 digits)

Output formats:
- png: Raster image file
- svg: Vector image file
"""