import logging
import os
import io
import json
from typing import Annotated, Optional, Literal, Dict, Any
from pathlib import Path
from datetime import datetime

from langchain_core.tools import tool
from ..decorators import log_io
from .prompt import GENERATE_BARCODE_DESCRIPTION

try:
    import barcode
    from barcode import Code128, Code39, EAN13, EAN8, UPCA, ISBN13, ISBN10
    from barcode.writer import ImageWriter, SVGWriter
    from PIL import Image
    BARCODE_AVAILABLE = True
except ImportError:
    BARCODE_AVAILABLE = False

logger = logging.getLogger(__name__)

BARCODE_FORMATS = {}
if BARCODE_AVAILABLE:
    BARCODE_FORMATS = {
        'code128': Code128,
        'code39': Code39,
        'ean13': EAN13,
        'ean8': EAN8,
        'upca': UPCA,
        'isbn13': ISBN13,
        'isbn10': ISBN10,
    }

OUTPUT_FORMATS = Literal['png', 'svg']


@tool(description=GENERATE_BARCODE_DESCRIPTION)
@log_io
def generate_barcode(
    data: Annotated[str, "The data to encode in the barcode"],
    format_type: Annotated[
        str, 
        "The barcode format. Supported: code128, code39, ean13, ean8, upca, isbn13, isbn10"
    ] = 'code128',
    output_format: Annotated[
        str,
        "Output format: png or svg"
    ] = 'png',
    width: Annotated[Optional[int], "Width of the barcode bars in pixels"] = None,
    height: Annotated[Optional[int], "Height of the barcode in pixels"] = None,
    output_path: Annotated[Optional[str], "Path to save the barcode file"] = None,
    with_text: Annotated[bool, "Whether to include text below the barcode"] = True,
    font_size: Annotated[int, "Font size for the text"] = 10,
) -> str:
    
    if not BARCODE_AVAILABLE:
        error_msg = "Barcode generation requires 'python-barcode' and 'Pillow' packages. Please install them with: pip install python-barcode[images]"
        logger.error(error_msg)
        return json.dumps({
            "error": error_msg,
            "file_paths": []
        }, ensure_ascii=False)
    
    try:
        format_type = format_type.lower()
        if format_type not in BARCODE_FORMATS:
            available_formats = ', '.join(BARCODE_FORMATS.keys())
            error_msg = f"Unsupported barcode format: {format_type}. Available formats: {available_formats}"
            logger.error(error_msg)
            return json.dumps({
                "error": error_msg,
                "file_paths": []
            }, ensure_ascii=False)
        
        if output_format not in ['png', 'svg']:
            error_msg = f"Unsupported output format: {output_format}. Supported formats: png, svg"
            logger.error(error_msg)
            return json.dumps({
                "error": error_msg,
                "file_paths": []
            }, ensure_ascii=False)
        
        BarcodeClass = BARCODE_FORMATS[format_type]
        
        writer_options = {}
        if width:
            writer_options['module_width'] = width / 100
        if height:
            writer_options['module_height'] = height
        if not with_text:
            writer_options['write_text'] = False
        if font_size:
            writer_options['font_size'] = font_size
        
        if output_format == 'svg':
            writer = SVGWriter()
        else:
            writer = ImageWriter()
            
        for key, value in writer_options.items():
            setattr(writer, key, value)
        
        barcode_instance = BarcodeClass(data, writer=writer)
        
        if not output_path:
            workspace_dir = Path("workspace")
            workspace_dir.mkdir(exist_ok=True)
            
            extension = 'svg' if output_format == 'svg' else 'png'
            output_path = workspace_dir / f"barcode_{format_type}_{hash(data) % 10000}.{extension}"
        
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        barcode_instance.save(str(output_path.with_suffix('')))
        
        if output_format == 'svg':
            actual_path = output_path.with_suffix('.svg')
        else:
            actual_path = output_path.with_suffix('.png')
        
        if actual_path.exists():
            file_path = str(actual_path)
            logger.info(f"Generated {format_type} barcode saved to: {file_path}")
            
            return json.dumps({
                "message": f"成功生成{format_type}格式的条形码，已保存到本地文件",
                "file_path": file_path,
                "format": format_type,
                "data": data,
                "output_format": output_format
            }, ensure_ascii=False)
        else:
            error_msg = f"Failed to save barcode to {actual_path}"
            logger.error(error_msg)
            return json.dumps({
                "error": error_msg,
                "file_path": ""
            }, ensure_ascii=False)
                
    except ValueError as e:
        error_msg = f"Invalid data for {format_type} barcode: {str(e)}"
        logger.error(error_msg)
        return json.dumps({
            "error": error_msg,
            "file_path": ""
        }, ensure_ascii=False)
    except Exception as e:
        error_msg = f"Failed to generate barcode: {str(e)}"
        logger.error(error_msg)
        return json.dumps({
            "error": error_msg,
            "file_path": ""
        }, ensure_ascii=False)