#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Catalog Get Item Tool

独立的亚马逊目录获取工具，根据ASIN获取商品目录信息。
"""

import json
import logging
from typing import Any, Dict, Optional
from datetime import datetime

from langchain.tools import tool
from typing_extensions import Annotated

from src.tools.decorators import log_io
from src.tools.amazon.sp_api.common.sp_config import AmazonSPConfig
from sp_api.api import CatalogItems

logger = logging.getLogger(__name__)


@tool(description="根据ASIN获取亚马逊商品目录信息。返回商品的公开信息，包括：商品标题、品牌、制造商、颜色、尺寸、型号、发布日期、商品图片、产品类型、销售排名、商品标识符（UPC/EAN/SKU）、商品尺寸重量等。注意：不包含价格信息，价格信息需要使用listing工具。")
@log_io
def amazon_catalog_get_item(
    asin: Annotated[str, "商品的ASIN编号"],
    marketplace_ids: Annotated[Optional[str], "市场ID列表，逗号分隔，默认ATVPDKIKX0DER"] = "ATVPDKIKX0DER",
    included_data: Annotated[Optional[str], "包含的数据字段，逗号分隔，可选值：summaries,dimensions,identifiers,images,productTypes,salesRanks,variations,vendorDetails,relationships,competitivePricing,offers"] = None,
) -> str:
    """
    根据ASIN获取亚马逊商品目录信息
    
    Args:
        asin: 商品的ASIN编号
        marketplace_ids: 市场ID列表，逗号分隔
        included_data: 包含的数据字段，逗号分隔
        
    Returns:
        商品目录信息的JSON字符串
    """
    try:
        # 配置SP API
        config = AmazonSPConfig()
        config.validate()
        credentials = config.build_credentials()
        
        # 创建Catalog API客户端
        catalog_client = CatalogItems(credentials=credentials)
        
        # 处理市场ID
        if isinstance(marketplace_ids, str):
            marketplace_ids = [mid.strip() for mid in marketplace_ids.split(",")]
        
        # 处理包含的数据字段
        included_data_list = None
        if included_data:
            # 过滤掉Catalog API不支持的参数
            catalog_supported = ['summaries', 'dimensions', 'identifiers', 'images', 'productTypes', 'salesRanks', 'variations', 'vendorDetails', 'relationships', 'competitivePricing']
            included_data_list = [field.strip() for field in included_data.split(",") if field.strip() in catalog_supported]
            if not included_data_list:
                included_data_list = ['summaries']  # 默认值
        
        logger.info(f"获取商品目录信息，ASIN: {asin}, 市场: {marketplace_ids}")
        
        # 调用API获取商品信息
        response = catalog_client.get_catalog_item(
            asin=asin,
            marketplaceIds=marketplace_ids,
            includedData=included_data_list
        )
        
        if not response.payload:
            return json.dumps({
                "success": False,
                "error": "未找到商品信息",
                "details": {
                    "asin": asin,
                    "marketplace_ids": marketplace_ids
                }
            }, ensure_ascii=False)
        
        # 解析返回的数据
        catalog_data = response.payload
        
        # 提取关键信息
        summary = catalog_data.get("summaries", [{}])[0] if catalog_data.get("summaries") else {}
        dimensions = catalog_data.get("dimensions", [{}])[0] if catalog_data.get("dimensions") else {}
        identifiers = catalog_data.get("identifiers", [{}])[0] if catalog_data.get("identifiers") else {}
        images = catalog_data.get("images", [])
        product_types = catalog_data.get("productTypes", [])
        sales_ranks = catalog_data.get("salesRanks", [])
        
        # 构建结构化数据
        structured_data = {
            "success": True,
            "asin": asin,
            "marketplace_ids": marketplace_ids,
            "summary": {
                "title": summary.get("title", ""),
                "brand": summary.get("brand", ""),
                "manufacturer": summary.get("manufacturer", ""),
                "color": summary.get("color", ""),
                "size": summary.get("size", ""),
                "model": summary.get("model", ""),
                "release_date": summary.get("releaseDate", ""),
                "is_adult_product": summary.get("isAdultProduct", False),
                "is_autographed": summary.get("isAutographed", False),
                "is_memorabilia": summary.get("isMemorabilia", False),
            },
            "dimensions": {
                "height": dimensions.get("height", {}),
                "length": dimensions.get("length", {}),
                "width": dimensions.get("width", {}),
                "weight": dimensions.get("weight", {}),
            },
            "identifiers": {
                "external_product_id": identifiers.get("externalProductId", {}),
                "external_product_id_type": identifiers.get("externalProductIdType", ""),
                "sku": identifiers.get("sku", ""),
            },
            "images": [
                {
                    "link": img.get("link", ""),
                    "height": img.get("height", 0),
                    "width": img.get("width", 0),
                    "variant": img.get("variant", ""),
                }
                for img in images
            ],
            "product_types": [
                {
                    "product_type": pt.get("productType", ""),
                    "display_name": pt.get("displayName", ""),
                }
                for pt in product_types
            ],
            "sales_ranks": [
                {
                    "rank": rank.get("rank", 0),
                    "product_category_id": rank.get("productCategoryId", ""),
                    "product_category_name": rank.get("productCategoryName", ""),
                }
                for rank in sales_ranks
            ],
            "raw_data": catalog_data,
            "execution_time": datetime.now().isoformat()
        }
        
        return json.dumps(structured_data, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"获取商品目录信息失败: {e}")
        return json.dumps({
            "success": False,
            "error": f"获取商品目录信息失败: {str(e)}",
            "details": {
                "asin": asin,
                "marketplace_ids": marketplace_ids
            }
        }, ensure_ascii=False)
