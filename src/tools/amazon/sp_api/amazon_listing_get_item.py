#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Listing Get Item Tool

独立的亚马逊listing获取工具，根据seller_id和sku获取listing信息。
"""

import json
import logging
from typing import Any, Dict, Optional
from datetime import datetime

from langchain.tools import tool
from typing_extensions import Annotated

from src.tools.decorators import log_io
from src.tools.amazon.sp_api.common.sp_config import AmazonSPConfig
from sp_api.api import ListingsItems

logger = logging.getLogger(__name__)


@tool(description="根据seller_id和标识符获取亚马逊listing信息。支持多种标识符类型：SKU（卖家私有信息，包含价格）、ASIN（公开商品信息，不包含价格）、UPC/EAN（通过搜索查找，返回公开信息）。返回商品信息包括：产品标题、品牌、描述、五点描述、搜索关键词、价格信息（仅SKU）、履约可用性、采购信息、listing问题、报价信息等。注意：ASIN查询返回公开商品信息，不包含价格数据。")
@log_io
def amazon_listing_get_item(
    seller_id: Annotated[str, "卖家ID"],
    identifier: Annotated[str, "商品标识符（SKU、ASIN、UPC、EAN等）"],
    identifier_type: Annotated[Optional[str], "标识符类型，可选值：sku, asin, upc, ean等，默认为sku"] = "sku",
    marketplace_ids: Annotated[Optional[str], "市场ID列表，逗号分隔，默认ATVPDKIKX0DER"] = "ATVPDKIKX0DER",
    issue_locale: Annotated[Optional[str], "问题本地化语言，如en_US"] = None,
    included_data: Annotated[Optional[str], "包含的数据字段，逗号分隔，可选值：summaries,attributes,issues,offers,fulfillmentAvailability,procurement"] = None,
) -> str:
    """
    根据seller_id和标识符获取亚马逊listing信息
    
    Args:
        seller_id: 卖家ID
        identifier: 商品标识符（SKU、ASIN、UPC、EAN等）
        identifier_type: 标识符类型
        marketplace_ids: 市场ID列表，逗号分隔
        issue_locale: 问题本地化语言
        
    Returns:
        listing信息的JSON字符串
    """
    try:
        # 配置SP API
        config = AmazonSPConfig()
        config.validate()
        credentials = config.build_credentials()
        
        # 创建Listings API客户端
        listings_client = ListingsItems(credentials=credentials)
        
        # 处理市场ID
        if isinstance(marketplace_ids, str):
            marketplace_ids = [mid.strip() for mid in marketplace_ids.split(",")]
        
        # 处理包含的数据字段
        included_data_list = None
        if included_data:
            included_data_list = [field.strip() for field in included_data.split(",")]
        
        logger.info(f"获取listing信息，seller_id: {seller_id}, identifier: {identifier}, type: {identifier_type}, 市场: {marketplace_ids}")
        
        # 根据标识符类型选择不同的API调用方式
        if identifier_type.lower() == "sku":
            # 使用ListingsItems API获取SKU信息
            response = listings_client.get_listings_item(
                sellerId=seller_id,
                sku=identifier,
                marketplaceIds=marketplace_ids,
                issueLocale=issue_locale,
                includedData=included_data_list
            )
        elif identifier_type.lower() == "asin":
            # 使用CatalogItems API获取ASIN信息
            from sp_api.api import CatalogItems
            
            catalog_client = CatalogItems(credentials=credentials)
            
            # 为Catalog API使用正确的included_data参数
            catalog_included_data = None
            if included_data_list:
                # 过滤掉Catalog API不支持的参数
                catalog_supported = ['summaries', 'dimensions', 'identifiers', 'images', 'productTypes', 'salesRanks', 'variations', 'vendorDetails', 'relationships', 'competitivePricing']
                catalog_included_data = [field for field in included_data_list if field in catalog_supported]
                if not catalog_included_data:
                    catalog_included_data = ['summaries']  # 默认值
            
            # 获取Catalog信息
            catalog_response = catalog_client.get_catalog_item(
                asin=identifier,
                marketplaceIds=marketplace_ids,
                includedData=catalog_included_data
            )
            
            # 构建类似listing的响应格式
            catalog_data = catalog_response.payload if catalog_response.payload else {}
            
            # 解析Catalog数据到类似listing的格式
            summary = catalog_data.get('summaries', [{}])[0] if catalog_data.get('summaries') else {}
            
            # 构建attributes结构
            attributes = {
                "title": {"value": summary.get("itemName", "")},
                "brand": {"value": summary.get("brandName", "")},
                "description": {},
                "bullet_point": {},
                "generic_keyword": {},
                "list_price": {},  # Catalog API不包含价格信息
                "cost_price": {},  # Catalog API不包含价格信息
                "fulfillment_availability": {},
                "procurement": {}
            }
            
            response = type('Response', (), {
                'payload': {
                    'asin': identifier,
                    'attributes': attributes,
                    'catalog_data': catalog_data
                }
            })()
        else:
            # 对于其他标识符类型，先尝试通过Catalog搜索找到ASIN
            from sp_api.api import CatalogItems
            
            catalog_client = CatalogItems(credentials=credentials)
            
            # 搜索商品
            search_response = catalog_client.search_catalog_items(
                keywords=[identifier],
                marketplaceIds=marketplace_ids
            )
            
            if search_response.payload and search_response.payload.get('items'):
                # 找到商品，使用第一个结果的ASIN
                first_item = search_response.payload['items'][0]
                asin = first_item.get('asin')
                
                if asin:
                    # 递归调用，使用ASIN
                    return amazon_listing_get_item.func(seller_id, asin, "asin", marketplace_ids, issue_locale, included_data)
            
            # 如果没找到，返回错误
            return json.dumps({
                "success": False,
                "error": f"未找到标识符为 {identifier} 的商品信息",
                "details": {
                    "seller_id": seller_id,
                    "identifier": identifier,
                    "identifier_type": identifier_type,
                    "marketplace_ids": marketplace_ids
                }
            }, ensure_ascii=False)
        
        if not response.payload:
            return json.dumps({
                "success": False,
                "error": "未找到listing信息",
                "details": {
                    "seller_id": seller_id,
                    "identifier": identifier,
                    "identifier_type": identifier_type,
                    "marketplace_ids": marketplace_ids
                }
            }, ensure_ascii=False)
        
        # 解析返回的数据
        listing_data = response.payload
        
        # 提取关键信息
        attributes = listing_data.get("attributes", {})
        issues = listing_data.get("issues", [])
        offers = listing_data.get("offers", [])
        
        # 构建结构化数据
        structured_data = {
            "success": True,
            "seller_id": seller_id,
            "identifier": identifier,
            "identifier_type": identifier_type,
            "marketplace_ids": marketplace_ids,
            "asin": listing_data.get("asin", ""),
            "product_type": listing_data.get("productType", ""),
            "requirements": listing_data.get("requirements", ""),
            "attributes": {
                "title": attributes.get("title", {}),
                "brand": attributes.get("brand", {}),
                "description": attributes.get("description", {}),
                "bullet_point": attributes.get("bullet_point", {}),
                "generic_keyword": attributes.get("generic_keyword", {}),
                "list_price": attributes.get("list_price", {}),
                "cost_price": attributes.get("cost_price", {}),
                "fulfillment_availability": attributes.get("fulfillment_availability", {}),
                "procurement": attributes.get("procurement", {}),
            },
            "issues": [
                {
                    "code": issue.get("code", ""),
                    "message": issue.get("message", ""),
                    "severity": issue.get("severity", ""),
                    "attribute_name": issue.get("attributeName", ""),
                }
                for issue in issues
            ],
            "offers": [
                {
                    "marketplace_id": offer.get("marketplaceId", ""),
                    "price": offer.get("price", {}),
                    "fulfillment_availability": offer.get("fulfillmentAvailability", {}),
                }
                for offer in offers
            ],
            "raw_data": listing_data,
            "execution_time": datetime.now().isoformat()
        }
        
        return json.dumps(structured_data, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"获取listing信息失败: {e}")
        return json.dumps({
            "success": False,
            "error": f"获取listing信息失败: {str(e)}",
            "details": {
                "seller_id": seller_id,
                "identifier": identifier,
                "identifier_type": identifier_type,
                "marketplace_ids": marketplace_ids
            }
        }, ensure_ascii=False)
