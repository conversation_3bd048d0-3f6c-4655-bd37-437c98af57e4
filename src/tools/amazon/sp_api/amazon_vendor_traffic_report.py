#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Vendor Traffic Report Tool

独立的供应商历史流量报告工具，从拉取报告到解析数据的完整流程。
"""

from __future__ import annotations

import json
import logging
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Annotated, Any, Dict, List, Optional, Union

from langchain_core.tools import tool
from src.tools.decorators import log_io

# 导入common模块
from src.tools.amazon.sp_api.common.reports import ReportsClient, ReportRequest, iso_utc
from src.tools.amazon.sp_api.common.sp_config import AmazonSPConfig

logger = logging.getLogger(__name__)


@tool(description="获取亚马逊供应商历史流量报告数据。支持指定日期范围，自动处理报告创建、状态检查和数据解析。返回结构化的历史流量数据，包含页面浏览量、转化率等关键指标。")
@log_io
def amazon_vendor_traffic_report(
    start_date: Annotated[str, "开始日期，格式：YYYY-MM-DD"] = None,
    end_date: Annotated[str, "结束日期，格式：YYYY-MM-DD"] = None,
    date: Annotated[str, "单日日期，格式：YYYY-MM-DD，如果指定则忽略start_date和end_date"] = None,
    marketplace_ids: Annotated[Optional[str], "市场ID列表，逗号分隔，默认ATVPDKIKX0DER"] = "ATVPDKIKX0DER",
    wait_timeout: Annotated[int, "等待超时时间（秒），默认300"] = 300,
    output_dir: Annotated[str, "输出目录，默认reports"] = "reports",
) -> str:
    """
    获取亚马逊供应商历史流量报告数据
    
    Args:
        start_date: 开始日期，格式：YYYY-MM-DD
        end_date: 结束日期，格式：YYYY-MM-DD
        date: 单日日期，格式：YYYY-MM-DD
        marketplace_ids: 市场ID列表，逗号分隔
        wait_timeout: 等待超时时间（秒）
        output_dir: 输出目录
        
    Returns:
        JSON格式的历史流量报告数据
    """
    try:
        # 初始化配置
        config = AmazonSPConfig()
        reports_client = ReportsClient(config)
        
        # 处理日期参数
        if date:
            start_date = date
            end_date = date
        elif not start_date or not end_date:
            # 默认获取昨天的数据
            yesterday = datetime.now(timezone.utc) - timedelta(days=1)
            start_date = yesterday.strftime("%Y-%m-%d")
            end_date = start_date
        
        # 处理市场ID
        if isinstance(marketplace_ids, str):
            marketplace_ids = [mid.strip() for mid in marketplace_ids.split(",")]
        
        # 智能日期回退策略：从指定日期开始逐日回退寻找可用数据
        max_days_back = 4  # 最多回退4天
        base_date = datetime.strptime(start_date, "%Y-%m-%d")
        
        for days_back in range(max_days_back):
            try_date = base_date - timedelta(days=days_back)
            try_date_str = try_date.strftime("%Y-%m-%d")
            
            if days_back == 0:
                logger.info(f"🔍 尝试日期: {try_date_str} (请求日期)")
            else:
                logger.info(f"🔍 尝试日期: {try_date_str} ({days_back}天前)")
            
            # 创建报告请求
            report_request = ReportRequest(
                report_type="GET_VENDOR_TRAFFIC_REPORT",
                data_start_time=datetime.strptime(f"{try_date_str}T00:00:00Z", "%Y-%m-%dT%H:%M:%SZ").isoformat().replace("+00:00", "Z"),
                data_end_time=datetime.strptime(f"{try_date_str}T23:59:59Z", "%Y-%m-%dT%H:%M:%SZ").isoformat().replace("+00:00", "Z"),
                marketplace_ids=marketplace_ids,
                report_options={
                    "distributorView": "MANUFACTURING",
                    "reportPeriod": "DAY"
                }
            )
            
            try:
                # 创建报告
                logger.info(f"创建报告请求...")
                report_id = reports_client.create(report_request)
                logger.info(f"✅ 报告创建成功！报告ID: {report_id}")
                
                # 等待报告完成
                logger.info(f"等待报告处理完成...")
                document_id = reports_client.wait_done(report_id, timeout_seconds=wait_timeout)
                logger.info(f"📄 报告处理完成，状态: DONE")
                
                # 尝试下载和解析报告
                try:
                    # 下载报告
                    logger.info(f"下载报告文档: {document_id}")
                    download_url = reports_client.get_download_url(document_id)
                    logger.info(f"获取下载URL成功")
                    
                    # 下载文件
                    output_path = Path(output_dir) / f"vendor_traffic_report_{try_date_str}_{try_date_str}.json"
                    reports_client.download_to_file(download_url, output_path)
                    logger.info(f"报告下载完成: {output_path}")
                    
                    # 检查文件是否有数据（简单检查文件大小）
                    if output_path.stat().st_size < 500:  # 如果文件小于500字节，可能是空报告
                        raise Exception("报告数据为空")
                    
                    # 如果一切成功，更新实际使用的日期并退出循环
                    start_date = try_date_str
                    end_date = try_date_str
                    logger.info(f"🎉 成功获取 {try_date_str} 的流量报告！")
                    break
                    
                except Exception as download_error:
                    # 下载或解析失败，视为该日期不可用，继续尝试下一个日期
                    download_error_msg = str(download_error)
                    logger.warning(f"下载或解析失败: {download_error_msg}")
                    
                    if "报告数据为空" in download_error_msg:
                        logger.warning(f"⚠️  日期 {try_date_str} 报告数据为空")
                    else:
                        logger.warning(f"⚠️  日期 {try_date_str} 下载或解析失败")
                    
                    if days_back < max_days_back - 1:
                        logger.info(f"🔄 尝试更早的日期...")
                        continue
                    else:
                        # 最后一次尝试也失败了
                        attempted_dates = [(base_date - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(max_days_back)]
                        return json.dumps({
                            "success": False,
                            "error": f"已尝试过去 {max_days_back} 天，均无法获取可用数据",
                            "details": {
                                "attempted_dates": attempted_dates,
                                "last_error": download_error_msg,
                                "report_type": "GET_VENDOR_TRAFFIC_REPORT"
                            }
                        }, ensure_ascii=False)
                
            except Exception as e:
                error_msg = str(e)
                
                # 检查是否是FATAL错误（数据未准备好）
                if "FATAL" in error_msg or "CANCELLED" in error_msg:
                    logger.warning(f"⚠️  日期 {try_date_str} 数据未准备好（状态：FATAL）")
                    if days_back < max_days_back - 1:
                        logger.info(f"🔄 尝试更早的日期...")
                        continue
                    else:
                        # 所有日期都尝试失败了
                        attempted_dates = [(base_date - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(max_days_back)]
                        return json.dumps({
                            "success": False,
                            "error": f"已尝试过去 {max_days_back} 天，均无法获取可用数据",
                            "details": {
                                "attempted_dates": attempted_dates,
                                "last_error": error_msg,
                                "report_type": "GET_VENDOR_TRAFFIC_REPORT"
                            }
                        }, ensure_ascii=False)
                else:
                    # 非FATAL错误（配置、权限等问题），直接返回
                    logger.error(f"❌ 报告创建或处理失败: {error_msg}")
                    return json.dumps({
                        "success": False,
                        "error": f"报告处理失败: {error_msg}",
                        "details": {
                            "date": try_date_str,
                            "report_id": report_id if 'report_id' in locals() else None,
                            "report_type": "GET_VENDOR_TRAFFIC_REPORT"
                        }
                    }, ensure_ascii=False)
        
        # 如果执行到这里，说明成功获取了数据，构建返回结果
        result = {
            "success": True,
            "report_type": "GET_VENDOR_TRAFFIC_REPORT",
            "report_id": report_id,
            "document_id": document_id,
            "date_range": {
                "start": start_date,
                "end": end_date
            },
            "marketplace_ids": marketplace_ids,
            "file_path": str(output_path),
            "file_size": output_path.stat().st_size if output_path.exists() else 0,
            "data": parsed_data["data"],
            "execution_time": datetime.now().isoformat()
        }
        
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"供应商历史流量报告工具执行失败: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "details": {
                "start_date": start_date,
                "end_date": end_date,
                "marketplace_ids": marketplace_ids
            }
        }, ensure_ascii=False)


def main():
    """直接测试函数"""
    import sys
    import json
    
    print("🚦 测试 Amazon Vendor Traffic Report")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        {
            "name": "测试默认参数（昨天的数据）",
            "params": {}
        },
        {
            "name": "测试指定日期",
            "params": {
                "start_date": "2025-08-23",
                "end_date": "2025-08-23"
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print("-" * 30)
        
        try:
            # 调用函数
            result = amazon_vendor_traffic_report.func(**test_case['params'])
            data = json.loads(result)
            
            if data['success']:
                print("✅ 成功获取流量报告！")
                print(f"📊 报告类型: {data['report_type']}")
                print(f"📅 日期范围: {data['date_range']['start']} 到 {data['date_range']['end']}")
                print(f"🆔 报告ID: {data['report_id']}")
                print(f"📄 文档ID: {data['document_id']}")
                print(f"📍 市场: {data['marketplace_ids']}")
                print(f"📁 输出目录: {data['output_file']}")
            else:
                print("❌ 获取失败")
                print(f"错误信息: {data['error']}")
                if 'attempted_dates' in data.get('details', {}):
                    print(f"尝试的日期: {data['details']['attempted_dates']}")
                
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
    
    print("\n" + "=" * 50)
    print("测试完成！")


if __name__ == "__main__":
    main()


def parse_vendor_traffic_report(file_path: str) -> Dict[str, Any]:
    """
    解析供应商历史流量报告数据
    
    Args:
        file_path: 报告文件路径
        
    Returns:
        解析后的数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        # 提取报告规格和数据
        report_spec = report_data.get("reportSpecification", {})
        report_data_list = report_data.get("trafficData", [])
        
        if not report_data_list:
            return {
                "success": False,
                "error": "报告数据为空"
            }
        
        # 统计总数据
        total_records = len(report_data_list)
        total_page_views = sum(item.get("pageViews", 0) for item in report_data_list)
        total_sessions = sum(item.get("sessions", 0) for item in report_data_list)
        total_units_ordered = sum(item.get("unitsOrdered", 0) for item in report_data_list)
        total_ordered_revenue = sum(item.get("orderedRevenue", 0) for item in report_data_list)
        
        # 按ASIN分组统计
        asin_stats = {}
        for item in report_data_list:
            asin = item.get("asin", "")
            if asin:
                if asin not in asin_stats:
                    asin_stats[asin] = {
                        "page_views": 0,
                        "sessions": 0,
                        "units_ordered": 0,
                        "ordered_revenue": 0
                    }
                asin_stats[asin]["page_views"] += item.get("pageViews", 0)
                asin_stats[asin]["sessions"] += item.get("sessions", 0)
                asin_stats[asin]["units_ordered"] += item.get("unitsOrdered", 0)
                asin_stats[asin]["ordered_revenue"] += item.get("orderedRevenue", 0)
        
        # 计算转化率
        overall_conversion_rate = (total_units_ordered / total_page_views * 100) if total_page_views > 0 else 0
        
        # 按日期统计流量
        date_stats = {}
        for item in report_data_list:
            date = item.get("date", "")
            if date:
                if date not in date_stats:
                    date_stats[date] = {
                        "page_views": 0,
                        "sessions": 0,
                        "units_ordered": 0,
                        "ordered_revenue": 0
                    }
                date_stats[date]["page_views"] += item.get("pageViews", 0)
                date_stats[date]["sessions"] += item.get("sessions", 0)
                date_stats[date]["units_ordered"] += item.get("unitsOrdered", 0)
                date_stats[date]["ordered_revenue"] += item.get("orderedRevenue", 0)
        
        # 转换为列表格式
        asin_summary = [
            {
                "asin": asin,
                "page_views": stats["page_views"],
                "sessions": stats["sessions"],
                "units_ordered": stats["units_ordered"],
                "ordered_revenue": stats["ordered_revenue"],
                "conversion_rate": (stats["units_ordered"] / stats["page_views"] * 100) if stats["page_views"] > 0 else 0
            }
            for asin, stats in asin_stats.items()
        ]
        
        # 按页面浏览量排序
        asin_summary.sort(key=lambda x: x["page_views"], reverse=True)
        
        # 日期统计转换为列表
        daily_breakdown = [
            {
                "date": date,
                "page_views": stats["page_views"],
                "sessions": stats["sessions"],
                "units_ordered": stats["units_ordered"],
                "ordered_revenue": stats["ordered_revenue"]
            }
            for date, stats in date_stats.items()
        ]
        
        # 按日期排序
        daily_breakdown.sort(key=lambda x: x["date"])
        
        # 构建返回数据
        parsed_data = {
            "report_type": "GET_VENDOR_TRAFFIC_REPORT",
            "date_range": {
                "start": report_spec.get("dataStartTime"),
                "end": report_spec.get("dataEndTime")
            },
            "marketplace_ids": report_spec.get("marketplaceIds", []),
            "summary": {
                "total_records": total_records,
                "total_page_views": total_page_views,
                "total_sessions": total_sessions,
                "total_units_ordered": total_units_ordered,
                "total_ordered_revenue": total_ordered_revenue,
                "overall_conversion_rate": overall_conversion_rate,
                "unique_asins": len(asin_stats)
            },
            "top_traffic": asin_summary[:10],  # 前10个流量最多的ASIN
            "daily_breakdown": daily_breakdown,  # 日期级流量统计
            "all_data": report_data_list[:100]  # 限制返回前100条详细数据
        }
        
        return {
            "success": True,
            "data": parsed_data
        }
        
    except Exception as e:
        logger.error(f"解析供应商历史流量报告失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }
