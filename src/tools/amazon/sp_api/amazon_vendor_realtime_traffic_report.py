#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Vendor Real-time Traffic Report Tool

独立的供应商实时流量报告工具，从拉取报告到解析数据的完整流程。
"""

from __future__ import annotations

import json
import logging
import os
import psycopg
import sys
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Annotated, Any, Dict, List, Optional, Union

from langchain_core.tools import tool
from src.tools.decorators import log_io

# 导入common模块
from src.tools.amazon.sp_api.common.reports import ReportsClient, ReportRequest, iso_utc
from src.tools.amazon.sp_api.common.sp_config import AmazonSPConfig

logger = logging.getLogger(__name__)

# 市场映射表：前端marketplace -> Amazon marketplace_ids
MARKETPLACE_MAPPING = {
    # 北美
    "US": "ATVPDKIKX0DER",   # 美国
    "CA": "A2EUQ1WTGCTBG2",  # 加拿大
    "MX": "A1AM78C64UM0Y8",  # 墨西哥
    "BR": "A2Q3Y263D00KWC",  # 巴西
    
    # 欧洲
    "UK": "A1F83G8C2ARO7P",  # 英国
    "DE": "A1PA6795UKMFR9",  # 德国
    "FR": "A13V1IB3VIYZZH",  # 法国
    "IT": "APJ6JRA9NG5V4",   # 意大利
    "ES": "A1RKKUPIHCS9HS",  # 西班牙
    "NL": "A1805IZSGTT6HS",  # 荷兰
    "SE": "A2NODRKZP88ZB9",  # 瑞典
    "PL": "A1C3SOZRARQ6R3",  # 波兰
    "TR": "A33AVAJ2PDY3EV",  # 土耳其
    "EG": "ARBP9OOSHTCHU",   # 埃及

    # 亚太
    "JP": "A1VC38T7YXB528",  # 日本
    "AU": "A39IBJ37TRP1C6",  # 澳大利亚
    "SG": "A19VAU5U5O7RUS",  # 新加坡
    "IN": "A21TJRUUN4KGV",   # 印度

    # 中东
    "AE": "A2VIGQ35RCS4UG",  # 阿联酋
    "SA": "A17E79C6D8DWNP",  # 沙特阿拉伯
}


@tool(description="获取亚马逊供应商实时流量报告数据。自动拉取最新72小时数据，处理报告创建、状态检查和数据解析。返回结构化的实时流量数据，包含小时级流量统计、ASIN表现等关键指标。支持24小时数据聚合和高效的数据库存储。")
@log_io
def amazon_vendor_realtime_traffic_report(
    company_id: Annotated[str, "前端传递的公司ID"],
    store_id: Annotated[str, "前端传递的店铺ID"],
    marketplace: Annotated[str, "前端传递的市场代码，如US、UK、DE等"] = "US",
) -> str:
    """
    获取亚马逊供应商实时流量报告数据
    
    Args:
        company_id: 前端传递的公司ID
        store_id: 前端传递的店铺ID
        marketplace: 前端传递的市场代码（如US、UK、DE等）
        
    Returns:
        JSON格式的实时流量报告数据
    """
    try:
        # 参数验证
        if not company_id:
            return json.dumps({
                "success": False,
                "error": "缺少必要参数: company_id",
                "error_type": "MISSING_COMPANY_ID"
            }, ensure_ascii=False, indent=2)
        
        if not store_id:
            return json.dumps({
                "success": False,
                "error": "缺少必要参数: store_id",
                "error_type": "MISSING_STORE_ID"
            }, ensure_ascii=False, indent=2)
        
        if not marketplace:
            return json.dumps({
                "success": False,
                "error": "缺少必要参数: marketplace",
                "error_type": "MISSING_MARKETPLACE"
            }, ensure_ascii=False, indent=2)
        
        # 映射 marketplace 到 marketplace_ids
        if marketplace not in MARKETPLACE_MAPPING:
            return json.dumps({
                "success": False,
                "error": f"不支持的市场: {marketplace}",
                "error_type": "UNSUPPORTED_MARKETPLACE",
                "supported_marketplaces": list(MARKETPLACE_MAPPING.keys())
            }, ensure_ascii=False, indent=2)
        
        marketplace_ids = [MARKETPLACE_MAPPING[marketplace]]
        
        # 初始化配置
        config = AmazonSPConfig()
        reports_client = ReportsClient(config)
        
        # 对于实时流量报告，使用最近72小时数据（Amazon建议的时间窗口）
        # 时间边界：左闭右开，结束时间往回错1小时，获取较新数据但避免当前小时
        now = datetime.now(timezone.utc)
        
        # 左闭：72小时前
        start_time = now - timedelta(hours=72)
        
        # 右开：1小时前（获取较新数据，但避免当前小时的不完整数据）
        end_time = now - timedelta(hours=1)
        
        data_start_time = start_time.isoformat().replace("+00:00", "Z")
        data_end_time = end_time.isoformat().replace("+00:00", "Z")
        
        logger.info(f"创建报告类型: GET_VENDOR_REAL_TIME_TRAFFIC_REPORT")
        logger.info(f"市场ID: {marketplace}")
        logger.info(f"时间范围: {data_start_time} 到 {data_end_time}")
        
        report_request = ReportRequest(
            report_type="GET_VENDOR_REAL_TIME_TRAFFIC_REPORT",
            marketplace_ids=marketplace_ids,
            data_start_time=data_start_time,
            data_end_time=data_end_time,
            report_options={
                "dateGranularity": "HOUR"
            }
        )
        
        # 创建报告
        try:
            report_id = reports_client.create(report_request)
            logger.info(f"报告创建成功，报告ID: {report_id}")
        except Exception as e:
            logger.error(f"报告创建失败: {str(e)}")
            return json.dumps({
                "success": False,
                "error": f"报告创建失败: {str(e)}",
                "details": {
                    "report_type": "GET_VENDOR_REAL_TIME_TRAFFIC_REPORT",
                    "marketplace_ids": marketplace_ids
                }
            }, ensure_ascii=False)
        
        # 等待报告完成
        try:
            document_id = reports_client.wait_done(report_id)
            logger.info(f"报告处理完成，文档ID: {document_id}")
        except Exception as e:
            logger.error(f"等待报告完成失败: {str(e)}")
            return json.dumps({
                "success": False,
                "error": f"等待报告完成失败: {str(e)}",
                "details": {
                    "report_id": report_id,
                    "report_type": "GET_VENDOR_REAL_TIME_TRAFFIC_REPORT"
                }
            }, ensure_ascii=False)
        
        # 下载报告
        try:
            download_url = reports_client.get_download_url(document_id)
            
            # 清理之前的报告文件
            reports_dir = Path("reports")
            if reports_dir.exists():
                for old_file in reports_dir.glob("vendor_realtime_traffic_report_*.json"):
                    try:
                        old_file.unlink()
                        logger.info(f"删除旧文件: {old_file}")
                    except Exception as e:
                        logger.warning(f"删除旧文件失败: {old_file}, 错误: {e}")
            
            # 确保reports目录存在
            reports_dir.mkdir(exist_ok=True)
            
            # 生成文件名使用时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = reports_dir / f"vendor_realtime_traffic_report_{timestamp}.json"
            reports_client.download_to_file(download_url, output_path)
            logger.info(f"报告下载成功: {output_path}")
        except Exception as e:
            logger.error(f"报告下载失败: {str(e)}")
            return json.dumps({
                "success": False,
                "error": f"报告下载失败: {str(e)}",
                "details": {
                    "report_id": report_id,
                    "document_id": document_id
                }
            }, ensure_ascii=False)
        
        # 解析报告数据
        logger.info(f"解析报告数据: {output_path}")
        parsed_data = parse_vendor_realtime_traffic_report(str(output_path))
        
        if not parsed_data["success"]:
            # 清理临时文件
            try:
                if output_path.exists():
                    output_path.unlink()
                    logger.info(f"报告解析失败，清理临时文件: {output_path}")
            except Exception as cleanup_e:
                logger.warning(f"清理临时文件失败: {output_path}, 错误: {cleanup_e}")
            
            return json.dumps({
                "success": False,
                "error": f"报告解析失败: {parsed_data['error']}",
                "details": {
                    "file_path": str(output_path)
                }
            }, ensure_ascii=False)
        
        # 存储数据到数据库
        try:
            stored_count = store_realtime_traffic_report_to_database(
                parsed_data["data"]["all_data"], 
                company_id, 
                store_id, 
                marketplace
            )
            logger.info(f"成功存储 {stored_count} 条实时流量报告记录到数据库")
        except Exception as e:
            logger.error(f"数据库存储失败: {e}")
            stored_count = 0
        
        # 返回成功结果
        result = {
            "success": True,
            "data_source": "AMAZON_VENDOR_REALTIME_TRAFFIC_REPORT",
            "operation_id": f"realtime_traffic_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "operation_time": datetime.now(timezone.utc).isoformat(),
            "database_stored": stored_count,
            "report_type": "GET_VENDOR_REAL_TIME_TRAFFIC_REPORT",
            "report_id": report_id,
            "document_id": document_id,
            "time_range": {
                "start": parsed_data["data"]["date_range"]["start"],
                "end": parsed_data["data"]["date_range"]["end"]
            },
            "marketplace_ids": marketplace_ids,
            "file_path": str(output_path),
            "file_size": output_path.stat().st_size if output_path.exists() else 0,
            "data": parsed_data["data"],
            "execution_time": datetime.now().isoformat()
        }
        
        # 添加按小时解析的统计信息
        if parsed_data["data"]["hourly_breakdown"]:
            logger.info("📊 按小时解析统计:")
            for hour_data in parsed_data["data"]["hourly_breakdown"][:5]:  # 显示前5个小时
                logger.info(f"  {hour_data['hour']}: {hour_data['glance_views']} 次浏览")
            if len(parsed_data["data"]["hourly_breakdown"]) > 5:
                logger.info(f"  ... 还有 {len(parsed_data["data"]["hourly_breakdown"]) - 5} 个小时的数据")
        
        # 添加ASIN统计信息
        if parsed_data["data"]["top_traffic"]:
            logger.info("🏷️  Top ASIN 流量统计:")
            for i, asin_data in enumerate(parsed_data["data"]["top_traffic"][:3], 1):  # 显示前3个ASIN
                logger.info(f"  {i}. {asin_data['asin']}: {asin_data['glance_views']} 次浏览")
        
        # 清理临时文件
        try:
            if output_path.exists():
                output_path.unlink()
                logger.info(f"清理临时文件: {output_path}")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {output_path}, 错误: {e}")
        
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"供应商实时流量报告工具执行失败: {e}")
        
        # 清理临时文件（如果存在）
        try:
            if 'output_path' in locals() and output_path.exists():
                output_path.unlink()
                logger.info(f"异常情况下清理临时文件: {output_path}")
        except Exception as cleanup_e:
            logger.warning(f"异常情况下清理临时文件失败: {cleanup_e}")
        
        return json.dumps({
            "success": False,
            "error": str(e),
            "details": {
                "report_type": "GET_VENDOR_REAL_TIME_TRAFFIC_REPORT",
                "marketplace_ids": marketplace_ids
            }
        }, ensure_ascii=False)


def parse_vendor_realtime_traffic_report(file_path: str) -> Dict[str, Any]:
    """
    解析供应商实时流量报告数据
    
    Args:
        file_path: 报告文件路径
        
    Returns:
        解析后的数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        # 提取报告规格和数据
        report_spec = report_data.get("reportSpecification", {})
        report_data_list = report_data.get("reportData", [])  # 修复：使用reportData而不是trafficData
        
        if not report_data_list:
            return {
                "success": False,
                "error": "报告数据为空"
            }
        
        # 统计总数据
        total_records = len(report_data_list)
        total_glance_views = sum(item.get("glanceViews", 0) for item in report_data_list)  # 修复：使用glanceViews
        
        # 按ASIN分组统计
        asin_stats = {}
        # 按小时分组统计
        hour_stats = {}
        
        for item in report_data_list:
            asin = item.get("asin", "")
            # 提取小时信息（如果有）
            start_time = item.get("startTime", "")
            hour = ""
            if start_time:
                try:
                    dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                    hour = dt.strftime("%Y-%m-%d %H:00")
                    # 添加调试信息
                    if len(hour_stats) < 5:  # 只显示前5个时间解析
                        logger.info(f"🔍 时间解析: {start_time} -> {hour}")
                except Exception as e:
                    logger.warning(f"⚠️ 时间解析失败: {start_time}, 错误: {e}")
                    hour = "未知时间"
            
            if asin:
                if asin not in asin_stats:
                    asin_stats[asin] = {
                        "glance_views": 0,
                        "page_views": 0,
                        "sessions": 0,
                        "units_ordered": 0,
                        "ordered_revenue": 0
                    }
                
                # 正确提取glanceViews字段
                glance_views = item.get("glanceViews", 0)
                page_views = item.get("pageViews", 0)
                sessions = item.get("sessions", 0)
                units_ordered = item.get("unitsOrdered", 0)
                ordered_revenue = item.get("orderedRevenue", 0)
                
                # 添加调试信息（前5条记录）
                if len(asin_stats) <= 5:
                    logger.info(f"🔍 ASIN {asin} 数据: glanceViews={glance_views}, pageViews={page_views}, sessions={sessions}")
                
                # 累加到ASIN统计
                asin_stats[asin]["glance_views"] += glance_views
                asin_stats[asin]["page_views"] += page_views
                asin_stats[asin]["sessions"] += sessions
                asin_stats[asin]["units_ordered"] += units_ordered
                asin_stats[asin]["ordered_revenue"] += ordered_revenue
                
                # 按小时统计
                if hour not in hour_stats:
                    hour_stats[hour] = {
                        "glance_views": 0,
                        "page_views": 0,
                        "sessions": 0,
                        "units_ordered": 0,
                        "ordered_revenue": 0
                    }
                
                hour_stats[hour]["glance_views"] += glance_views
                hour_stats[hour]["page_views"] += page_views
                hour_stats[hour]["sessions"] += sessions
                hour_stats[hour]["units_ordered"] += units_ordered
                hour_stats[hour]["ordered_revenue"] += ordered_revenue
        
        # 添加调试信息
        logger.info(f"📊 解析统计: 总记录数={total_records}, 有效ASIN数={len(asin_stats)}, 有效小时数={len(hour_stats)}")
        logger.info(f"🔍 前5个ASIN统计: {list(asin_stats.items())[:5]}")
        logger.info(f"🔍 前5个小时统计: {list(hour_stats.items())[:5]}")
        
        # 转换为列表格式
        asin_summary = [
            {
                "asin": asin,
                "glance_views": stats["glance_views"]
            }
            for asin, stats in asin_stats.items()
        ]
        
        # 按页面浏览量排序
        asin_summary.sort(key=lambda x: x["glance_views"], reverse=True)
        
        # 小时统计
        hour_summary = [
            {
                "hour": hour,
                "glance_views": stats["glance_views"]
            }
            for hour, stats in hour_stats.items()
        ]
        
        # 按时间排序
        hour_summary.sort(key=lambda x: x["hour"])
        
        # 构建返回数据
        parsed_data = {
            "report_type": "GET_VENDOR_REAL_TIME_TRAFFIC_REPORT",
            "date_range": {
                "start": report_spec.get("dataStartTime"),
                "end": report_spec.get("dataEndTime")
            },
            "marketplace_ids": report_spec.get("marketplaceIds", []),
            "summary": {
                "total_records": total_records,
                "total_glance_views": total_glance_views,
                "unique_asins": len(asin_stats),
                "unique_hours": len(hour_stats)
            },
            "top_traffic": asin_summary[:10],  # 前10个流量最多的ASIN
            "hourly_breakdown": hour_summary,  # 小时级统计
            "all_data": report_data_list
        }
        
        return {
            "success": True,
            "data": parsed_data
        }
        
    except Exception as e:
        logger.error(f"解析供应商实时流量报告失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def store_realtime_traffic_report_to_database(traffic_data: List[Dict], company_id: str, store_id: str, marketplace: str) -> int:
    """
    将实时流量报告数据存储到数据库
    
    Args:
        traffic_data: 流量数据列表
        company_id: 公司ID
        store_id: 店铺ID
        marketplace: 市场代码
        
    Returns:
        存储的记录数
    """
    if not traffic_data:
        return 0
    
    postgres_uri = os.getenv("POSTGRES_URI")
    if not postgres_uri:
        raise ValueError("POSTGRES_URI 环境变量未设置")
    
    print(f"📊 开始处理 {len(traffic_data)} 条实时流量数据...")
    
    # 存储到数据库
    try:
        with psycopg.connect(postgres_uri) as conn:
            with conn.cursor() as cur:
                stored_count = 0
                batch_size = 50  # 每批插入50条记录
                batch_data = []
                
                # 按日期和ASIN分组聚合数据
                aggregated_data = {}
                
                print(f"🔍 开始分析数据聚合...")
                print(f"📊 原始数据条数: {len(traffic_data)}")
                
                for item in traffic_data:
                    asin = item.get("asin", "")
                    start_time = item.get("startTime", "")
                    
                    # 只检查asin是否为空
                    if not asin:
                        continue
                    
                    # 提取日期和小时
                    try:
                        dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                        date_key = dt.date()
                        hour = dt.hour
                    except:
                        continue
                    
                    # 构建聚合键：date-asin
                    key = f"{date_key}-{asin}"
                    
                    if key not in aggregated_data:
                        aggregated_data[key] = {
                            "date": date_key,
                            "asin": asin,
                            "hours": {}  # 不预初始化24小时，只存储有数据的小时
                        }
                    
                    # 如果该小时还没有数据，初始化
                    if hour not in aggregated_data[key]["hours"]:
                        aggregated_data[key]["hours"][hour] = {
                            "glance_views": 0,
                            "page_views": 0,
                            "sessions": 0,
                            "units_ordered": 0,
                            "ordered_revenue": 0
                        }
                    
                    # 累加该小时的数据
                    if 0 <= hour < 24:
                        aggregated_data[key]["hours"][hour]["glance_views"] += item.get("glanceViews", 0)
                        aggregated_data[key]["hours"][hour]["page_views"] += item.get("pageViews", 0)
                        aggregated_data[key]["hours"][hour]["sessions"] += item.get("sessions", 0)
                        aggregated_data[key]["hours"][hour]["units_ordered"] += item.get("unitsOrdered", 0)
                        aggregated_data[key]["hours"][hour]["ordered_revenue"] += item.get("orderedRevenue", 0)
                
                print(f"📊 聚合后记录数: {len(aggregated_data)} (原始记录数: {len(traffic_data)})")
                
                for key, data in aggregated_data.items():
                    # 构建主键：company_id-store_id-date-asin
                    company_id_store_id_date_asin = f"{company_id}-{store_id}-{data['date']}-{data['asin']}"
                    
                    # 构建24小时的数据（JSON格式）
                    # 注意：hour_1 = 0:00-1:00, hour_2 = 1:00-2:00, ..., hour_24 = 23:00-24:00
                    values = [
                        company_id_store_id_date_asin,
                        company_id,
                        store_id,
                        marketplace,
                        data['date'],
                        data['asin']
                    ]
                    
                    for hour in range(24):
                        hour_data = data['hours'].get(hour) # 使用 .get() 获取小时数据，如果不存在则为 None
                        if hour_data:
                            hour_json = json.dumps({
                                "glance_views": hour_data['glance_views'],
                                "page_views": hour_data['page_views'],
                                "sessions": hour_data['sessions'],
                                "units_ordered": hour_data['units_ordered'],
                                "ordered_revenue": hour_data['ordered_revenue']
                            })
                            values.append(hour_json)
                        else:
                            values.append(None) # 未出现的小时，添加 None
                    
                    # 添加data_load_time
                    values.append(datetime.now(timezone.utc))
                    
                    batch_data.append(values)
                    
                    # 批量插入
                    if len(batch_data) >= batch_size:
                        placeholders = ', '.join(['%s'] * len(values))
                        cur.executemany(f"""
                            INSERT INTO amazon_data.realtime_traffic_table (
                                company_id_store_id_date_asin, company_id, store_id, marketplace, date, asin,
                                hour_1_glance_views_data, hour_2_glance_views_data, hour_3_glance_views_data,
                                hour_4_glance_views_data, hour_5_glance_views_data, hour_6_glance_views_data,
                                hour_7_glance_views_data, hour_8_glance_views_data, hour_9_glance_views_data,
                                hour_10_glance_views_data, hour_11_glance_views_data, hour_12_glance_views_data,
                                hour_13_glance_views_data, hour_14_glance_views_data, hour_15_glance_views_data,
                                hour_16_glance_views_data, hour_17_glance_views_data, hour_18_glance_views_data,
                                hour_19_glance_views_data, hour_20_glance_views_data, hour_21_glance_views_data,
                                hour_22_glance_views_data, hour_23_glance_views_data, hour_24_glance_views_data,
                                data_load_time
                            ) VALUES ({placeholders})
                            ON CONFLICT (company_id_store_id_date_asin) DO UPDATE SET
                                -- 增量更新：只更新有数据的小时，保留已有的历史数据
                                hour_1_glance_views_data = COALESCE(EXCLUDED.hour_1_glance_views_data, realtime_traffic_table.hour_1_glance_views_data),
                                hour_2_glance_views_data = COALESCE(EXCLUDED.hour_2_glance_views_data, realtime_traffic_table.hour_2_glance_views_data),
                                hour_3_glance_views_data = COALESCE(EXCLUDED.hour_3_glance_views_data, realtime_traffic_table.hour_3_glance_views_data),
                                hour_4_glance_views_data = COALESCE(EXCLUDED.hour_4_glance_views_data, realtime_traffic_table.hour_4_glance_views_data),
                                hour_5_glance_views_data = COALESCE(EXCLUDED.hour_5_glance_views_data, realtime_traffic_table.hour_5_glance_views_data),
                                hour_6_glance_views_data = COALESCE(EXCLUDED.hour_6_glance_views_data, realtime_traffic_table.hour_6_glance_views_data),
                                hour_7_glance_views_data = COALESCE(EXCLUDED.hour_7_glance_views_data, realtime_traffic_table.hour_7_glance_views_data),
                                hour_8_glance_views_data = COALESCE(EXCLUDED.hour_8_glance_views_data, realtime_traffic_table.hour_8_glance_views_data),
                                hour_9_glance_views_data = COALESCE(EXCLUDED.hour_9_glance_views_data, realtime_traffic_table.hour_9_glance_views_data),
                                hour_10_glance_views_data = COALESCE(EXCLUDED.hour_10_glance_views_data, realtime_traffic_table.hour_10_glance_views_data),
                                hour_11_glance_views_data = COALESCE(EXCLUDED.hour_11_glance_views_data, realtime_traffic_table.hour_11_glance_views_data),
                                hour_12_glance_views_data = COALESCE(EXCLUDED.hour_12_glance_views_data, realtime_traffic_table.hour_12_glance_views_data),
                                hour_13_glance_views_data = COALESCE(EXCLUDED.hour_13_glance_views_data, realtime_traffic_table.hour_13_glance_views_data),
                                hour_14_glance_views_data = COALESCE(EXCLUDED.hour_14_glance_views_data, realtime_traffic_table.hour_14_glance_views_data),
                                hour_15_glance_views_data = COALESCE(EXCLUDED.hour_15_glance_views_data, realtime_traffic_table.hour_15_glance_views_data),
                                hour_16_glance_views_data = COALESCE(EXCLUDED.hour_16_glance_views_data, realtime_traffic_table.hour_16_glance_views_data),
                                hour_17_glance_views_data = COALESCE(EXCLUDED.hour_17_glance_views_data, realtime_traffic_table.hour_17_glance_views_data),
                                hour_18_glance_views_data = COALESCE(EXCLUDED.hour_18_glance_views_data, realtime_traffic_table.hour_18_glance_views_data),
                                hour_19_glance_views_data = COALESCE(EXCLUDED.hour_19_glance_views_data, realtime_traffic_table.hour_19_glance_views_data),
                                hour_20_glance_views_data = COALESCE(EXCLUDED.hour_20_glance_views_data, realtime_traffic_table.hour_20_glance_views_data),
                                hour_21_glance_views_data = COALESCE(EXCLUDED.hour_21_glance_views_data, realtime_traffic_table.hour_21_glance_views_data),
                                hour_22_glance_views_data = COALESCE(EXCLUDED.hour_22_glance_views_data, realtime_traffic_table.hour_22_glance_views_data),
                                hour_23_glance_views_data = COALESCE(EXCLUDED.hour_23_glance_views_data, realtime_traffic_table.hour_23_glance_views_data),
                                hour_24_glance_views_data = COALESCE(EXCLUDED.hour_24_glance_views_data, realtime_traffic_table.hour_24_glance_views_data),
                                data_load_time = NOW()
                        """, batch_data)
                        
                        stored_count += len(batch_data)
                        batch_data = []
                        print(f"📊 已存储 {stored_count} 条记录...")
                
                # 插入剩余的数据
                if batch_data:
                    placeholders = ', '.join(['%s'] * len(batch_data[0]))
                    cur.executemany(f"""
                        INSERT INTO amazon_data.realtime_traffic_table (
                            company_id_store_id_date_asin, company_id, store_id, marketplace, date, asin,
                            hour_1_glance_views_data, hour_2_glance_views_data, hour_3_glance_views_data,
                            hour_4_glance_views_data, hour_5_glance_views_data, hour_6_glance_views_data,
                            hour_7_glance_views_data, hour_8_glance_views_data, hour_9_glance_views_data,
                            hour_10_glance_views_data, hour_11_glance_views_data, hour_12_glance_views_data,
                            hour_13_glance_views_data, hour_14_glance_views_data, hour_15_glance_views_data,
                            hour_16_glance_views_data, hour_17_glance_views_data, hour_18_glance_views_data,
                            hour_19_glance_views_data, hour_20_glance_views_data, hour_21_glance_views_data,
                            hour_22_glance_views_data, hour_23_glance_views_data, hour_24_glance_views_data,
                            data_load_time
                        ) VALUES ({placeholders})
                        ON CONFLICT (company_id_store_id_date_asin) DO UPDATE SET
                            hour_1_glance_views_data = COALESCE(EXCLUDED.hour_1_glance_views_data, realtime_traffic_table.hour_1_glance_views_data),
                            hour_2_glance_views_data = COALESCE(EXCLUDED.hour_2_glance_views_data, realtime_traffic_table.hour_2_glance_views_data),
                            hour_3_glance_views_data = COALESCE(EXCLUDED.hour_3_glance_views_data, realtime_traffic_table.hour_3_glance_views_data),
                            hour_4_glance_views_data = COALESCE(EXCLUDED.hour_4_glance_views_data, realtime_traffic_table.hour_4_glance_views_data),
                            hour_5_glance_views_data = COALESCE(EXCLUDED.hour_5_glance_views_data, realtime_traffic_table.hour_5_glance_views_data),
                            hour_6_glance_views_data = COALESCE(EXCLUDED.hour_6_glance_views_data, realtime_traffic_table.hour_6_glance_views_data),
                            hour_7_glance_views_data = COALESCE(EXCLUDED.hour_7_glance_views_data, realtime_traffic_table.hour_7_glance_views_data),
                            hour_8_glance_views_data = COALESCE(EXCLUDED.hour_8_glance_views_data, realtime_traffic_table.hour_8_glance_views_data),
                            hour_9_glance_views_data = COALESCE(EXCLUDED.hour_9_glance_views_data, realtime_traffic_table.hour_9_glance_views_data),
                            hour_10_glance_views_data = COALESCE(EXCLUDED.hour_10_glance_views_data, realtime_traffic_table.hour_10_glance_views_data),
                            hour_11_glance_views_data = COALESCE(EXCLUDED.hour_11_glance_views_data, realtime_traffic_table.hour_11_glance_views_data),
                            hour_12_glance_views_data = COALESCE(EXCLUDED.hour_12_glance_views_data, realtime_traffic_table.hour_12_glance_views_data),
                            hour_13_glance_views_data = COALESCE(EXCLUDED.hour_13_glance_views_data, realtime_traffic_table.hour_13_glance_views_data),
                            hour_14_glance_views_data = COALESCE(EXCLUDED.hour_14_glance_views_data, realtime_traffic_table.hour_14_glance_views_data),
                            hour_15_glance_views_data = COALESCE(EXCLUDED.hour_15_glance_views_data, realtime_traffic_table.hour_15_glance_views_data),
                            hour_16_glance_views_data = COALESCE(EXCLUDED.hour_16_glance_views_data, realtime_traffic_table.hour_16_glance_views_data),
                            hour_17_glance_views_data = COALESCE(EXCLUDED.hour_17_glance_views_data, realtime_traffic_table.hour_17_glance_views_data),
                            hour_18_glance_views_data = COALESCE(EXCLUDED.hour_18_glance_views_data, realtime_traffic_table.hour_18_glance_views_data),
                            hour_19_glance_views_data = COALESCE(EXCLUDED.hour_19_glance_views_data, realtime_traffic_table.hour_19_glance_views_data),
                            hour_20_glance_views_data = COALESCE(EXCLUDED.hour_20_glance_views_data, realtime_traffic_table.hour_20_glance_views_data),
                            hour_21_glance_views_data = COALESCE(EXCLUDED.hour_21_glance_views_data, realtime_traffic_table.hour_21_glance_views_data),
                            hour_22_glance_views_data = COALESCE(EXCLUDED.hour_22_glance_views_data, realtime_traffic_table.hour_22_glance_views_data),
                            hour_23_glance_views_data = COALESCE(EXCLUDED.hour_23_glance_views_data, realtime_traffic_table.hour_23_glance_views_data),
                            hour_24_glance_views_data = COALESCE(EXCLUDED.hour_24_glance_views_data, realtime_traffic_table.hour_24_glance_views_data),
                            data_load_time = NOW()
                    """, batch_data)
                    
                    stored_count += len(batch_data)
                
                conn.commit()
                print(f"✅ 成功存储 {stored_count} 条实时流量记录到数据库")
                return stored_count
                
    except Exception as e:
        print(f"❌ 数据库存储失败: {e}")
        raise e


def main():
    """主函数，用于直接运行测试"""
    import sys
    import os
    
    # 添加项目根目录到路径
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
    
    print('🚀 Amazon Vendor Real-time Traffic Report 测试工具')
    print('=' * 60)
    print('选择测试模式:')
    print('1. Mock数据插入测试')
    print('2. 真实API调用测试')
    print()
    
    choice = input('请输入选择 (1 或 2): ').strip()
    
    if choice == '1':
        return test_mock_database_insert()
    elif choice == '2':
        return test_real_api_call()
    else:
        print('❌ 无效选择')
        return False


def test_mock_database_insert():
    """测试Mock数据插入"""
    try:
        print('🧪 开始Mock数据插入测试...')
        
        # 模拟实时流量数据
        mock_traffic_data = [
            {
                "asin": "B0B9LCR8V1",
                "startTime": "2025-01-01T10:00:00Z",
                "endTime": "2025-01-01T11:00:00Z",
                "pageViews": 150,
                "sessions": 120,
                "unitsOrdered": 5,
                "orderedRevenue": 125.50,
                "currency": "USD"
            },
            {
                "asin": "B0B9LCR8V2",
                "startTime": "2025-01-01T10:00:00Z",
                "endTime": "2025-01-01T11:00:00Z",
                "pageViews": 200,
                "sessions": 180,
                "unitsOrdered": 3,
                "orderedRevenue": 89.99,
                "currency": "USD"
            }
        ]
        
        stored_count = store_realtime_traffic_report_to_database(
            mock_traffic_data,
            "TEST_COMPANY_001",
            "TEST_STORE_001",
            "US"
        )
        
        print(f'✅ Mock数据插入成功，存储了 {stored_count} 条记录')
        return 0
        
    except Exception as e:
        print(f'❌ Mock数据插入失败: {e}')
        return False


def test_real_api_call():
    """测试真实API调用"""
    try:
        print('🔍 开始真实API调用测试...')
        result = amazon_vendor_realtime_traffic_report.func(
            company_id="TEST_COMPANY_001",
            store_id="TEST_STORE_001",
            marketplace="US"
        )
        data = json.loads(result)
        
        if data['success']:
            print()
            print('🎉 成功获取供应商实时流量报告！')
            print('-' * 40)
            
            # 基本信息
            operation_id = data.get('operation_id', 'N/A')
            operation_time = data.get('operation_time', 'N/A')
            database_stored = data.get('database_stored', 0)
            
            print(f'📄 操作ID: {operation_id}')
            print(f'⏰ 操作时间: {operation_time[:19]}' if operation_time != 'N/A' else '⏰ 操作时间: N/A')
            print(f'💾 存储记录数: {database_stored}')
            
            # 报告信息
            report_id = data.get('report_id', 'N/A')
            document_id = data.get('document_id', 'N/A')
            print(f'📋 报告ID: {report_id}')
            print(f'📄 文档ID: {document_id}')
            
            # 时间范围
            time_range = data.get('time_range', {})
            if time_range:
                start_time = time_range.get('start', 'N/A')
                end_time = time_range.get('end', 'N/A')
                print(f'📅 开始时间: {start_time[:19]}' if start_time != 'N/A' else '📅 开始时间: N/A')
                print(f'📅 结束时间: {end_time[:19]}' if end_time != 'N/A' else '📅 结束时间: N/A')
            
            # 数据统计
            if 'data' in data and 'all_data' in data['data']:
                all_data = data['data']['all_data']
                print(f'📊 总记录数: {len(all_data)}')
            
            print()
            print('✅ 实时流量报告获取完成！')
            return 0
            
        else:
            print()
            print('❌ 获取实时流量报告失败')
            print('-' * 40)
            print(f'错误信息: {data["error"]}')
            print(f'错误类型: {data.get("error_type", "UNKNOWN")}')
            
            return 1
            
    except Exception as e:
        print(f'❌ 真实API调用测试失败: {e}')
        return 1


if __name__ == "__main__":
    main()
