#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Coupon Performance Report Tool

独立的优惠券表现报告工具，从拉取报告到解析数据的完整流程。
"""

from __future__ import annotations

import json
import logging
import sys
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Annotated, Any, Dict, List, Optional, Union

from langchain_core.tools import tool
from src.tools.decorators import log_io

# 导入common模块
from src.tools.amazon.sp_api.common.reports import ReportsClient, ReportRequest, iso_utc
from src.tools.amazon.sp_api.common.sp_config import AmazonSPConfig

logger = logging.getLogger(__name__)

# 市场映射表：前端marketplace -> Amazon marketplace_ids
MARKETPLACE_MAPPING = {
    # 北美
    "US": "ATVPDKIKX0DER",   # 美国
    "CA": "A2EUQ1WTGCTBG2",  # 加拿大
    "MX": "A1AM78C64UM0Y8",  # 墨西哥
    "BR": "A2Q3Y263D00KWC",  # 巴西

    # 欧洲
    "UK": "A1F83G8C2ARO7P",  # 英国
    "DE": "A1PA6795UKMFR9",  # 德国
    "FR": "A13V1IB3VIYZZH",  # 法国
    "IT": "APJ6JRA9NG5V4",   # 意大利
    "ES": "A1RKKUPIHCS9HS",  # 西班牙
    "NL": "A1805IZSGTT6HS",  # 荷兰
    "SE": "A2NODRKZP88ZB9",  # 瑞典
    "PL": "A1C3SOZRARQ6R3",  # 波兰
    "TR": "A33AVAJ2PDY3EV",  # 土耳其
    "EG": "ARBP9OOSHTCHU",   # 埃及

    # 亚太
    "JP": "A1VC38T7YXB528",  # 日本
    "AU": "A39IBJ37TRP1C6",  # 澳大利亚
    "SG": "A19VAU5U5O7RUS",  # 新加坡
    "IN": "A21TJRUUN4KGV",   # 印度

    # 中东
    "AE": "A2VIGQ35RCS4UG",  # 阿联酋
    "SA": "A17E79C6D8DWNP",  # 沙特阿拉伯
}


@tool(description="获取亚马逊优惠券表现报告数据。支持指定日期范围，自动处理报告创建、状态检查和数据解析。返回结构化的优惠券数据，包含活动统计、优惠券表现、ASIN分析等关键指标。")
@log_io
def amazon_coupon_performance_report(
    company_id: Annotated[str, "前端传递的公司ID"],
    store_id: Annotated[str, "前端传递的店铺ID"],
    marketplace: Annotated[str, "前端传递的市场代码，如US、UK、DE等"] = "US",
) -> str:
    """
    获取亚马逊优惠券表现报告数据
    
    Args:
        company_id: 前端传递的公司ID
        store_id: 前端传递的店铺ID
        start_date: 开始日期，格式：YYYY-MM-DD
        end_date: 结束日期，格式：YYYY-MM-DD
        marketplace: 市场代码，如US、UK、DE等
        wait_timeout: 等待超时时间（秒）
        output_dir: 输出目录
        
    Returns:
        JSON格式的优惠券报告数据
    """
    try:
        # 参数验证
        if not company_id:
            return json.dumps({
                "success": False,
                "error": "缺少必要参数: company_id",
                "error_type": "MISSING_COMPANY_ID"
            }, ensure_ascii=False, indent=2)
        
        if not store_id:
            return json.dumps({
                "success": False,
                "error": "缺少必要参数: store_id",
                "error_type": "MISSING_STORE_ID"
            }, ensure_ascii=False, indent=2)
        
        # 固定获取最近30天的数据（忽略外部传参）
        end_date_dt = datetime.now(timezone.utc) - timedelta(days=1)  # 昨天作为结束日期
        start_date_dt = end_date_dt - timedelta(days=29)  # 30天前作为开始日期
        start_date = start_date_dt.strftime("%Y-%m-%d")
        end_date = end_date_dt.strftime("%Y-%m-%d")
        
        if not marketplace:
            return json.dumps({
                "success": False,
                "error": "缺少必要参数: marketplace",
                "error_type": "MISSING_MARKETPLACE"
            }, ensure_ascii=False, indent=2)
        
        # 初始化配置
        config = AmazonSPConfig()
        reports_client = ReportsClient(config)
        
        # 处理市场ID
        marketplace_ids = [MARKETPLACE_MAPPING.get(marketplace.upper(), "ATVPDKIKX0DER")]
        
        # 创建报告
        logger.info(f"创建优惠券表现报告，日期范围: {start_date} - {end_date}")
        
        # 优惠券报告需要特殊的日期范围（活动开始日期）
        # 通常活动开始日期比报告日期早很多
        start_datetime = datetime.strptime(f"{start_date}T00:00:00Z", "%Y-%m-%dT%H:%M:%SZ")
        end_datetime = datetime.strptime(f"{end_date}T23:59:59Z", "%Y-%m-%dT%H:%M:%SZ")
        
        # 活动开始日期范围（通常比报告日期早60-90天）
        campaign_start_from = (start_datetime - timedelta(days=90)).strftime("%Y-%m-%dT%H:%M:%SZ")
        campaign_start_to = end_datetime.strftime("%Y-%m-%dT%H:%M:%SZ")
        
        # 让数据统计时间与活动筛选时间保持一致，确保获取每个活动的完整期间数据
        data_start_time = campaign_start_from
        data_end_time = campaign_start_to
        
        logger.info(f"用户请求时间: {start_date} 到 {end_date}")
        logger.info(f"数据统计时间: {data_start_time} 到 {data_end_time}")
        logger.info(f"活动筛选时间: {campaign_start_from} 到 {campaign_start_to}")
        
        report_request = ReportRequest(
            report_type="GET_COUPON_PERFORMANCE_REPORT",
            data_start_time=data_start_time,
            data_end_time=data_end_time,
            marketplace_ids=marketplace_ids,
            report_options={
                "reportPeriod": "DAY",
                "campaignStartDateFrom": campaign_start_from,
                "campaignStartDateTo": campaign_start_to
            }
        )
        
        # 创建报告
        try:
            report_id = reports_client.create(report_request)
            logger.info(f"报告创建成功，报告ID: {report_id}")
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"报告创建失败: {str(e)}",
                "details": {
                    "start_date": start_date,
                    "end_date": end_date,
                    "report_type": "GET_COUPON_PERFORMANCE_REPORT"
                }
            }, ensure_ascii=False)
        
        # 等待报告完成
        logger.info(f"等待报告完成，报告ID: {report_id}")
        try:
            document_id = reports_client.wait_done(report_id, timeout_seconds=300)
            logger.info(f"报告处理完成，文档ID: {document_id}")
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"报告处理超时或失败: {str(e)}",
                "details": {
                    "report_id": report_id,
                    "timeout": 300
                }
            }, ensure_ascii=False)
        
        # 下载报告
        logger.info(f"下载报告文档: {document_id}")
        try:
            download_url = reports_client.get_download_url(document_id)
            logger.info(f"获取下载URL成功: {download_url}")
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"获取下载URL失败: {str(e)}",
                "details": {
                    "document_id": document_id
                }
            }, ensure_ascii=False)
        
        # 下载文件
        try:
            output_path = Path("reports") / f"get_coupon_performance_report_report.json"
            
            # 如果文件已存在，先删除旧文件
            if output_path.exists():
                output_path.unlink()
                logger.info(f"已删除旧报告文件: {output_path}")
            
            reports_client.download_to_file(download_url, output_path)
            logger.info(f"报告下载完成: {output_path}")
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"报告下载失败: {str(e)}",
                "details": {
                    "document_id": document_id,
                    "download_url": download_url
                }
            }, ensure_ascii=False)
        
        # 解析报告数据
        logger.info(f"解析报告数据: {str(output_path)}")
        parsed_data = parse_coupon_performance_report(str(output_path))
        
        if not parsed_data["success"]:
            return json.dumps({
                "success": False,
                "error": f"报告解析失败: {parsed_data['error']}",
                "details": {
                    "file_path": str(output_path)
                }
            }, ensure_ascii=False)
        
        # 存储数据到数据库
        try:
            stored_count = store_coupon_data_to_database(
                parsed_data["data"]["full_data"], 
                company_id, 
                store_id, 
                marketplace
            )
            logger.info(f"成功存储 {stored_count} 条优惠券记录到数据库")
        except Exception as e:
            logger.error(f"数据库存储失败: {e}")
            stored_count = 0
        
        # 返回成功结果
        result = {
            "success": True,
            "data_source": "AMAZON_COUPON_PERFORMANCE_REPORT",
            "operation_id": report_id,
            "operation_time": datetime.now().isoformat(),
            "database_stored": stored_count,
            "report_type": "GET_COUPON_PERFORMANCE_REPORT",
            "report_id": report_id,
            "document_id": document_id,
            "date_range": {
                "start": start_date,
                "end": end_date
            },
            "marketplace_ids": marketplace_ids,
            "file_path": str(output_path),
            "file_size": output_path.stat().st_size if output_path.exists() else 0,
            "data": parsed_data["data"],
            "execution_time": datetime.now().isoformat()
        }
        
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"优惠券表现报告工具执行失败: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "details": {
                "start_date": start_date,
                "end_date": end_date,
                "marketplace": marketplace
            }
        }, ensure_ascii=False)


def parse_coupon_performance_report(file_path: str) -> Dict[str, Any]:
    """
    解析优惠券表现报告数据
    
    Args:
        file_path: 报告文件路径
        
    Returns:
        解析后的数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        # 提取报告规格和数据
        report_spec = report_data.get("reportSpecification", {})
        campaigns = report_data.get("campaigns", [])
        
        if not campaigns:
            return {
                "success": False,
                "error": "报告数据为空"
            }
        
        # 统计所有优惠券
        total_campaigns = len(campaigns)
        total_coupons = 0
        total_clips = 0
        total_redemptions = 0
        total_budget = 0
        total_budget_spent = 0
        total_discount = 0
        
        # 完整数据存储（基于couponId维度）
        full_coupon_data = []
        
        for campaign in campaigns:
            total_clips += campaign.get("totalClips", 0)
            total_redemptions += campaign.get("totalRedemptions", 0)
            
            # 统计优惠券详情
            for coupon in campaign.get("coupons", []):
                total_coupons += 1
                total_budget += coupon.get("budget", 0)
                total_budget_spent += coupon.get("budgetSpent", 0)
                total_discount += coupon.get("totalDiscount", 0)
                
                # 基于couponId维度创建记录
                for asin_info in coupon.get("asins", []):
                    asin = asin_info.get("asin", "")
                    if asin:
                        full_record = {
                            # 报告基本信息
                            "report_type": "GET_COUPON_PERFORMANCE_REPORT",
                            "report_date_from": report_spec.get("reportOptions", {}).get("campaignStartDateFrom"),
                            "report_date_to": report_spec.get("reportOptions", {}).get("campaignStartDateTo"),
                            "marketplace_ids": report_spec.get("marketplaceIds", []),
                            
                            # 活动信息（公共数据，复制到每个coupon记录）
                            "campaign_id": campaign.get("campaignId"),
                            "campaign_name": campaign.get("campaignName"),
                            "vendor_code": campaign.get("vendorCode"),
                            "marketplace_id": campaign.get("marketplaceId"),
                            "creation_date_time": campaign.get("creationDateTime"),
                            "last_updated_date_time": campaign.get("lastUpdatedDateTime"),
                            "is_subscribe_and_save": campaign.get("isSubscribeAndSave"),
                            "budget_type": campaign.get("budgetType"),
                            "total_clips": campaign.get("totalClips"),
                            "total_redemptions": campaign.get("totalRedemptions"),
                            "currency_code": campaign.get("currencyCode"),
                            
                            # 优惠券信息（基于couponId维度）
                            "coupon_id": coupon.get("couponId"),
                            "coupon_name": coupon.get("name"),
                            "website_message": coupon.get("websiteMessage"),
                            "start_date_time": coupon.get("startDateTime"),
                            "end_date_time": coupon.get("endDateTime"),
                            "is_once_per_customer": coupon.get("isOncePerCustomer"),
                            "clips": coupon.get("clips"),
                            "redemptions": coupon.get("redemptions"),
                            "budget": coupon.get("budget"),
                            "total_discount": coupon.get("totalDiscount"),
                            "budget_spent": coupon.get("budgetSpent"),
                            "budget_remaining": coupon.get("budgetRemaining"),
                            "budget_percentage_used": coupon.get("budgetPercentageUsed"),
                            
                            # ASIN信息
                            "asin": asin,
                            "discount_type": asin_info.get("discountType"),
                            "discount_amount": asin_info.get("discountAmount"),
                            
                            # 解析时间戳
                            "parsed_at": datetime.now().isoformat()
                        }
                        full_coupon_data.append(full_record)
        
        # 构建返回数据
        parsed_data = {
            "report_type": "GET_COUPON_PERFORMANCE_REPORT",
            "date_range": {
                "start": report_spec.get("reportOptions", {}).get("campaignStartDateFrom"),
                "end": report_spec.get("reportOptions", {}).get("campaignStartDateTo")
            },
            "marketplace_ids": report_spec.get("marketplaceIds", []),
            "summary": {
                "total_campaigns": total_campaigns,
                "total_coupons": total_coupons,
                "total_clips": total_clips,
                "total_redemptions": total_redemptions,
                "total_budget": total_budget,
                "total_budget_spent": total_budget_spent,
                "total_discount": total_discount,
                "budget_utilization_rate": (total_budget_spent / total_budget * 100) if total_budget > 0 else 0,
                "redemption_rate": (total_redemptions / total_clips * 100) if total_clips > 0 else 0
            },
            "full_data": full_coupon_data,  # 返回所有记录
            "total_records": len(full_coupon_data),
            "total_available_records": len(full_coupon_data)  # 总可用记录数
        }
        
        return {
            "success": True,
            "data": parsed_data
        }
        
    except Exception as e:
        logger.error(f"解析优惠券表现报告失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def store_coupon_data_to_database(coupon_data: List[Dict], company_id: str, store_id: str, marketplace: str) -> int:
    """
    将优惠券数据存储到数据库
    
    Args:
        coupon_data: 优惠券数据列表
        company_id: 公司ID
        store_id: 店铺ID
        marketplace: 市场代码
        
    Returns:
        存储的记录数
    """
    if not coupon_data:
        return 0
    
    import os
    import psycopg
    
    postgres_uri = os.getenv("POSTGRES_URI")
    if not postgres_uri:
        raise ValueError("POSTGRES_URI 环境变量未设置")
    
    print(f"📊 开始处理 {len(coupon_data)} 条优惠券数据...")
    
    # 存储到数据库
    try:
        with psycopg.connect(postgres_uri) as conn:
            with conn.cursor() as cur:
                stored_count = 0
                batch_size = 50  # 每批插入50条记录
                batch_data = []
                
                # 去重处理，只保留唯一的主键记录
                unique_items = {}
                for item in coupon_data:
                    asin = item.get("asin", "")
                    coupon_id = item.get("coupon_id", "")
                    
                    if not asin or not coupon_id:
                        continue
                    
                    # 构建主键
                    company_id_store_id_coupon_id_asin = f"{company_id}-{store_id}-{coupon_id}-{asin}"
                    
                    # 只保留最新的记录（如果有重复）
                    unique_items[company_id_store_id_coupon_id_asin] = item
                
                print(f"📊 去重后记录数: {len(unique_items)} (原始记录数: {len(coupon_data)})")
                
                for item in unique_items.values():
                    
                    # 处理时间字段
                    start_date_time = None
                    end_date_time = None
                    
                    if item.get("start_date_time"):
                        try:
                            start_date_time = datetime.fromisoformat(item["start_date_time"].replace('Z', '+00:00'))
                        except:
                            pass
                            
                    if item.get("end_date_time"):
                        try:
                            end_date_time = datetime.fromisoformat(item["end_date_time"].replace('Z', '+00:00'))
                        except:
                            pass
                    
                    # 构建数据
                    campaign_id = item.get("campaign_id", "")
                    coupon_id = item.get("coupon_id", "")
                    coupon_name = item.get("coupon_name", "")
                    asin = item.get("asin", "")
                    
                    # 重新构建主键（确保每个记录都有正确的主键）
                    company_id_store_id_coupon_id_asin = f"{company_id}-{store_id}-{coupon_id}-{asin}"
                    
                    values = [
                        company_id_store_id_coupon_id_asin,  # 1. company_id_store_id_coupon_id_asin
                        company_id,                          # 2. company_id
                        store_id,                            # 3. store_id
                        marketplace,                         # 4. marketplace
                        asin,                                # 5. asin
                        campaign_id,                         # 6. campaign_id
                        coupon_id,                           # 7. coupon_id
                        coupon_name,                         # 8. coupon_name
                        start_date_time,                     # 9. start_date_time
                        end_date_time,                       # 10. end_date_time
                        item.get("discount_amount", 0),      # 11. discount_amount
                        item.get("clips", 0),                # 12. coupon_clips (API返回的是clips)
                        item.get("redemptions", 0),          # 13. coupon_redemptions (API返回的是redemptions)
                        item.get("total_discount", 0),       # 14. total_discount
                        item.get("budget_spent", 0),         # 15. budget_spent
                        item.get("budget_remaining", 0),     # 16. budget_remaining
                        item.get("budget_percentage_used", 0), # 17. budget_percentage_used
                        item.get("currency_code", ""),       # 18. currency_code
                        item.get("budget", 0),               # 19. budget
                        datetime.now(timezone.utc)           # 20. data_load_time
                    ]
                    
                    batch_data.append(values)
                    
                    # 批量插入
                    if len(batch_data) >= batch_size:
                        cur.executemany("""
                            INSERT INTO amazon_data.coupon_table (
                                company_id_store_id_coupon_id_asin, company_id, store_id, marketplace, asin,
                                campaign_id, coupon_id, coupon_name, start_date_time, end_date_time,
                                discount_amount, coupon_clips, coupon_redemptions, total_discount,
                                budget_spent, budget_remaining, budget_percentage_used, currency_code, budget,
                                data_load_time
                            ) VALUES (
                                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                            )
                            ON CONFLICT (company_id_store_id_coupon_id_asin) DO UPDATE SET
                                coupon_name = EXCLUDED.coupon_name,
                                start_date_time = EXCLUDED.start_date_time,
                                end_date_time = EXCLUDED.end_date_time,
                                discount_amount = EXCLUDED.discount_amount,
                                coupon_clips = EXCLUDED.coupon_clips,
                                coupon_redemptions = EXCLUDED.coupon_redemptions,
                                total_discount = EXCLUDED.total_discount,
                                budget_spent = EXCLUDED.budget_spent,
                                budget_remaining = EXCLUDED.budget_remaining,
                                budget_percentage_used = EXCLUDED.budget_percentage_used,
                                currency_code = EXCLUDED.currency_code,
                                budget = EXCLUDED.budget,
                                data_load_time = NOW()
                        """, batch_data)
                        
                        stored_count += len(batch_data)
                        batch_data = []
                        print(f"📊 已存储 {stored_count} 条记录...")
                
                # 插入剩余的数据
                if batch_data:
                    cur.executemany("""
                        INSERT INTO amazon_data.coupon_table (
                            company_id_store_id_coupon_id_asin, company_id, store_id, marketplace, asin,
                            campaign_id, coupon_id, coupon_name, start_date_time, end_date_time,
                            discount_amount, coupon_clips, coupon_redemptions, total_discount,
                            budget_spent, budget_remaining, budget_percentage_used, currency_code, budget,
                            data_load_time
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                        )
                        ON CONFLICT (company_id_store_id_coupon_id_asin) DO UPDATE SET
                            coupon_name = EXCLUDED.coupon_name,
                            start_date_time = EXCLUDED.start_date_time,
                            end_date_time = EXCLUDED.end_date_time,
                            discount_amount = EXCLUDED.discount_amount,
                            coupon_clips = EXCLUDED.coupon_clips,
                            coupon_redemptions = EXCLUDED.coupon_redemptions,
                            total_discount = EXCLUDED.total_discount,
                            budget_spent = EXCLUDED.budget_spent,
                            budget_remaining = EXCLUDED.budget_remaining,
                            budget_percentage_used = EXCLUDED.budget_percentage_used,
                            currency_code = EXCLUDED.currency_code,
                            budget = EXCLUDED.budget,
                            data_load_time = NOW()
                    """, batch_data)
                    
                    stored_count += len(batch_data)
                
                conn.commit()
                print(f"✅ 成功存储 {stored_count} 条优惠券记录到数据库")
                return stored_count
                
    except Exception as e:
        print(f"❌ 数据库存储失败: {e}")
        raise e


def main():
    """主函数，用于直接运行测试"""
    import sys
    import os
    
    # 添加项目根目录到路径
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
    
    print('🚀 Amazon Coupon Performance Report 测试工具')
    print('=' * 60)
    print('选择测试模式:')
    print('1. Mock数据插入测试')
    print('2. 真实API调用测试')
    print()
    
    choice = input('请输入选择 (1 或 2): ').strip()
    
    if choice == '1':
        return test_mock_database_insert()
    elif choice == '2':
        return test_real_api_call()
    else:
        print('❌ 无效选择')
        return False


def test_mock_database_insert():
    """测试mock数据插入"""
    import json
    from datetime import datetime
    
    print('🧪 开始测试Mock数据插入...')
    print('=' * 50)
    
    # 创建mock数据
    mock_data = [
        {
            "campaign_id": "TEST_CAMPAIGN_001",
            "coupon_id": "TEST_COUPON_001",
            "coupon_name": "测试优惠券1",
            "asin": "B09Q39ZY44",
            "start_date_time": "2025-08-01T00:00:00Z",
            "end_date_time": "2025-08-31T23:59:59Z",
            "discount_amount": 5.00,
            "clips": 150,
            "redemptions": 75,
            "total_discount": 375.00,
            "budget_spent": 500.00,
            "budget_remaining": 500.00,
            "budget_percentage_used": 50.00,
            "currency_code": "USD",
            "budget": 1000.00
        },
        {
            "campaign_id": "TEST_CAMPAIGN_002",
            "coupon_id": "TEST_COUPON_002",
            "coupon_name": "测试优惠券2",
            "asin": "B08XYZ1234",
            "start_date_time": "2025-08-01T00:00:00Z",
            "end_date_time": "2025-08-31T23:59:59Z",
            "discount_amount": 3.00,
            "clips": 200,
            "redemptions": 120,
            "total_discount": 360.00,
            "budget_spent": 400.00,
            "budget_remaining": 600.00,
            "budget_percentage_used": 40.00,
            "currency_code": "USD",
            "budget": 1000.00
        }
    ]
    
    try:
        # 调用存储函数
        print('💾 插入Mock数据...')
        stored_count = store_coupon_data_to_database(
            mock_data, 
            "TEST_COMPANY_001", 
            "TEST_STORE_001", 
            "US"
        )
        
        print()
        print('✅ Mock数据插入测试完成！')
        print(f'📊 成功插入 {stored_count} 条记录')
        print(f'🏢 企业ID: TEST_COMPANY_001')
        print(f'🏪 店铺ID: TEST_STORE_001')
        print(f'🌍 市场: US')
        print()
        print('🔍 你可以在数据库中查看:')
        print('   Schema: amazon_data')
        print('   Table: coupon_table')
        
        return True
        
    except Exception as e:
        print(f'❌ Mock数据插入失败: {e}')
        return False


def test_real_api_call():
    """测试真实API调用"""
    try:
        print('🔍 开始真实API调用测试...')
        print('📋 测试参数:')
        print('  - 企业ID: TEST_COMPANY_001')
        print('  - 店铺ID: TEST_STORE_001')
        print('  - 日期: 使用固定最近30天')
        print('  - 市场: US')
        print()
        
        # 直接调用工具函数
        result = amazon_coupon_performance_report.invoke({
            "company_id": "TEST_COMPANY_001",
            "store_id": "TEST_STORE_001",
            "marketplace": "US"
        })
        data = json.loads(result)
        
        if data['success']:
            print()
            print('🎉 成功获取优惠券表现报告！')
            print('-' * 40)
            
            # 基本信息
            print(f'📄 报告ID: {data.get("report_id", "N/A")}')
            print(f'📄 文档ID: {data.get("document_id", "N/A")}')
            print(f'📁 文件路径: {data.get("file_path", "N/A")}')
            print(f'💾 文件大小: {data.get("file_size", 0):,} bytes')
            
            # 如果有解析的数据，显示汇总信息
            if 'data' in data and 'summary' in data['data']:
                summary = data['data']['summary']
                print()
                print('📊 数据汇总:')
                print('-' * 40)
                print(f'🎯 总活动数: {summary.get("total_campaigns", 0):,}')
                print(f'🎫 总优惠券数: {summary.get("total_coupons", 0):,}')
                print(f'📎 总领取次数: {summary.get("total_clips", 0):,}')
                print(f'💳 总兑换次数: {summary.get("total_redemptions", 0):,}')
                print(f'💰 总预算: ${summary.get("total_budget", 0):,.2f}')
                print(f'💸 总花费: ${summary.get("total_budget_spent", 0):,.2f}')
                print(f'🎁 总折扣: ${summary.get("total_discount", 0):,.2f}')
                print(f'📈 预算使用率: {summary.get("budget_utilization_rate", 0):,.2f}%')
                print(f'🔄 兑换率: {summary.get("redemption_rate", 0):,.2f}%')
                print(f'📝 总记录数: {data["data"].get("total_records", 0):,}')
            
            print()
            print('✅ 报告获取完成！数据已保存到本地文件。')
            return 0
            
        else:
            print()
            print('❌ 获取优惠券报告失败')
            print('-' * 40)
            print(f'错误信息: {data["error"]}')
            print(f'错误类型: {data.get("error_type", "UNKNOWN")}')
            
            print()
            print('💡 可能的原因:')
            print('  - 请求的日期范围内没有优惠券数据')
            print('  - Amazon API权限不足')
            print('  - 账户配置问题')
            print('  - 优惠券活动已结束')
            
            print()
            print('🔧 建议:')
            print('  - 检查日期范围是否正确')
            print('  - 确认Amazon SP API配置')
            print('  - 联系Amazon支持确认账户状态')
            
            return 1
            
    except KeyboardInterrupt:
        print()
        print('⚠️  操作被用户中断')
        return 1
    except Exception as e:
        print()
        print(f'❌ 程序执行出错: {e}')
        return 1


if __name__ == "__main__":
    sys.exit(main())