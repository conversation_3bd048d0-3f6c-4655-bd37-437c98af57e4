#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Vendor Real-time Inventory Report Tool

独立的供应商实时库存报告工具，从拉取报告到解析数据的完整流程。
"""

from __future__ import annotations

import json
import logging
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Annotated, Any, Dict, List, Optional, Union

from langchain_core.tools import tool
from src.tools.decorators import log_io

# 导入common模块
from src.tools.amazon.sp_api.common.reports import ReportsClient, ReportRequest, iso_utc
from src.tools.amazon.sp_api.common.sp_config import AmazonSPConfig

logger = logging.getLogger(__name__)


@tool(description="获取亚马逊供应商实时库存报告数据。支持指定日期范围，自动处理报告创建、状态检查和数据解析。返回结构化的实时库存数据，包含库存水平、ASIN状态等关键指标。")
@log_io
def amazon_vendor_realtime_inventory_report(
    start_date: Annotated[str, "开始日期，格式：YYYY-MM-DD"] = None,
    end_date: Annotated[str, "结束日期，格式：YYYY-MM-DD"] = None,
    date: Annotated[str, "单日日期，格式：YYYY-MM-DD，如果指定则忽略start_date和end_date"] = None,
    marketplace_ids: Annotated[Optional[str], "市场ID列表，逗号分隔，默认ATVPDKIKX0DER"] = "ATVPDKIKX0DER",
    wait_timeout: Annotated[int, "等待超时时间（秒），默认300"] = 300,
    output_dir: Annotated[str, "输出目录，默认reports"] = "reports",
) -> str:
    """
    获取亚马逊供应商实时库存报告数据
    
    Args:
        start_date: 开始日期，格式：YYYY-MM-DD
        end_date: 结束日期，格式：YYYY-MM-DD
        date: 单日日期，格式：YYYY-MM-DD
        marketplace_ids: 市场ID列表，逗号分隔
        wait_timeout: 等待超时时间（秒）
        output_dir: 输出目录
        
    Returns:
        JSON格式的实时库存报告数据
    """
    try:
        # 初始化配置
        config = AmazonSPConfig()
        reports_client = ReportsClient(config)
        
        # 处理日期参数
        if date:
            start_date = date
            end_date = date
        elif not start_date or not end_date:
            # 默认获取今天的数据（实时报告）
            today = datetime.now(timezone.utc)
            start_date = today.strftime("%Y-%m-%d")
            end_date = start_date
        
        # 处理市场ID
        if isinstance(marketplace_ids, str):
            marketplace_ids = [mid.strip() for mid in marketplace_ids.split(",")]
        
        # 创建报告
        logger.info(f"创建供应商实时库存报告，日期范围: {start_date} - {end_date}")
        
        report_request = ReportRequest(
            report_type="GET_VENDOR_REAL_TIME_INVENTORY_REPORT",
            data_start_time=datetime.strptime(f"{start_date}T00:00:00Z", "%Y-%m-%dT%H:%M:%SZ").isoformat().replace("+00:00", "Z"),
            data_end_time=datetime.strptime(f"{end_date}T23:59:59Z", "%Y-%m-%dT%H:%M:%SZ").isoformat().replace("+00:00", "Z"),
            marketplace_ids=marketplace_ids
        )
        
        # 创建报告
        try:
            report_id = reports_client.create(report_request)
            logger.info(f"报告创建成功，报告ID: {report_id}")
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"报告创建失败: {str(e)}",
                "details": {
                    "start_date": start_date,
                    "end_date": end_date,
                    "report_type": "GET_VENDOR_REAL_TIME_INVENTORY_REPORT"
                }
            }, ensure_ascii=False)
        
        # 等待报告完成
        logger.info(f"等待报告完成，报告ID: {report_id}")
        try:
            document_id = reports_client.wait_done(report_id, timeout_seconds=wait_timeout)
            logger.info(f"报告处理完成，文档ID: {document_id}")
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"报告处理超时或失败: {str(e)}",
                "details": {
                    "report_id": report_id,
                    "timeout": wait_timeout
                }
            }, ensure_ascii=False)
        
        # 下载报告
        logger.info(f"下载报告文档: {document_id}")
        try:
            download_url = reports_client.get_download_url(document_id)
            logger.info(f"获取下载URL成功: {download_url}")
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"获取下载URL失败: {str(e)}",
                "details": {
                    "document_id": document_id
                }
            }, ensure_ascii=False)
        
        # 下载文件
        try:
            output_path = Path(output_dir) / f"get_vendor_real_time_inventory_report_report.json"
            reports_client.download_to_file(download_url, output_path)
            logger.info(f"报告下载完成: {output_path}")
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"报告下载失败: {str(e)}",
                "details": {
                    "document_id": document_id,
                    "download_url": download_url
                }
            }, ensure_ascii=False)
        
        # 解析报告数据
        logger.info(f"解析报告数据: {str(output_path)}")
        parsed_data = parse_vendor_realtime_inventory_report(str(output_path))
        
        if not parsed_data["success"]:
            return json.dumps({
                "success": False,
                "error": f"报告解析失败: {parsed_data['error']}",
                "details": {
                    "file_path": str(output_path)
                }
            }, ensure_ascii=False)
        
        # 返回成功结果
        result = {
            "success": True,
            "report_type": "GET_VENDOR_REAL_TIME_INVENTORY_REPORT",
            "report_id": report_id,
            "document_id": document_id,
            "date_range": {
                "start": start_date,
                "end": end_date
            },
            "marketplace_ids": marketplace_ids,
            "file_path": str(output_path),
            "file_size": output_path.stat().st_size if output_path.exists() else 0,
            "data": parsed_data["data"],
            "execution_time": datetime.now().isoformat()
        }
        
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"供应商实时库存报告工具执行失败: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "details": {
                "start_date": start_date,
                "end_date": end_date,
                "marketplace_ids": marketplace_ids
            }
        }, ensure_ascii=False)


def parse_vendor_realtime_inventory_report(file_path: str) -> Dict[str, Any]:
    """
    解析供应商实时库存报告数据
    
    Args:
        file_path: 报告文件路径
        
    Returns:
        解析后的数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        # 提取报告规格和数据
        report_spec = report_data.get("reportSpecification", {})
        report_data_list = report_data.get("inventoryData", [])
        
        if not report_data_list:
            return {
                "success": False,
                "error": "报告数据为空"
            }
        
        # 统计总数据
        total_records = len(report_data_list)
        total_quantity = sum(item.get("quantity", 0) for item in report_data_list)
        total_asins = len(set(item.get("asin", "") for item in report_data_list if item.get("asin")))
        
        # 按ASIN分组统计
        asin_stats = {}
        for item in report_data_list:
            asin = item.get("asin", "")
            if asin:
                if asin not in asin_stats:
                    asin_stats[asin] = {
                        "quantity": 0,
                        "fulfillment_type": item.get("fulfillmentType", ""),
                        "condition": item.get("condition", ""),
                        "last_updated": item.get("lastUpdatedTime", "")
                    }
                asin_stats[asin]["quantity"] += item.get("quantity", 0)
        
        # 按库存状态分类
        inventory_status = {
            "in_stock": 0,
            "low_stock": 0,
            "out_of_stock": 0
        }
        
        for item in report_data_list:
            quantity = item.get("quantity", 0)
            if quantity == 0:
                inventory_status["out_of_stock"] += 1
            elif quantity <= 10:  # 假设小于等于10为低库存
                inventory_status["low_stock"] += 1
            else:
                inventory_status["in_stock"] += 1
        
        # 转换为列表格式
        asin_summary = [
            {
                "asin": asin,
                "total_quantity": stats["quantity"],
                "fulfillment_type": stats["fulfillment_type"],
                "condition": stats["condition"],
                "last_updated": stats["last_updated"]
            }
            for asin, stats in asin_stats.items()
        ]
        
        # 按库存量排序
        asin_summary.sort(key=lambda x: x["total_quantity"], reverse=True)
        
        # 构建返回数据
        parsed_data = {
            "report_type": "GET_VENDOR_REAL_TIME_INVENTORY_REPORT",
            "date_range": {
                "start": report_spec.get("dataStartTime"),
                "end": report_spec.get("dataEndTime")
            },
            "marketplace_ids": report_spec.get("marketplaceIds", []),
            "summary": {
                "total_records": total_records,
                "total_quantity": total_quantity,
                "unique_asins": total_asins,
                "avg_quantity_per_asin": total_quantity / total_asins if total_asins > 0 else 0,
                "inventory_status": inventory_status
            },
            "top_inventory": asin_summary[:10],  # 前10个库存最多的ASIN
            "all_data": report_data_list[:100]  # 限制返回前100条详细数据
        }
        
        return {
            "success": True,
            "data": parsed_data
        }
        
    except Exception as e:
        logger.error(f"解析供应商实时库存报告失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }
