#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Promotion Performance Report Tool

独立的促销表现报告工具，从拉取报告到解析数据的完整流程。
"""

from __future__ import annotations

import json
import logging
from datetime import datetime, timedelta, timezone
import sys
from pathlib import Path
from typing import Annotated, Any, Dict, List, Optional, Union

from langchain_core.tools import tool
from src.tools.decorators import log_io

# 导入common模块
from src.tools.amazon.sp_api.common.reports import ReportsClient, ReportRequest, iso_utc
from src.tools.amazon.sp_api.common.sp_config import AmazonSPConfig

logger = logging.getLogger(__name__)

# 市场映射表：前端marketplace -> Amazon marketplace_ids
MARKETPLACE_MAPPING = {
    # 北美
    "US": "ATVPDKIKX0DER",   # 美国
    "CA": "A2EUQ1WTGCTBG2",  # 加拿大
    "MX": "A1AM78C64UM0Y8",  # 墨西哥
    "BR": "A2Q3Y263D00KWC",  # 巴西

    # 欧洲
    "UK": "A1F83G8C2ARO7P",  # 英国
    "DE": "A1PA6795UKMFR9",  # 德国
    "FR": "A13V1IB3VIYZZH",  # 法国
    "IT": "APJ6JRA9NG5V4",   # 意大利
    "ES": "A1RKKUPIHCS9HS",  # 西班牙
    "NL": "A1805IZSGTT6HS",  # 荷兰
    "SE": "A2NODRKZP88ZB9",  # 瑞典
    "PL": "A1C3SOZRARQ6R3",  # 波兰
    "TR": "A33AVAJ2PDY3EV",  # 土耳其
    "EG": "ARBP9OOSHTCHU",   # 埃及

    # 亚太
    "JP": "A1VC38T7YXB528",  # 日本
    "AU": "A39IBJ37TRP1C6",  # 澳大利亚
    "SG": "A19VAU5U5O7RUS",  # 新加坡
    "IN": "A21TJRUUN4KGV",   # 印度

    # 中东
    "AE": "A2VIGQ35RCS4UG",  # 阿联酋
    "SA": "A17E79C6D8DWNP",  # 沙特阿拉伯
}


@tool(description="获取亚马逊促销表现报告数据。支持指定日期范围，自动处理报告创建、状态检查和数据解析。返回结构化的促销数据，包含促销统计、产品表现、ROAS分析等关键指标。")
@log_io
def amazon_promotion_performance_report(
    marketplace: Annotated[str, "前端传递的市场代码，如US、UK、DE等"] = "US",
    auto_fallback: Annotated[bool, "是否自动回退，默认True"] = True,
    max_fallback_days: Annotated[int, "最大回退天数，默认4"] = 4,
) -> str:
    """
    获取亚马逊促销表现报告数据
    
    Args:
        marketplace: 前端传递的市场代码，如US、UK、DE等
        start_date: 开始日期，格式：YYYY-MM-DD
        end_date: 结束日期，格式：YYYY-MM-DD
        date: 单日日期，格式：YYYY-MM-DD
        wait_timeout: 等待超时时间（秒）
        output_dir: 输出目录
        auto_fallback: 是否自动回退
        max_fallback_days: 最大回退天数
        
    Returns:
        JSON格式的促销报告数据
    """
    try:
        # 初始化配置
        config = AmazonSPConfig()
        reports_client = ReportsClient(config)
        
        # 固定获取最近30天的数据（忽略外部传参）
        end_date_dt = datetime.now(timezone.utc) - timedelta(days=1)  # 昨天作为结束日期
        start_date_dt = end_date_dt - timedelta(days=29)  # 30天前作为开始日期
        start_date = start_date_dt.strftime("%Y-%m-%d")
        end_date = end_date_dt.strftime("%Y-%m-%d")
        
        # 处理市场ID
        marketplace_ids = [MARKETPLACE_MAPPING.get(marketplace.upper(), "ATVPDKIKX0DER")]
        
        # 创建报告
        logger.info(f"创建促销表现报告，日期范围: {start_date} - {end_date}")
        
        # 促销报告需要特殊的日期范围（促销开始日期）
        start_datetime = datetime.strptime(f"{start_date}T00:00:00Z", "%Y-%m-%dT%H:%M:%SZ")
        end_datetime = datetime.strptime(f"{end_date}T23:59:59Z", "%Y-%m-%dT%H:%M:%SZ")
        
        # 促销开始日期范围（通常比报告日期早30-60天）
        promotion_start_from = (start_datetime - timedelta(days=60)).strftime("%Y-%m-%dT%H:%M:%SZ")
        promotion_start_to = end_datetime.strftime("%Y-%m-%dT%H:%M:%SZ")
        
        report_request = ReportRequest(
            report_type="GET_PROMOTION_PERFORMANCE_REPORT",
            # 数据统计区间与活动筛选区间保持一致
            data_start_time=promotion_start_from,
            data_end_time=promotion_start_to,
            marketplace_ids=marketplace_ids,
            report_options={
                "reportPeriod": "DAY",
                "promotionStartDateFrom": promotion_start_from,
                "promotionStartDateTo": promotion_start_to
            }
        )
        
        # 创建报告
        try:
            report_id = reports_client.create(report_request)
            logger.info(f"报告创建成功，报告ID: {report_id}")
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"报告创建失败: {str(e)}",
                "details": {
                    "start_date": start_date,
                    "end_date": end_date,
                    "report_type": "GET_PROMOTION_PERFORMANCE_REPORT"
                }
            }, ensure_ascii=False)
        
        # 等待报告完成
        logger.info(f"等待报告完成，报告ID: {report_id}")
        try:
            document_id = reports_client.wait_done(report_id, timeout_seconds=300)
            logger.info(f"报告处理完成，文档ID: {document_id}")
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"报告处理超时或失败: {str(e)}",
                "details": {
                    "report_id": report_id,
                    "timeout": 300
                }
            }, ensure_ascii=False)
        
        # 下载报告
        logger.info(f"下载报告文档: {document_id}")
        try:
            download_url = reports_client.get_download_url(document_id)
            logger.info(f"获取下载URL成功: {download_url}")
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"获取下载URL失败: {str(e)}",
                "details": {
                    "document_id": document_id
                }
            }, ensure_ascii=False)
        
        # 下载文件
        try:
            output_path = Path("reports") / f"get_promotion_performance_report_report.json"
            reports_client.download_to_file(download_url, output_path)
            logger.info(f"报告下载完成: {output_path}")
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"报告下载失败: {str(e)}",
                "details": {
                    "document_id": document_id,
                    "download_url": download_url
                }
            }, ensure_ascii=False)
        
        # 解析报告数据
        logger.info(f"解析报告数据: {str(output_path)}")
        parsed_data = parse_promotion_performance_report(str(output_path))
        
        if not parsed_data["success"]:
            return json.dumps({
                "success": False,
                "error": f"报告解析失败: {parsed_data['error']}",
                "details": {
                    "file_path": str(output_path)
                }
            }, ensure_ascii=False)
        
        # 返回成功结果
        result = {
            "success": True,
            "data_source": "AMAZON_PROMOTION_PERFORMANCE_REPORT",
            "operation_id": report_id,
            "operation_time": datetime.now().isoformat(),
            "report_type": "GET_PROMOTION_PERFORMANCE_REPORT",
            "report_id": report_id,
            "document_id": document_id,
            "date_range": {
                "start": start_date,
                "end": end_date
            },
            "marketplace_ids": marketplace_ids,
            "file_path": str(output_path),
            "file_size": output_path.stat().st_size if output_path.exists() else 0,
            "data": parsed_data["data"],
            "execution_time": datetime.now().isoformat()
        }
        
        # 存储数据到数据库
        try:
            stored_count = store_promotion_data_to_database(
                parsed_data["data"]["full_data"], 
                "TEST_COMPANY_001",  # 硬编码company_id
                "TEST_STORE_001",    # 硬编码store_id
                marketplace
            )
            result["database_stored"] = stored_count
            logger.info(f"成功存储 {stored_count} 条促销记录到数据库")
        except Exception as e:
            logger.error(f"数据库存储失败: {e}")
            result["database_stored"] = 0
        
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"促销表现报告工具执行失败: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "details": {
                "start_date": start_date,
                "end_date": end_date,
                "marketplace": marketplace
            }
        }, ensure_ascii=False)


def parse_promotion_performance_report(file_path: str) -> Dict[str, Any]:
    """
    解析促销表现报告数据
    
    Args:
        file_path: 报告文件路径
        
    Returns:
        解析后的数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        # 提取报告规格和数据
        report_spec = report_data.get("reportSpecification", {})
        promotions = report_data.get("promotions", [])
        
        if not promotions:
            return {
                "success": False,
                "error": "报告数据为空"
            }
        
        # 统计所有促销
        total_promotions = len(promotions)
        total_units_sold = 0
        total_revenue = 0
        total_amount_spent = 0
        total_glance_views = 0
        
        # 完整数据存储（基于promotionId维度）
        full_promotion_data = []
        
        for promotion in promotions:
            total_units_sold += promotion.get("unitsSold", 0)
            total_revenue += promotion.get("revenue", 0)
            total_amount_spent += promotion.get("amountSpent", 0)
            total_glance_views += promotion.get("glanceViews", 0)
            
            # 基于promotionId维度创建记录
            for product in promotion.get("includedProducts", []):
                asin = product.get("asin", "")
                if asin:
                    full_record = {
                        # 报告基本信息
                        "report_type": "GET_PROMOTION_PERFORMANCE_REPORT",
                        "report_date_from": report_spec.get("reportOptions", {}).get("promotionStartDateFrom"),
                        "report_date_to": report_spec.get("reportOptions", {}).get("promotionStartDateTo"),
                        "marketplace_ids": report_spec.get("marketplaceIds", []),
                        
                        # 促销信息（公共数据，复制到每个产品记录）
                        "promotion_id": promotion.get("promotionId"),
                        "promotion_name": promotion.get("promotionName"),
                        "vendor_code": promotion.get("vendorCode"),
                        "units_sold": promotion.get("unitsSold"),
                        "amount_spent": promotion.get("amountSpent"),
                        "amount_spent_currency_code": promotion.get("amountSpentCurrencyCode"),
                        "revenue": promotion.get("revenue"),
                        "revenue_currency_code": promotion.get("revenueCurrencyCode"),
                        "type": promotion.get("type"),
                        "status": promotion.get("status"),
                        "marketplace_id": promotion.get("marketplaceId"),
                        "funding_agreement_id": promotion.get("fundingAgreementId"),
                        "glance_views": promotion.get("glanceViews"),
                        "start_date_time": promotion.get("startDateTime"),
                        "end_date_time": promotion.get("endDateTime"),
                        "created_date_time": promotion.get("createdDateTime"),
                        "last_updated_date_time": promotion.get("lastUpdatedDateTime"),
                        
                        # 产品信息（基于promotionId维度）
                        "asin": asin,
                        "product_name": product.get("productName"),
                        "product_glance_views": product.get("productGlanceViews"),
                        "product_units_sold": product.get("productUnitsSold"),
                        "product_amount_spent": product.get("productAmountSpent"),
                        "product_amount_spent_currency_code": product.get("productAmountSpentCurrencyCode"),
                        "product_revenue": product.get("productRevenue"),
                        "product_revenue_currency_code": product.get("productRevenueCurrencyCode"),
                        
                        # 解析时间戳
                        "parsed_at": datetime.now().isoformat()
                    }
                    full_promotion_data.append(full_record)
        
        # 构建返回数据
        parsed_data = {
            "report_type": "GET_PROMOTION_PERFORMANCE_REPORT",
            "date_range": {
                "start": report_spec.get("reportOptions", {}).get("promotionStartDateFrom"),
                "end": report_spec.get("reportOptions", {}).get("promotionStartDateTo")
            },
            "marketplace_ids": report_spec.get("marketplaceIds", []),
            "summary": {
                "total_promotions": total_promotions,
                "total_units_sold": total_units_sold,
                "total_revenue": total_revenue,
                "total_amount_spent": total_amount_spent,
                "total_glance_views": total_glance_views,
                "overall_roas": (total_revenue / total_amount_spent) if total_amount_spent > 0 else 0,
                "conversion_rate": (total_units_sold / total_glance_views * 100) if total_glance_views > 0 else 0
            },
            "full_data": full_promotion_data,  # 返回所有记录
            "total_records": len(full_promotion_data),
            "total_available_records": len(full_promotion_data)  # 总可用记录数
        }
        
        return {
            "success": True,
            "data": parsed_data
        }
        
    except Exception as e:
        logger.error(f"解析促销表现报告失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def test_mock_database_insert():
    """测试mock数据插入"""
    import json
    from datetime import datetime
    
    print('🧪 开始测试Mock数据插入...')
    print('=' * 50)
    
    # 创建mock数据
    mock_data = [
        {
            "promotion_id": "TEST_PROMOTION_001",
            "promotion_name": "测试促销活动1",
            "promotion_type": "PRICE_DISCOUNT",
            "funding_agreement_id": "TEST_AGREEMENT_001",
            "asin": "B09Q39ZY44",
            "start_date_time": "2025-08-01",
            "end_date_time": "2025-08-31",
            "status": "ACTIVE",
            "units_sold": 150,
            "amount_spent_currency_code": "USD",
            "glance_view": 500
        },
        {
            "promotion_id": "TEST_PROMOTION_002",
            "promotion_name": "测试促销活动2",
            "promotion_type": "PRICE_DISCOUNT",
            "funding_agreement_id": "TEST_AGREEMENT_002",
            "asin": "B08XYZ1234",
            "start_date_time": "2025-08-01",
            "end_date_time": "2025-08-31",
            "status": "ACTIVE",
            "units_sold": 200,
            "amount_spent_currency_code": "USD",
            "glance_view": 800
        }
    ]
    
    try:
        # 调用存储函数（需要先实现）
        print('💾 插入Mock数据...')
        print('⚠️  注意：promotion_table数据库存储功能尚未实现')
        print('📊 Mock数据预览:')
        for i, item in enumerate(mock_data, 1):
            print(f'  {i}. {item["promotion_name"]} - ASIN: {item["asin"]} - 销量: {item["units_sold"]}')
        
        print()
        print('✅ Mock数据测试完成！')
        print(f'📊 准备插入 {len(mock_data)} 条记录')
        print(f'🏢 企业ID: TEST_COMPANY_001')
        print(f'🏪 店铺ID: TEST_STORE_001')
        print(f'🌍 市场: US')
        print()
        print('🔍 你可以在数据库中查看:')
        print('   Schema: amazon_data')
        print('   Table: promotion_table (需要先创建)')
        
        return True
        
    except Exception as e:
        print(f'❌ Mock数据插入失败: {e}')
        return False


def test_real_api_call():
    """测试真实API调用"""
    try:
        print('🔍 开始真实API调用测试...')
        print('📋 测试参数:')
        print('  - 市场: US')
        print('  - 日期: 使用固定最近30天')
        print()
        
        # 直接调用工具函数
        result = amazon_promotion_performance_report.invoke({
            "marketplace": "US"
        })
        data = json.loads(result)
        
        if data['success']:
            print()
            print('🎉 成功获取促销表现报告！')
            print('-' * 40)
            
            # 基本信息
            print(f'📄 报告ID: {data.get("report_id", "N/A")}')
            print(f'📄 文档ID: {data.get("document_id", "N/A")}')
            print(f'📁 文件路径: {data.get("file_path", "N/A")}')
            print(f'💾 文件大小: {data.get("file_size", 0):,} bytes')
            
            print()
            print('✅ 报告获取完成！数据已保存到本地文件。')
            return 0
            
        else:
            print()
            print('❌ 获取促销报告失败')
            print('-' * 40)
            print(f'错误信息: {data["error"]}')
            
            print()
            print('💡 可能的原因:')
            print('  - 请求的日期范围内没有促销数据')
            print('  - Amazon API权限不足')
            print('  - 账户配置问题')
            print('  - 促销活动已结束')
            
            print()
            print('🔧 建议:')
            print('  - 检查日期范围是否正确')
            print('  - 确认Amazon SP API配置')
            print('  - 联系Amazon支持确认账户状态')
            
            return 1
            
    except KeyboardInterrupt:
        print()
        print('⚠️  操作被用户中断')
        return 1
    except Exception as e:
        print()
        print(f'❌ 程序执行出错: {e}')
        return 1


def store_promotion_data_to_database(promotion_data: List[Dict], company_id: str, store_id: str, marketplace: str) -> int:
    """
    将促销数据存储到数据库
    
    Args:
        promotion_data: 促销数据列表
        company_id: 公司ID
        store_id: 店铺ID
        marketplace: 市场代码
        
    Returns:
        存储的记录数
    """
    if not promotion_data:
        return 0
    
    import os
    import psycopg
    from datetime import datetime, timezone
    
    postgres_uri = os.getenv("POSTGRES_URI")
    if not postgres_uri:
        raise ValueError("POSTGRES_URI 环境变量未设置")
    
    print(f"📊 开始处理 {len(promotion_data)} 条促销数据...")
    
    # 存储到数据库
    try:
        with psycopg.connect(postgres_uri) as conn:
            with conn.cursor() as cur:
                stored_count = 0
                batch_size = 50  # 每批插入50条记录
                batch_data = []
                
                # 去重处理，只保留唯一的主键记录
                unique_items = {}
                duplicate_keys = []
                
                print(f"🔍 开始分析数据去重...")
                print(f"📊 原始数据条数: {len(promotion_data)}")
                
                for i, item in enumerate(promotion_data):
                    asin = item.get("asin", "")
                    promotion_id = item.get("promotion_id", "")
                    funding_agreement_id = item.get("funding_agreement_id", "")
                    
                    # 只检查asin是否为空，允许空的promotion_id
                    if not asin:
                        continue
                    
                    # 如果promotion_id为空，使用BEST_DEAL作为promotion_id（BEST_DEAL活动没有promotionId）
                    if not promotion_id:
                        promotion_id = "BEST_DEAL"
                        item["promotion_id"] = "BEST_DEAL"
                    
                    # 构建主键
                    key = f"{company_id}-{store_id}-{promotion_id}-{funding_agreement_id}-{asin}"
                    
                    if key in unique_items:
                        duplicate_keys.append({
                            "key": key,
                            "existing": unique_items[key],
                            "new": item,
                            "index": i
                        })
                    else:
                        unique_items[key] = item
                
                print(f"📊 去重后记录数: {len(unique_items)} (原始记录数: {len(promotion_data)})")
                
                if duplicate_keys:
                    print(f"🔍 重复记录详情:")
                    for i, dup in enumerate(duplicate_keys[:5]):  # 只显示前5条重复记录
                        print(f"  重复{i+1}: {dup['key']}")
                        print(f"    现有: promotion_id={dup['existing'].get('promotion_id')}, asin={dup['existing'].get('asin')}")
                        print(f"    新记录: promotion_id={dup['new'].get('promotion_id')}, asin={dup['new'].get('asin')}")
                    if len(duplicate_keys) > 5:
                        print(f"    ... 还有 {len(duplicate_keys) - 5} 条重复记录")
                
                for item in unique_items.values():
                    
                    # 处理时间字段
                    start_date_time = None
                    end_date_time = None
                    
                    if item.get("start_date_time"):
                        try:
                            start_date_time = datetime.fromisoformat(item["start_date_time"].replace('Z', '+00:00'))
                        except:
                            pass
                            
                    if item.get("end_date_time"):
                        try:
                            end_date_time = datetime.fromisoformat(item["end_date_time"].replace('Z', '+00:00'))
                        except:
                            pass
                    
                    # 构建数据
                    promotion_id = item.get("promotion_id", "")
                    promotion_name = item.get("promotion_name", "")
                    asin = item.get("asin", "")
                    
                    # 重新构建主键（确保每个记录都有正确的主键）
                    company_id_store_id_promotion_id_funding_agreement_id_asin = f"{company_id}-{store_id}-{item.get('promotion_id', '')}-{item.get('funding_agreement_id', '')}-{asin}"
                    
                    values = [
                        company_id_store_id_promotion_id_funding_agreement_id_asin,
                        company_id,
                        store_id,
                        marketplace,
                        item.get("funding_agreement_id", ""),
                        promotion_id,
                        promotion_name,
                        item.get("type", ""),
                        asin,
                        start_date_time,
                        end_date_time,
                        item.get("status", ""),
                        item.get("units_sold", 0),
                        item.get("amount_spent_currency_code", ""),
                        item.get("glance_views", 0),  # 修复：使用glance_views而不是glance_view
                        datetime.now(timezone.utc)
                    ]
                    
                    batch_data.append(values)
                    
                    # 批量插入
                    if len(batch_data) >= batch_size:
                        cur.executemany("""
                            INSERT INTO amazon_data.promotion_table (
                                company_id_store_id_promotion_id_funding_agreement_id_asin, company_id, store_id, marketplace,
                                funding_agreement_id, promotion_id, promotion_name, promotion_type, asin,
                                start_date_time, end_date_time, status, units_sold, amount_spent_currency_code, glance_view,
                                data_load_time
                            ) VALUES (
                                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                            )
                            ON CONFLICT (company_id_store_id_promotion_id_funding_agreement_id_asin) DO UPDATE SET
                                promotion_name = EXCLUDED.promotion_name,
                                start_date_time = EXCLUDED.start_date_time,
                                end_date_time = EXCLUDED.end_date_time,
                                status = EXCLUDED.status,
                                units_sold = EXCLUDED.units_sold,
                                amount_spent_currency_code = EXCLUDED.amount_spent_currency_code,
                                glance_view = EXCLUDED.glance_view,
                                data_load_time = NOW()
                        """, batch_data)
                        
                        stored_count += len(batch_data)
                        batch_data = []
                        print(f"📊 已存储 {stored_count} 条记录...")
                
                # 插入剩余的数据
                if batch_data:
                    cur.executemany("""
                        INSERT INTO amazon_data.promotion_table (
                            company_id_store_id_promotion_id_funding_agreement_id_asin, company_id, store_id, marketplace,
                            funding_agreement_id, promotion_id, promotion_name, promotion_type, asin,
                            start_date_time, end_date_time, status, units_sold, amount_spent_currency_code, glance_view,
                            data_load_time
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                        )
                        ON CONFLICT (company_id_store_id_promotion_id_funding_agreement_id_asin) DO UPDATE SET
                            promotion_name = EXCLUDED.promotion_name,
                            start_date_time = EXCLUDED.start_date_time,
                            end_date_time = EXCLUDED.end_date_time,
                            status = EXCLUDED.status,
                            units_sold = EXCLUDED.units_sold,
                            amount_spent_currency_code = EXCLUDED.amount_spent_currency_code,
                            glance_view = EXCLUDED.glance_view,
                            data_load_time = NOW()
                    """, batch_data)
                    
                    stored_count += len(batch_data)
                
                conn.commit()
                print(f"✅ 成功存储 {stored_count} 条促销记录到数据库")
                return stored_count
                
    except Exception as e:
        print(f"❌ 数据库存储失败: {e}")
        raise e


def main():
    """主函数，用于直接运行测试"""
    import sys
    import os
    
    # 添加项目根目录到路径
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
    
    print('🚀 Amazon Promotion Performance Report 测试工具')
    print('=' * 60)
    print('选择测试模式:')
    print('1. Mock数据插入测试')
    print('2. 真实API调用测试')
    print()
    
    choice = input('请输入选择 (1 或 2): ').strip()
    
    if choice == '1':
        return test_mock_database_insert()
    elif choice == '2':
        return test_real_api_call()
    else:
        print('❌ 无效选择')
        return False


if __name__ == "__main__":
    sys.exit(main())