#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Listing Search Items Tool

独立的亚马逊listing搜索工具，根据Vendor Code列表批量搜索listing信息并存储到数据库。
"""

import json
import logging
import os
import sys
import psycopg
import time
import re
from typing import Any, Dict, Optional, List
from datetime import datetime, timezone

from langchain.tools import tool
from typing_extensions import Annotated

from src.tools.decorators import log_io
from src.tools.amazon.sp_api.common.sp_config import AmazonSPConfig
from sp_api.api import ListingsItems

logger = logging.getLogger(__name__)

# 市场映射表：前端marketplace -> Amazon marketplace_ids
MARKETPLACE_MAPPING = {
    # 北美
    "US": "ATVPDKIKX0DER",   # 美国
    "CA": "A2EUQ1WTGCTBG2",  # 加拿大
    "MX": "A1AM78C64UM0Y8",  # 墨西哥
    "BR": "A2Q3Y263D00KWC",  # 巴西

    # 欧洲
    "UK": "A1F83G8C2ARO7P",  # 英国
    "DE": "A1PA6795UKMFR9",  # 德国
    "FR": "A13V1IB3VIYZZH",  # 法国
    "IT": "APJ6JRA9NG5V4",   # 意大利
    "ES": "A1RKKUPIHCS9HS",  # 西班牙
    "NL": "A1805IZSGTT6HS",  # 荷兰
    "SE": "A2NODRKZP88ZB9",  # 瑞典
    "PL": "A1C3SOZRARQ6R3",  # 波兰
    "TR": "A33AVAJ2PDY3EV",  # 土耳其
    "EG": "ARBP9OOSHTCHU",   # 埃及

    # 亚太
    "JP": "A1VC38T7YXB528",  # 日本
    "AU": "A39IBJ37TRP1C6",  # 澳大利亚
    "SG": "A19VAU5U5O7RUS",  # 新加坡
    "IN": "A21TJRUUN4KGV",   # 印度

    # 中东
    "AE": "A2VIGQ35RCS4UG",  # 阿联酋
    "SA": "A17E79C6D8DWNP",  # 沙特阿拉伯
}


@tool(description="根据Vendor Code列表批量搜索亚马逊listing信息并存储到数据库。支持分页获取所有数据，返回所有卖家的listing列表，包括：产品标题、品牌、描述、五点描述、搜索关键词、list_price售价、cost_price成本价、履约可用性、采购信息、产品标识符（UPC/EAN/ISBN/制造商编号）、listing问题、报价信息等卖家私有信息。")
@log_io
def amazon_listing_search_items(
    vendor_codes: Annotated[Any, "Vendor Code支持列表或中/英文逗号分隔字符串，如：['WGEWH','WGEWI'] 或 'WGEWH,WGEWI'"],
    company_id: Annotated[str, "前端传递的公司ID"],
    store_id: Annotated[str, "前端传递的店铺ID"],
    vc_mode: Annotated[str, "前端传递的VC模式"],
    marketplace: Annotated[str, "前端传递的市场代码，如US、UK、DE等"] = "US",

) -> str:
    """
    搜索Amazon listing items，支持按Vendor Code搜索，自动分页处理，并将结果存储到数据库
    
    Args:
        vc_mode: 前端传递的VC模式
        marketplace: 前端传递的市场代码，如US、UK、DE等
        
    Returns:
        JSON字符串，包含搜索结果和统计信息
    """
    try:
        # 参数验证
        if not vendor_codes:
            return json.dumps({
                "success": False,
                "error": "缺少必要参数: vendor_codes",
                "error_type": "MISSING_VENDOR_CODES"
            }, ensure_ascii=False, indent=2)
        
        if not company_id:
            return json.dumps({
                "success": False,
                "error": "缺少必要参数: company_id",
                "error_type": "MISSING_COMPANY_ID"
            }, ensure_ascii=False, indent=2)
        
        if not store_id:
            return json.dumps({
                "success": False,
                "error": "缺少必要参数: store_id", 
                "error_type": "MISSING_STORE_ID"
            }, ensure_ascii=False, indent=2)
        
        if not vc_mode:
            return json.dumps({
                "success": False,
                "error": "缺少必要参数: vc_mode",
                "error_type": "MISSING_VC_MODE"
            }, ensure_ascii=False, indent=2)
        
        if not marketplace:
            return json.dumps({
                "success": False,
                "error": "缺少必要参数: marketplace",
                "error_type": "MISSING_MARKETPLACE"
            }, ensure_ascii=False, indent=2)
        
        # 解析Vendor Code列表（仅支持：列表 或 中/英文逗号分隔字符串）
        vendor_code_list: List[str] = []
        if isinstance(vendor_codes, list):
            vendor_code_list = [str(code).strip() for code in vendor_codes if str(code).strip()]
        elif isinstance(vendor_codes, str):
            raw_vendor_codes = re.split(r"[,\uFF0C]+", vendor_codes)
            vendor_code_list = [code.strip() for code in raw_vendor_codes if code.strip()]
        else:
            return json.dumps({
                "success": False,
                "error": "vendor_codes 类型不支持，应为列表或字符串",
                "error_type": "INVALID_VENDOR_CODES_TYPE"
            }, ensure_ascii=False, indent=2)

        # 去重，保持顺序
        seen_codes = set()
        unique_vendor_codes: List[str] = []
        for code in vendor_code_list:
            if code not in seen_codes:
                seen_codes.add(code)
                unique_vendor_codes.append(code)
        vendor_code_list = unique_vendor_codes
        if not vendor_code_list:
            return json.dumps({
                "success": False,
                "error": "Vendor Code列表为空",
                "error_type": "EMPTY_VENDOR_CODES"
            }, ensure_ascii=False, indent=2)
        
        # 映射 marketplace 到 marketplace_ids
        if marketplace not in MARKETPLACE_MAPPING:
            return json.dumps({
                "success": False,
                "error": f"不支持的市场: {marketplace}",
                "error_type": "UNSUPPORTED_MARKETPLACE",
                "supported_marketplaces": list(MARKETPLACE_MAPPING.keys())
            }, ensure_ascii=False, indent=2)
        
        marketplace_ids = [MARKETPLACE_MAPPING[marketplace]]
        
        # 初始化配置
        config = AmazonSPConfig()
        config.validate()
        credentials = config.build_credentials()
        
        # 创建Listings API客户端
        listings_client = ListingsItems(credentials=credentials)
        
        # 处理包含的数据字段
        included_data_list = ["summaries", "attributes", "issues", "offers", "fulfillmentAvailability", "procurement"]
        
        logger.info(f"开始批量分页搜索listing信息，Vendor Codes: {vendor_code_list}, 市场: {marketplace_ids}")
        logger.info(f"分页参数: page_size=20, max_pages=100, delay=1s")
        
        # 批量处理所有Vendor Code
        all_processed_items = []
        all_sellers_results = {}
        total_items_found = 0
        total_database_stored = 0
        
        print(f"🚀 开始批量处理 {len(vendor_code_list)} 个Vendor Code...")
        
        for i, vendor_code in enumerate(vendor_code_list, 1):
            print(f"📋 [{i}/{len(vendor_code_list)}] 开始处理Vendor Code: {vendor_code}")
            logger.info(f"开始处理Vendor Code: {vendor_code}")
            
            # 分页搜索当前卖家的所有数据
            seller_processed_items = []
            page_count = 0
            page_token = None
            seller_items_found = 0
            seller_items_stored = 0
            
            try:
                print(f"  🔄 Vendor {vendor_code} - 开始分页搜索...")
                while page_count < 10: # 把max_pages的使用写死为默认值
                    page_count += 1
                    print(f"  📄 [{page_count}/{10}] Vendor {vendor_code} - 正在处理第 {page_count} 页...")
                    logger.info(f"Vendor {vendor_code} - 正在处理第 {page_count} 页...")
                    
                    # 构建搜索参数
                    search_params = {
                        "sellerId": vendor_code,
                        "marketplaceIds": marketplace_ids,
                        "pageSize": 100, # 把max_pages的使用写死为默认值
                        "includedData": included_data_list,
                        "sortBy": "createdDate", # 把sort_by的使用写死为默认值
                        "sortOrder": "DESCENDING", # 把sort_order的使用写死为默认值
                    }
                    
                    if page_token:
                        search_params["pageToken"] = page_token
                    
                    # 调用API搜索listing信息
                    response = listings_client.search_listings_items(**search_params)
                    
                    if not response.payload:
                        logger.warning(f"Vendor {vendor_code} - 第 {page_count} 页没有数据")
                        break
                    
                    # 解析返回的数据
                    search_data = response.payload
                    items = search_data.get("items", [])
                    
                    print(f"    ✅ 第 {page_count} 页: 找到 {len(items)} 个商品")
                    logger.info(f"Vendor {vendor_code} - 第 {page_count} 页: 找到 {len(items)} 个商品")
                    seller_items_found += len(items)
                    
                    # 处理当前页的商品 - 边解析边插入
                    page_processed_items = []
                    for item in items:
                        attributes = item.get("attributes", {})
                        summaries = item.get("summaries", [])
                        issues = item.get("issues", [])
                        offers = item.get("offers", [])
                        
                        # 辅助函数：从复杂结构中提取值
                        def extract_single_value(field_data, default=None):
                            """从Amazon API的复杂字段结构中提取单个值（用于数值字段）"""
                            if not field_data or not isinstance(field_data, list):
                                return default
                            
                            # 如果是数组，取第一个元素的值
                            first_item = field_data[0]
                            if isinstance(first_item, dict):
                                return first_item.get("value", default)
                            return first_item if first_item is not None else default
                        
                        def extract_values(field_data, default=None):
                            """从Amazon API的复杂字段结构中提取所有值（用于数组字段）"""
                            if not field_data or not isinstance(field_data, list):
                                return default or []
                            
                            values = []
                            for item in field_data:
                                if isinstance(item, dict):
                                    value = item.get("value")
                                    if value is not None:
                                        values.append(value)
                                elif item is not None:
                                    values.append(item)
                            
                            return values if values else (default or [])
                        
                        def extract_single_object(field_data, default=None):
                            """从Amazon API的复杂字段结构中提取单个对象（用于JSONB对象字段）"""
                            if not field_data or not isinstance(field_data, list):
                                return default or {}
                            
                            # 取第一个元素作为对象
                            first_item = field_data[0]
                            if isinstance(first_item, dict):
                                return first_item
                            return default or {}
                        
                        # 保存原始API响应数据
                        original_api_item = item.copy()
                        
                        # 严格按照字段表格构建数据
                        processed_item = {
                            # 基础字段 - ASIN从summaries中提取
                            "asin": summaries[0].get("asin", "") if summaries else item.get("asin", ""),
                            "sku": item.get("sku", ""),
                            "vendor_code": vendor_code,  # 添加Vendor Code信息
                            
                            # 从external_product_id提取的字段（保持完整数组结构）
                            "external_product_id": attributes.get("external_product_id", []),
                            
                            # 从summaries提取的字段
                            "status": summaries[0].get("status", [])[0] if summaries and summaries[0].get("status") else None,
                            "created_date": summaries[0].get("createdDate", "") if summaries else "",
                            "last_updated_date": summaries[0].get("lastUpdatedDate", "") if summaries else "",
                            "main_image": summaries[0].get("mainImage", {}) if summaries else {},
                            
                            # 从attributes提取的字段
                            "item_name": extract_single_value(attributes.get("item_name", [])),
                            "brand": extract_single_value(attributes.get("brand", [])),
                            "description": extract_single_value(attributes.get("description", [])),
                            "bullet_point": extract_values(attributes.get("bullet_point", [])),
                            "generic_keyword": extract_values(attributes.get("generic_keyword", [])),
                            "list_price": extract_single_object(attributes.get("list_price", [])),
                            "cost_price": extract_single_object(attributes.get("cost_price", [])),
                            "fulfillment_availability": extract_single_object(attributes.get("fulfillment_availability", [])),
                            "procurement": extract_single_object(attributes.get("procurement", [])),
                            
                            # 新增字段提取 - 只提取数据库schema中已定义的字段
                            "product_category_id": extract_single_value(attributes.get("product_category", [])),
                            "product_subcategory_id": extract_single_value(attributes.get("product_subcategory", [])),
                            
                            # 尺寸和重量信息 - 从API提取
                            "item_weight": extract_single_object(attributes.get("item_weight", [])),
                            "item_depth_width_height": extract_single_object(attributes.get("item_depth_width_height", [])),
                            "item_package_weight": extract_single_object(attributes.get("item_package_weight", [])),
                            "item_package_dimensions": extract_single_object(attributes.get("item_package_dimensions", [])),
                            
                            # 产品样式信息 - 从API提取
                            "style": extract_single_object(attributes.get("style", [])),
                            "color": extract_single_object(attributes.get("color", [])),
                            "size": extract_single_object(attributes.get("size", [])),
                            "included_components": extract_values(attributes.get("included_components", [])),
                            
                            # 从issues提取的字段
                            "issues": [
                                {
                                    "code": issue.get("code", ""),
                                    "message": issue.get("message", ""),
                                    "severity": issue.get("severity", ""),
                                    "attribute_name": issue.get("attributeName", ""),
                                }
                                for issue in issues
                            ],
                            
                            # 从offers提取的字段
                            "offers": [
                                {
                                    "marketplace_id": offer.get("marketplaceId", ""),
                                    "price": offer.get("price", {}),
                                    "fulfillment_availability": offer.get("fulfillmentAvailability", {}),
                                }
                                for offer in offers
                            ],
                            
                            # 解析时间戳
                            "parsed_at": datetime.now().isoformat(),
                            
                            # 保存原始API响应
                            "original_api_response": original_api_item
                        }
                        
                        page_processed_items.append(processed_item)
                        seller_processed_items.append(processed_item)
                    
                    # 每页立即写入数据库，降低内存与失败风险
                    if page_processed_items:
                        try:
                            page_stored = store_listing_details_to_database(
                                page_processed_items,
                                company_id,
                                store_id,
                                vc_mode,
                                marketplace,
                                [vendor_code]
                            )
                            seller_items_stored += page_stored
                            total_database_stored += page_stored
                            print(f"    💾 第 {page_count} 页: 成功存储 {page_stored} 条记录")
                        except Exception as e:
                            logger.error(f"Vendor {vendor_code} - 第 {page_count} 页数据库存储失败: {e}")
                            print(f"    ❌ 第 {page_count} 页: 数据库存储失败: {e}")

                    # 获取下一页的token（兼容两种返回位置）
                    page_token = None
                    try:
                        # 一些 sp-api 客户端在 response.pagination 暴露 nextToken
                        if hasattr(response, 'pagination') and response.pagination:
                            page_token = response.pagination.get("nextToken")
                    except Exception:
                        pass
                    if not page_token:
                        # 退化到 payload.pagination
                        pagination = search_data.get("pagination", {})
                        page_token = pagination.get("nextToken")
                    
                    if not page_token:
                        logger.info(f"Vendor {vendor_code} - 没有更多页面")
                        break
                    
                    # 页面间延迟
                    time.sleep(1)  # 固定延迟1秒
                
                # 卖家级别不再一次性入库，已在分页中逐页入库
                
                # 记录当前卖家的结果
                all_sellers_results[vendor_code] = {
                    "items_found": seller_items_found,
                    "items_stored": seller_items_stored,
                    "pages_processed": page_count
                }
                
                total_items_found += seller_items_found
                
                print(f"  ✅ Vendor {vendor_code} - 处理完成: 找到 {seller_items_found} 个商品，存储 {seller_items_stored} 条记录")
                
            except Exception as e:
                logger.error(f"Vendor {vendor_code} - 处理失败: {e}")
                print(f"  ❌ Vendor {vendor_code} - 处理失败: {e}")
                all_sellers_results[vendor_code] = {
                    "error": str(e),
                    "items_found": 0,
                    "items_stored": 0,
                    "pages_processed": 0
                }
        
        # 构建最终结果
        structured_data = {
            "success": True,
            "data_source": "AMAZON_LISTING_SEARCH_ITEMS",
            "operation_id": f"listing_search_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "operation_time": datetime.now().isoformat(),
            "database_stored": total_database_stored,
            "vendor_codes": vendor_code_list,
            "company_id": company_id,
            "store_id": store_id,
            "vc_mode": vc_mode,
            "marketplace": marketplace,
            "marketplace_ids": marketplace_ids,
            "summary": {
                "total_vendors": len(vendor_code_list),
                "total_items_found": total_items_found,
                "total_items_stored": total_database_stored,
                "successful_vendors": len([r for r in all_sellers_results.values() if "error" not in r]),
                "failed_vendors": len([r for r in all_sellers_results.values() if "error" in r])
            },
            "vendor_results": all_sellers_results,
            "pagination_settings": {
                "page_size": 100, # 把max_pages的使用写死为默认值
                "max_pages": 10, # 把max_pages的使用写死为默认值
                "delay_seconds": 1, # 把delay_seconds的使用写死为默认值
                "sort_by": "createdDate", # 把sort_by的使用写死为默认值
                "sort_order": "DESCENDING" # 把sort_order的使用写死为默认值
            }
        }
        
        return json.dumps(structured_data, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"搜索listing信息失败: {e}")
        return json.dumps({
            "success": False,
            "error": f"搜索listing信息失败: {str(e)}",
            "details": {
                "vendor_codes": vendor_codes,
                "marketplace": marketplace
            }
        }, ensure_ascii=False)


def store_listing_details_to_database(items: list, company_id: str, store_id: str, vc_mode: str, marketplace: str, vendor_codes: list) -> int:
    """
    将listing详情数据存储到数据库
    
    Args:
        items: 处理后的商品列表
        company_id: 公司ID
        store_id: 店铺ID
        vc_mode: VC模式 DI/DO/DF
        marketplace: 市场代码
        vendor_codes: 供应商代码列表
        
    Returns:
        存储的记录数
    """
    if not items:
        return 0
    
    postgres_uri = os.getenv("POSTGRES_URI")
    if not postgres_uri:
        raise ValueError("POSTGRES_URI 环境变量未设置")
    
    # 数据库连接重试机制
    max_retries = 3
    for attempt in range(max_retries):
        try:
            with psycopg.connect(postgres_uri) as conn:
                with conn.cursor() as cur:
                    stored_count = 0
                    
                    # 逐条插入逻辑
                    for i, item in enumerate(items, 1):
                        asin = item.get("asin", "")
                        sku = item.get("sku", "")
                        item_vendor_code = item.get("vendor_code", vendor_codes[0])  # 默认使用第一个Vendor Code
                        
                        if not asin:
                            logger.warning(f"跳过没有ASIN的记录: {item}")
                            continue
                        
                        # 构建主键
                        company_id_store_id_vendor_code_asin = f"{company_id}-{store_id}-{item_vendor_code}-{asin}"
                        
                        # 处理时间字段
                        created_date = None
                        last_updated_date = None
                        
                        if item.get("created_date"):
                            try:
                                created_date = datetime.fromisoformat(item["created_date"].replace('Z', '+00:00'))
                            except:
                                pass
                                
                        if item.get("last_updated_date"):
                            try:
                                last_updated_date = datetime.fromisoformat(item["last_updated_date"].replace('Z', '+00:00'))
                            except:
                                pass
                        
                        # 构建数据 - 按照数据库schema顺序，使用提取的字段
                        values = [
                            company_id_store_id_vendor_code_asin,
                            company_id,
                            store_id,
                            marketplace,
                            item_vendor_code,
                            vc_mode,
                            asin,
                            sku,
                            json.dumps(item.get("external_product_id", [])),
                            item.get("status"),
                            item.get("item_name", ""),
                            json.dumps(item.get("style", {})),  # style - 从API提取
                            json.dumps(item.get("color", {})),  # color - 从API提取  
                            json.dumps(item.get("size", {})),  # size - 从API提取
                            json.dumps({"value": item.get("brand", "")} if item.get("brand") else {}),
                            created_date,
                            last_updated_date,
                            json.dumps(item.get("main_image", {})),
                            json.dumps(item.get("included_components", [])),  # included_components - 从API提取
                            item.get("product_category_id"),  # 从API提取
                            item.get("product_subcategory_id"),  # 从API提取
                            json.dumps(item.get("cost_price", {})),
                            json.dumps(item.get("list_price", {})),
                            json.dumps(item.get("item_weight", {})),  # 从API提取
                            json.dumps(item.get("item_depth_width_height", {})),  # item_depth_width_height - 从API提取
                            json.dumps(item.get("item_package_weight", {})),  # item_package_weight - 从API提取
                            json.dumps(item.get("item_package_dimensions", {})),  # item_package_dimensions - 从API提取
                            json.dumps(item.get("bullet_point", [])),
                            json.dumps(item.get("generic_keyword", [])),
                            json.dumps(item.get("original_api_response", item)),  # full_info - 存储原始API响应数据
                            datetime.now(timezone.utc)  # data_load_time
                        ]
                        
                        # 执行插入
                        cur.execute("""
                            INSERT INTO amazon_data.listing_details_table (
                                company_id_store_id_vendor_code_asin, company_id, store_id, marketplace, vendor_code, vc_mode, asin, sku,
                                external_product_id, status, item_name, style, color, size, brand, created_date, last_updated_date,
                                main_image, included_components, product_category_id, product_subcategory_id, cost_price, list_price,
                                item_weight, item_depth_width_height, item_package_weight, item_package_dimensions,
                                bullet_point, generic_keyword, full_info, data_load_time
                            ) VALUES (
                                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                            )
                            ON CONFLICT (company_id_store_id_vendor_code_asin) DO UPDATE SET
                                sku = EXCLUDED.sku,
                                vendor_code = EXCLUDED.vendor_code,
                                vc_mode = EXCLUDED.vc_mode,
                                status = EXCLUDED.status,
                                created_date = EXCLUDED.created_date,
                                last_updated_date = EXCLUDED.last_updated_date,
                                main_image = EXCLUDED.main_image,
                                item_name = EXCLUDED.item_name,
                                style = EXCLUDED.style,
                                color = EXCLUDED.color,
                                size = EXCLUDED.size,
                                brand = EXCLUDED.brand,
                                included_components = EXCLUDED.included_components,
                                product_category_id = EXCLUDED.product_category_id,
                                product_subcategory_id = EXCLUDED.product_subcategory_id,
                                external_product_id = EXCLUDED.external_product_id,
                                list_price = EXCLUDED.list_price,
                                cost_price = EXCLUDED.cost_price,
                                item_weight = EXCLUDED.item_weight,
                                item_depth_width_height = EXCLUDED.item_depth_width_height,
                                item_package_weight = EXCLUDED.item_package_weight,
                                item_package_dimensions = EXCLUDED.item_package_dimensions,
                                bullet_point = EXCLUDED.bullet_point,
                                generic_keyword = EXCLUDED.generic_keyword,
                                full_info = EXCLUDED.full_info,
                                data_load_time = NOW()
                        """, values)
                        
                        stored_count += 1
                        
                        if stored_count % 50 == 0:
                            print(f"    💾 已存储 {stored_count} 条记录...")
                    
                    conn.commit()
                    return stored_count
                    
        except Exception as e:
            if attempt < max_retries - 1:
                logger.warning(f"数据库连接失败，尝试重连 ({attempt + 1}/{max_retries}): {e}")
                time.sleep(1)
                continue
            else:
                logger.error(f"存储listing详情到数据库失败: {e}")
                raise
    
    return 0


def main():
    """主函数，用于直接运行测试"""
    import sys
    import os
    
    # 添加项目根目录到路径
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
    
    print('🚀 Amazon Listing Search Items 测试工具')
    print('=' * 60)
    print('选择测试模式:')
    print('1. Mock数据插入测试')
    print('2. 真实API调用测试')
    print()
    
    choice = input('请输入选择 (1 或 2): ').strip()
    
    if choice == '1':
        return test_mock_database_insert()
    elif choice == '2':
        return test_real_api_call()
    else:
        print('❌ 无效选择')
        return False


def test_mock_database_insert():
    """测试mock数据插入"""
    import json
    from datetime import datetime
    
    print('🧪 开始测试Mock数据插入...')
    print('=' * 50)
    
    # 创建mock数据
    mock_data = [
        {
            "asin": "B09Q39ZY44",
            "sku": "TEST-SKU-001",
            "vendor_code": "TEST_VENDOR_001",
            "status": "ACTIVE",
            "created_date": "2025-08-01T00:00:00Z",
            "last_updated_date": "2025-08-31T23:59:59Z",
            "main_image": {"link": "https://example.com/image1.jpg"},
            "item_name": "测试产品1",
            "brand": "测试品牌1",
            "description": "这是测试产品1的描述",
            "bullet_point": ["特点1", "特点2", "特点3"],
            "generic_keyword": ["关键词1", "关键词2"],
            "external_product_id": [{"type": "UPC", "value": "123456789012"}],
            "list_price": {"amount": 29.99, "currency": "USD"},
            "cost_price": {"amount": 15.00, "currency": "USD"},
            "fulfillment_availability": {"fulfillmentChannel": "AMAZON"},
            "procurement": {"procurementType": "DIRECT"},
            "issues": [],
            "offers": []
        },
        {
            "asin": "B08XYZ1234",
            "sku": "TEST-SKU-002",
            "vendor_code": "TEST_VENDOR_002",
            "status": "ACTIVE",
            "created_date": "2025-08-01T00:00:00Z",
            "last_updated_date": "2025-08-31T23:59:59Z",
            "main_image": {"link": "https://example.com/image2.jpg"},
            "item_name": "测试产品2",
            "brand": "测试品牌2",
            "description": "这是测试产品2的描述",
            "bullet_point": ["特点A", "特点B", "特点C"],
            "generic_keyword": ["关键词A", "关键词B"],
            "external_product_id": [{"type": "EAN", "value": "9876543210987"}],
            "list_price": {"amount": 39.99, "currency": "USD"},
            "cost_price": {"amount": 20.00, "currency": "USD"},
            "fulfillment_availability": {"fulfillmentChannel": "MERCHANT"},
            "procurement": {"procurementType": "DIRECT"},
            "issues": [],
            "offers": []
        }
    ]
    
    try:
        # 调用存储函数
        print('💾 插入Mock数据...')
        stored_count = store_listing_details_to_database(
            mock_data, 
            "TEST_COMPANY_001", 
            "TEST_STORE_001", 
            "DI",
            "US",
            ["TEST_VENDOR_001"]
        )
        
        print()
        print('✅ Mock数据插入测试完成！')
        print(f'📊 成功插入 {stored_count} 条记录')
        print(f'🏢 企业ID: TEST_COMPANY_001')
        print(f'🏪 店铺ID: TEST_STORE_001')
        print(f'🌍 市场: US')
        print(f'🏭 VC模式: DI')
        print()
        print('🔍 你可以在数据库中查看:')
        print('   Schema: amazon_data')
        print('   Table: listing_details_table')
        
        return True
        
    except Exception as e:
        print(f'❌ Mock数据插入失败: {e}')
        return False


def test_real_api_call():
    """测试真实API调用"""
    try:
        print('🔍 开始真实API调用测试...')
        print('📋 测试参数:')
        print('  - Vendor Codes: 通过模拟数据库读取')
        print('  - 企业ID: TEST_COMPANY_001')
        print('  - 店铺ID: TEST_STORE_001')
        print('  - VC模式: DI')
        print('  - 市场: US')
        print()
        
        # 模拟从数据库读取 Vendor Codes（后续会替换为真实数据库查询）
        def mock_read_vendor_codes_from_db(company_id: str, store_id: str, vc_mode: str) -> list:
            # 模拟数据库读取返回列表
            return ["WGEWH", "WGEWI", "WGEWL", "WGEWJ"]

        vendor_codes_from_db = mock_read_vendor_codes_from_db(
            company_id="TEST_COMPANY_001",
            store_id="TEST_STORE_001",
            vc_mode="DI",
        )
        print(f"  - 模拟数据库返回 Vendor Codes: {vendor_codes_from_db}")
        
        # 直接调用工具函数
        result = amazon_listing_search_items.invoke({
            "vendor_codes": vendor_codes_from_db,
            "company_id": "TEST_COMPANY_001",
            "store_id": "TEST_STORE_001",
            "vc_mode": "DI",
            "marketplace": "US"
        })
        data = json.loads(result)
        
        if data['success']:
            print()
            print('🎉 成功获取listing信息！')
            print('-' * 40)
            
            # 基本信息
            print(f'📊 总供应商数: {data["summary"]["total_vendors"]}')
            print(f'📦 总商品数: {data["summary"]["total_items_found"]}')
            print(f'💾 存储记录数: {data["summary"]["total_items_stored"]}')
            print(f'✅ 成功供应商数: {data["summary"]["successful_vendors"]}')
            print(f'❌ 失败供应商数: {data["summary"]["failed_vendors"]}')
            
            # 显示每个供应商的结果
            print()
            print('📋 供应商详细结果:')
            print('-' * 40)
            for vendor_code, result_info in data["vendor_results"].items():
                if "error" in result_info:
                    print(f'  ❌ {vendor_code}: 失败 - {result_info["error"]}')
                else:
                    print(f'  ✅ {vendor_code}: 找到 {result_info["items_found"]} 个商品，存储 {result_info["items_stored"]} 条记录')
            
            print()
            print('✅ 报告获取完成！数据已保存到数据库。')
            return 0
            
        else:
            print()
            print('❌ 获取listing信息失败')
            print('-' * 40)
            print(f'错误信息: {data["error"]}')
            print(f'错误类型: {data.get("error_type", "UNKNOWN")}')
            
            print()
            print('💡 可能的原因:')
            print('  - Vendor Code不存在或无效')
            print('  - Amazon API权限不足')
            print('  - 账户配置问题')
            print('  - 网络连接问题')
            
            print()
            print('🔧 建议:')
            print('  - 检查Vendor Code是否正确')
            print('  - 确认Amazon SP API配置')
            print('  - 联系Amazon支持确认账户状态')
            
            return 1
            
    except KeyboardInterrupt:
        print()
        print('⚠️  操作被用户中断')
        return 1
    except Exception as e:
        print()
        print(f'❌ 程序执行出错: {e}')
        return 1


if __name__ == "__main__":
    sys.exit(main())
