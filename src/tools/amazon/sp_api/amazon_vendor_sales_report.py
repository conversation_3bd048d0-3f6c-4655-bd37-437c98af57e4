#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Vendor Sales Report Tool

独立的供应商销售报告工具，从拉取报告到解析数据的完整流程。
"""

from __future__ import annotations

import json
import logging
import sys
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Annotated, Any, Dict, List, Optional, Union

from langchain_core.tools import tool
from src.tools.decorators import log_io

# 导入common模块
from src.tools.amazon.sp_api.common.reports import ReportsClient, ReportRequest, iso_utc
from src.tools.amazon.sp_api.common.sp_config import AmazonSPConfig

logger = logging.getLogger(__name__)


@tool(description="获取亚马逊供应商销售报告数据。支持指定日期范围，自动处理报告创建、状态检查和数据解析。返回结构化的销售数据，包含总销售额、销量、ASIN表现等关键指标。")
@log_io
def amazon_vendor_sales_report(
    company_id: Annotated[str, "企业ID，前端传递"] = None,
    store_id: Annotated[str, "店铺ID，前端传递"] = None,
    marketplace: Annotated[str, "销售市场，前端传递，如US、UK、DE等"] = None,
    start_date: Annotated[str, "开始日期，格式：YYYY-MM-DD"] = None,
    end_date: Annotated[str, "结束日期，格式：YYYY-MM-DD"] = None,
    wait_timeout: Annotated[int, "等待超时时间（秒），默认300"] = 300,
    output_dir: Annotated[str, "输出目录，默认reports"] = "reports",
) -> str:
    """
    获取亚马逊供应商销售报告数据
    
    Args:
        company_id: 企业ID，前端传递
        store_id: 店铺ID，前端传递  
        marketplace: 销售市场，前端传递
        start_date: 开始日期，格式：YYYY-MM-DD
        end_date: 结束日期，格式：YYYY-MM-DD
        wait_timeout: 等待超时时间（秒）
        output_dir: 输出目录
        
    Returns:
        JSON格式的销售报告数据
    """
    try:
        # 验证必需的前端参数
        if not company_id:
            return json.dumps({
                "success": False,
                "error": "Data acquisition failed",
                "data_source": "GET_VENDOR_SALES_REPORT",
                "error_type": "MISSING_COMPANY_ID",
                "api_response": None
            }, ensure_ascii=False)
            
        if not store_id:
            return json.dumps({
                "success": False,
                "error": "Data acquisition failed", 
                "data_source": "GET_VENDOR_SALES_REPORT",
                "error_type": "MISSING_STORE_ID",
                "api_response": None
            }, ensure_ascii=False)
            
        if not marketplace:
            return json.dumps({
                "success": False,
                "error": "Data acquisition failed",
                "data_source": "GET_VENDOR_SALES_REPORT", 
                "error_type": "MISSING_MARKETPLACE",
                "api_response": None
            }, ensure_ascii=False)
        # 初始化配置
        config = AmazonSPConfig()
        reports_client = ReportsClient(config)
        
        # 处理日期参数
        if not start_date or not end_date:
            # 默认获取2天前的数据（T-2）
            two_days_ago = datetime.now(timezone.utc) - timedelta(days=2)
            start_date = two_days_ago.strftime("%Y-%m-%d")
            end_date = start_date
        
        # 使用默认市场ID
        marketplace_ids = ["ATVPDKIKX0DER"]
        
        # 智能日期回退策略：从T-2开始逐日回退寻找可用数据
        max_days_back = 4  # 最多回退4天（T-2到T-5）
        base_date = datetime.strptime(start_date, "%Y-%m-%d")
        
        # 初始化变量
        final_stored_count = 0
        final_database_error = None
        report_id = None
        document_id = None
        output_path = None
        
        for days_back in range(max_days_back):
            try_date = base_date - timedelta(days=days_back)
            try_date_str = try_date.strftime("%Y-%m-%d")
            
            if days_back == 0:
                logger.info(f"🔍 尝试日期: {try_date_str} (T-2)")
            else:
                logger.info(f"🔍 尝试日期: {try_date_str} (T-{2+days_back})")
            
            # 创建报告请求
            report_request = ReportRequest(
                report_type="GET_VENDOR_SALES_REPORT",
                data_start_time=datetime.strptime(f"{try_date_str}T00:00:00Z", "%Y-%m-%dT%H:%M:%SZ").isoformat().replace("+00:00", "Z"),
                data_end_time=datetime.strptime(f"{try_date_str}T23:59:59Z", "%Y-%m-%dT%H:%M:%SZ").isoformat().replace("+00:00", "Z"),
                marketplace_ids=marketplace_ids,
                report_options={
                    "distributorView": "MANUFACTURING",
                    "reportPeriod": "DAY",
                    "sellingProgram": "RETAIL"
                }
            )
            
            try:
                # 使用ReportsClient的完整流程：创建 -> 等待 -> 下载 -> 解析
                logger.info(f"创建报告请求...")
                report_id = reports_client.create(report_request)
                logger.info(f"✅ 报告创建成功！报告ID: {report_id}")
                
                logger.info(f"等待报告处理完成...")
                document_id = reports_client.wait_done(report_id, timeout_seconds=wait_timeout)
                logger.info(f"📄 报告处理完成，状态: DONE")
                
                logger.info(f"下载报告文档: {document_id}")
                download_url = reports_client.get_download_url(document_id)
                
                output_path = Path(output_dir) / f"vendor_sales_report_{try_date_str}_{try_date_str}.json"
                reports_client.download_to_file(download_url, output_path)
                logger.info(f"报告下载完成: {output_path}")
                
                # 解析报告数据
                logger.info(f"解析报告数据: {output_path}")
                parsed_data = parse_vendor_sales_report(str(output_path))
                
                if not parsed_data["success"]:
                    raise Exception(f"报告数据为空: {parsed_data['error']}")
                
                # 存储数据到数据库
                try:
                    logger.info(f"存储报告数据到数据库...")
                    stored_count = store_vendor_sales_to_database(
                        company_id=company_id,
                        store_id=store_id,
                        marketplace=marketplace,
                        report_id=report_id,
                        document_id=document_id,
                        file_path=str(output_path),
                        parsed_data=parsed_data
                    )
                    
                    # 如果一切成功，更新实际使用的日期并退出循环
                    start_date = try_date_str
                    end_date = try_date_str
                    final_stored_count = stored_count
                    final_database_error = None
                    logger.info(f"🎉 成功获取 {try_date_str} 的销售报告并存储 {stored_count} 条记录！")
                    break
                    
                except Exception as db_error:
                    # 数据库错误不影响报告获取，但需要记录
                    logger.warning(f"⚠️  数据库存储失败: {db_error}")
                    start_date = try_date_str
                    end_date = try_date_str
                    final_stored_count = 0
                    final_database_error = str(db_error)
                    logger.info(f"🎉 成功获取 {try_date_str} 的销售报告，但数据库存储失败")
                    break
                
            except Exception as e:
                # 任何失败都视为该日期不可用，继续尝试下一个日期
                error_msg = str(e)
                logger.warning(f"日期 {try_date_str} 处理失败: {error_msg}")
                
                if days_back < max_days_back - 1:
                    logger.info(f"🔄 尝试更早的日期...")
                    continue
                else:
                    # 所有日期都尝试失败了，返回标准失败格式
                    return json.dumps({
                        "success": False,
                        "error": "Data acquisition failed",
                        "data_source": "GET_VENDOR_SALES_REPORT",
                        "error_type": "RETRY_EXHAUSTED",
                        "api_response": None
                    }, ensure_ascii=False)
        
        # 检查是否成功获取了数据
        if report_id is None:
            # 所有日期都尝试失败了，返回标准失败格式
            return json.dumps({
                "success": False,
                "error": "Data acquisition failed",
                "data_source": "GET_VENDOR_SALES_REPORT",
                "error_type": "RETRY_EXHAUSTED",
                "api_response": None
            }, ensure_ascii=False)
        
        # 如果执行到这里，说明成功获取了数据
        # 返回标准响应格式
        if final_database_error:
            # 数据获取成功，但数据库存储失败
            result = {
                "success": True,
                "data_source": "GET_VENDOR_SALES_REPORT",
                "operation_id": report_id,
                "operation_time": datetime.now(timezone.utc).isoformat(),
                "database_stored": 0,
                "database_error": final_database_error,
                "report_id": report_id,
                "document_id": document_id,
                "file_path": str(output_path) if output_path else None,
                "file_size": output_path.stat().st_size if output_path and output_path.exists() else 0,
                "date_range": {
                    "start": start_date,
                    "end": end_date
                }
            }
        else:
            # 数据获取和存储都成功
            result = {
                "success": True,
                "data_source": "GET_VENDOR_SALES_REPORT", 
                "operation_id": report_id,
                "operation_time": datetime.now(timezone.utc).isoformat(),
                "database_stored": final_stored_count,
                "report_id": report_id,
                "document_id": document_id,
                "file_path": str(output_path) if output_path else None,
                "file_size": output_path.stat().st_size if output_path and output_path.exists() else 0,
                "date_range": {
                    "start": start_date,
                    "end": end_date
                }
            }
        
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"供应商销售报告工具执行失败: {e}")
        return json.dumps({
            "success": False,
            "error": "Data acquisition failed",
            "data_source": "GET_VENDOR_SALES_REPORT",
            "error_type": "SYSTEM_ERROR",
            "api_response": None
        }, ensure_ascii=False)


def store_vendor_sales_to_database(
    company_id: str,
    store_id: str,
    marketplace: str,
    report_id: str,
    document_id: str,
    file_path: str,
    parsed_data: Dict[str, Any]
) -> int:
    """
    存储供应商销售报告数据到数据库
    
    Args:
        company_id: 企业ID
        store_id: 店铺ID
        marketplace: 销售市场
        report_id: 报告ID
        document_id: 文档ID
        file_path: 文件路径
        parsed_data: 解析后的数据
        
    Returns:
        成功插入的记录数
    """
    import psycopg
    import json
    import os
    
    POSTGRES_URI = os.getenv("POSTGRES_URI")
    if not POSTGRES_URI:
        logger.error("POSTGRES_URI not configured")
        raise Exception("Database not configured")
    
    # 1. 读取并解析报告文件
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
    except Exception as e:
        logger.error(f"Failed to read report file: {e}")
        raise Exception(f"Cannot read report file: {e}")
    
    # 3. 提取销售数据
    sales_by_asin = report_data.get("salesByAsin", [])
    sales_aggregate = report_data.get("salesAggregate", [])
    report_spec = report_data.get("reportSpecification", {})
    
    # 使用ASIN级别数据，如果没有则使用汇总数据
    sales_data_list = sales_by_asin if sales_by_asin else sales_aggregate
    
    if not sales_data_list:
        logger.warning("No sales data found in report")
        return 0
    
    # 4. 准备插入数据
    insert_data = []
    date_str = report_spec.get("dataStartTime", "")[:10]  # 取日期部分
    
    for item in sales_data_list:
        asin = item.get("asin", "")
        if not asin:
            continue
            
        # 处理revenue字段（可能是数字或对象）
        revenue = item.get("orderedRevenue", 0)
        if isinstance(revenue, dict):
            revenue_amount = revenue.get("amount", 0)
        else:
            revenue_amount = revenue
            
        ordered_units = item.get("orderedUnits", 0)
        
        # 计算平均销售单价
        average_selling_price = revenue_amount / ordered_units if ordered_units > 0 else 0
        
        # 生成主键：company_id-store_id-date-asin
        primary_key = f"{company_id}-{store_id}-{date_str}-{asin}"
            
        insert_data.append({
            "company_id_store_id_date_asin": primary_key,
            "company_id": company_id,
            "store_id": store_id,
            "marketplace": marketplace,
            "date": report_spec.get("dataStartTime", ""),
            "asin": asin,
            "ordered_units": ordered_units,
            "ordered_revenue": revenue_amount,
            "average_selling_price": average_selling_price
        })
    
    # 5. 确保表存在
    create_vendor_sales_table_if_not_exists()
    
    # 6. 批量插入数据库
    try:
        with psycopg.connect(POSTGRES_URI) as conn:
            with conn.cursor() as cur:
                insert_sql = """
                INSERT INTO amazon_data.sales_report_table (
                    company_id_store_id_date_asin, company_id, store_id, marketplace, date,
                    asin, ordered_units, ordered_revenue, average_selling_price
                ) VALUES (
                    %(company_id_store_id_date_asin)s, %(company_id)s, %(store_id)s, %(marketplace)s, %(date)s,
                    %(asin)s, %(ordered_units)s, %(ordered_revenue)s, %(average_selling_price)s
                )
                ON CONFLICT (company_id_store_id_date_asin) 
                DO UPDATE SET
                    ordered_units = EXCLUDED.ordered_units,
                    ordered_revenue = EXCLUDED.ordered_revenue,
                    average_selling_price = EXCLUDED.average_selling_price,
                    data_load_time = NOW()
                """
                
                cur.executemany(insert_sql, insert_data)
                conn.commit()
                stored_count = cur.rowcount
                logger.info(f"✅ 成功存储 {stored_count} 条ASIN销售记录")
                return stored_count
                
    except Exception as e:
        logger.error(f"Database insertion failed: {e}")
        raise Exception(f"Failed to store data: {e}")


def create_vendor_sales_table_if_not_exists():
    """在工具内创建必要的数据表"""
    import psycopg
    import os
    
    POSTGRES_URI = os.getenv("POSTGRES_URI")
    create_schema_and_table_sql = """
    -- 创建schema（如果不存在）
    CREATE SCHEMA IF NOT EXISTS amazon_data;
    
    -- 创建表（如果不存在）
    CREATE TABLE IF NOT EXISTS amazon_data.sales_report_table (
        company_id_store_id_date_asin VARCHAR(200) PRIMARY KEY,
        company_id VARCHAR(50) NOT NULL,
        store_id VARCHAR(50) NOT NULL,
        marketplace VARCHAR(10) NOT NULL,
        date TIMESTAMPTZ NOT NULL,
        asin VARCHAR(20) NOT NULL,
        ordered_units INTEGER NOT NULL DEFAULT 0,
        ordered_revenue DECIMAL(15,4) NOT NULL DEFAULT 0.00,
        average_selling_price DECIMAL(15,4) NOT NULL DEFAULT 0.00,
        data_load_time TIMESTAMPTZ NOT NULL DEFAULT NOW()
    );
    """
    
    try:
        with psycopg.connect(POSTGRES_URI) as conn:
            with conn.cursor() as cur:
                cur.execute(create_schema_and_table_sql)
                conn.commit()
                logger.info("✅ Amazon schema and sales_report table ready")
    except Exception as e:
        logger.warning(f"Schema/Table creation check failed: {e}")
        # 不抛出异常，让主流程继续


def parse_vendor_sales_report(file_path: str) -> Dict[str, Any]:
    """
    解析供应商销售报告数据
    
    Args:
        file_path: 报告文件路径
        
    Returns:
        解析后的数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        # 提取报告规格和数据
        report_spec = report_data.get("reportSpecification", {})
        
        # Vendor Sales Report的数据结构
        sales_aggregate = report_data.get("salesAggregate", [])
        sales_by_asin = report_data.get("salesByAsin", [])
        
        # 检查是否有任何销售数据
        if not sales_aggregate and not sales_by_asin:
            return {
                "success": False,
                "error": "报告数据为空"
            }
        
        # 使用ASIN级别的数据，如果没有则使用汇总数据
        report_data_list = sales_by_asin if sales_by_asin else sales_aggregate
        
        # 只返回成功状态，表示数据解析成功
        return {
            "success": True,
            "has_data": len(report_data_list) > 0
        }
        
    except Exception as e:
        logger.error(f"解析供应商销售报告失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def test_mock_database_insert():
    """测试mock数据插入"""
    import json
    from datetime import datetime
    
    print('🧪 开始测试Mock数据插入...')
    print('=' * 50)
    
    # 创建mock数据
    mock_data = {
        "company_id": "TEST_COMPANY_001",
        "store_id": "TEST_STORE_001", 
        "marketplace": "US",
        "report_id": "mock_report_123456",
        "document_id": "mock_doc_123456",
        "sales_data": [
            {
                "asin": "B09Q39ZY44",
                "orderedUnits": 150,
                "orderedRevenue": {"amount": 2999.50, "currencyCode": "USD"}
            },
            {
                "asin": "B08XYZ1234", 
                "orderedUnits": 75,
                "orderedRevenue": {"amount": 1499.25, "currencyCode": "USD"}
            },
            {
                "asin": "B07ABC5678",
                "orderedUnits": 200,
                "orderedRevenue": {"amount": 3998.00, "currencyCode": "USD"}
            }
        ],
        "reportSpecification": {
            "dataStartTime": "2025-08-23T00:00:00Z",
            "dataEndTime": "2025-08-23T23:59:59Z"
        }
    }
    
    try:
        # 调用存储函数（会自动创建表）
        print('💾 插入Mock数据...')
        stored_count = store_vendor_sales_to_database_mock(mock_data)
        
        print()
        print('✅ Mock数据插入测试完成！')
        print(f'📊 成功插入 {stored_count} 条记录')
        print(f'🏢 企业ID: {mock_data["company_id"]}')
        print(f'🏪 店铺ID: {mock_data["store_id"]}')
        print(f'🌍 市场: {mock_data["marketplace"]}')
        print(f'📅 日期: 2025-08-23')
        print()
        print('🔍 你可以在数据库中查看:')
        print('   Schema: amazon_data')
        print('   Table: sales_report')
        
        return True
        
    except Exception as e:
        print(f'❌ Mock数据插入失败: {e}')
        return False


def store_vendor_sales_to_database_mock(mock_data):
    """使用mock数据进行数据库插入测试"""
    import psycopg
    import os
    
    POSTGRES_URI = os.getenv("POSTGRES_URI")
    if not POSTGRES_URI:
        raise Exception("Database not configured")
    
    # 确保表存在
    create_vendor_sales_table_if_not_exists()
    
    # 准备插入数据
    insert_data = []
    date_str = mock_data["reportSpecification"]["dataStartTime"][:10]
    
    for item in mock_data["sales_data"]:
        asin = item["asin"]
        revenue = item["orderedRevenue"]["amount"]
        units = item["orderedUnits"]
        avg_price = revenue / units if units > 0 else 0
        primary_key = f"{mock_data['company_id']}-{mock_data['store_id']}-{date_str}-{asin}"
        
        insert_data.append({
            "company_id_store_id_date_asin": primary_key,
            "company_id": mock_data["company_id"],
            "store_id": mock_data["store_id"],
            "marketplace": mock_data["marketplace"],
            "date": mock_data["reportSpecification"]["dataStartTime"],
            "asin": asin,
            "ordered_units": units,
            "ordered_revenue": revenue,
            "average_selling_price": avg_price
        })
    
    # 批量插入
    with psycopg.connect(POSTGRES_URI) as conn:
        with conn.cursor() as cur:
            insert_sql = """
            INSERT INTO amazon_data.sales_report_table (
                company_id_store_id_date_asin, company_id, store_id, marketplace, date,
                asin, ordered_units, ordered_revenue, average_selling_price
            ) VALUES (
                %(company_id_store_id_date_asin)s, %(company_id)s, %(store_id)s, %(marketplace)s, %(date)s,
                %(asin)s, %(ordered_units)s, %(ordered_revenue)s, %(average_selling_price)s
            )
            ON CONFLICT (company_id_store_id_date_asin) 
            DO UPDATE SET
                ordered_units = EXCLUDED.ordered_units,
                ordered_revenue = EXCLUDED.ordered_revenue,
                average_selling_price = EXCLUDED.average_selling_price,
                data_load_time = NOW()
            """
            
            cur.executemany(insert_sql, insert_data)
            conn.commit()
            return cur.rowcount


def main():
    """主函数，用于直接运行测试"""
    import sys
    import os
    
    # 添加项目根目录到路径
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
    
    print('🚀 Amazon Vendor Sales Report 测试工具')
    print('=' * 60)
    print('选择测试模式:')
    print('1. Mock数据插入测试')
    print('2. 真实API调用测试')
    print()
    
    choice = input('请输入选择 (1 或 2): ').strip()
    
    if choice == '1':
        return test_mock_database_insert()
    elif choice == '2':
        return test_real_api_call()
    else:
        print('❌ 无效选择')
        return False


def test_real_api_call():
    """测试真实API调用"""
    try:
        print('🔍 开始真实API调用测试...')
        result = amazon_vendor_sales_report.func(
            company_id="TEST_COMPANY_001",
            store_id="TEST_STORE_001", 
            marketplace="US"
        )
        data = json.loads(result)
        
        if data['success']:
            print()
            print('🎉 成功获取供应商销售报告！')
            print('-' * 40)
            
            # 基本信息
            date_range = data.get('date_range', {})
            print(f'📅 数据日期: {date_range.get("start", "未知")[:10] if date_range.get("start") else "未知"}')
            print(f'📄 报告ID: {data.get("report_id", "N/A")}')
            print(f'📄 文档ID: {data.get("document_id", "N/A")}')
            print(f'📁 文件路径: {data.get("file_path", "N/A")}')
            print(f'💾 文件大小: {data.get("file_size", 0):,} bytes')
            
            # 如果有解析的数据，显示汇总信息
            if 'parsed_data' in data and 'summary' in data['parsed_data']:
                summary = data['parsed_data']['summary']
                print()
                print('📊 数据汇总:')
                print('-' * 40)
                print(f'💰 总销售额: ${summary.get("total_revenue", 0):,.2f}')
                print(f'📦 总销量: {summary.get("total_units", 0):,} 件')
                print(f'🏷️  独特ASIN数: {summary.get("unique_asins", 0):,}')
                print(f'📈 平均订单价值: ${summary.get("avg_order_value", 0):,.2f}')
                print(f'📝 总记录数: {summary.get("total_records", 0):,}')
                
                # 显示表现最好的ASIN
                if 'top_performers' in data['parsed_data'] and data['parsed_data']['top_performers']:
                    print()
                    print('🏆 表现最佳的ASIN (Top 5):')
                    print('-' * 40)
                    for i, performer in enumerate(data['parsed_data']['top_performers'][:5], 1):
                        asin = performer.get('asin', 'N/A')
                        revenue = performer.get('total_revenue', 0)
                        units = performer.get('total_units', 0)
                        print(f'{i}. ASIN: {asin} | 销售额: ${revenue:,.2f} | 销量: {units:,}')
            
            print()
            print('✅ 报告获取完成！数据已保存到本地文件。')
            return 0
            
        else:
            print()
            print('❌ 获取销售报告失败')
            print('-' * 40)
            print(f'错误信息: {data["error"]}')
            print(f'错误类型: {data.get("error_type", "UNKNOWN")}')
            
            print()
            print('💡 可能的原因:')
            print('  - 供应商数据通常有2-4天延迟')
            print('  - 请求的日期范围内没有销售数据')
            print('  - Amazon API权限不足')
            print('  - 账户配置问题')
            
            print()
            print('🔧 建议:')
            print('  - 稍后再试（数据延迟）')
            print('  - 检查Amazon SP API配置')
            print('  - 联系Amazon支持确认账户状态')
            print('  - 尝试更早的日期范围')
            
            return 1
            
    except KeyboardInterrupt:
        print()
        print('⚠️  操作被用户中断')
        return 1
    except Exception as e:
        print()
        print(f'❌ 程序执行出错: {e}')
        return 1


if __name__ == "__main__":
    sys.exit(main())
