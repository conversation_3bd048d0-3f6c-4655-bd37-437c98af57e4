#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Catalog Search Items Tool

从数据库读取去重后的ASIN，获取亚马逊商品目录信息，重点关注分类代码和销售排名数据。
"""

import json
import logging
import os
import psycopg
import sys
from typing import Any, Dict, List, Optional
from datetime import datetime

from langchain.tools import tool
from typing_extensions import Annotated

from src.tools.decorators import log_io
from src.tools.amazon.sp_api.common.sp_config import AmazonSPConfig
from sp_api.api import CatalogItems

logger = logging.getLogger(__name__)

# 市场缩写到市场ID的映射
MARKETPLACE_MAPPING = {
    "US": "ATVPDKIKX0DER",  # 美国
    "DE": "A1PA6795UKMFR9",  # 德国
    "ES": "A1RKKUPIHCS9HS",  # 西班牙
    "FR": "A13V1IB3VIYZZH",  # 法国
    "IN": "A21TJRUUN4KGV",  # 印度
    "IT": "APJ6JRA9NG5V4",  # 意大利
    "UK": "A1F83G8C2ARO7P",  # 英国
    "JP": "A1VC38T7YXB528",  # 日本
    "CA": "AAHKV2X7AFYLW",  # 加拿大
    "MX": "A1AM78C64UM0Y8",  # 墨西哥
    "BR": "A2Q3Y263D00KWC",  # 巴西
    "AE": "A2VIGQ35RCS4UG",  # 阿联酋
    "SA": "A1805FZT6KMSX2",  # 沙特阿拉伯
    "SE": "A17E79C6D8DWNP",  # 瑞典
    "NL": "A2NODRKZPJZB6F",  # 荷兰
    "PL": "A1C3SOZRARQ6R3",  # 波兰
    "EG": "A1XFKOSQHF3NL0",  # 埃及
}


def get_unique_asins_from_db() -> List[str]:
    """从数据库获取去重后的ASIN列表"""
    try:
        # 获取数据库连接
        postgres_uri = os.getenv("POSTGRES_URI")
        if not postgres_uri:
            logger.error("POSTGRES_URI 环境变量未设置")
            return []
        
        # 连接数据库
        with psycopg.connect(postgres_uri) as conn:
            with conn.cursor() as cur:
                # 查询去重后的ASIN，只从listing_details_table获取
                cur.execute("""
                    SELECT DISTINCT asin 
                    FROM amazon_data.listing_details_table 
                    WHERE asin IS NOT NULL AND asin != '' AND asin ~ '^B[0-9A-Z]{9}$'
                    ORDER BY asin
                """)
                
                results = cur.fetchall()
                asins = [row[0] for row in results]
                
                logger.info(f"从数据库获取到 {len(asins)} 个去重后的ASIN")
                return asins
                
    except Exception as e:
        logger.error(f"数据库查询失败: {e}")
        return []


@tool(description="从数据库读取去重后的ASIN，获取亚马逊商品目录信息，重点关注分类代码和销售排名数据。返回商品信息包括：ASIN、分类代码、分类标题、销售排名等，并存储到catalog_rank_table数据库表中。")
@log_io
def amazon_catalog_search_items(
    company_id: Annotated[str, "企业ID，前端传递"],
    store_id: Annotated[str, "店铺ID，前端传递"],
    marketplace: Annotated[str, "市场代码，如US、CA、UK等"] = "US",
) -> str:
    """
    从数据库读取去重后的ASIN，获取商品目录信息
    
    Args:
        company_id: 企业ID，前端传递
        store_id: 店铺ID，前端传递
        marketplace: 市场代码，如US、CA、UK等
        
    Returns:
        搜索结果的JSON字符串
    """
    try:
        # 验证必需的前端参数
        if not company_id:
            return json.dumps({
                "success": False,
                "error": "Data acquisition failed",
                "data_source": "GET_CATALOG_ITEMS",
                "error_type": "MISSING_COMPANY_ID",
                "api_response": None
            }, ensure_ascii=False)
            
        if not store_id:
            return json.dumps({
                "success": False,
                "error": "Data acquisition failed", 
                "data_source": "GET_CATALOG_ITEMS",
                "error_type": "MISSING_STORE_ID",
                "api_response": None
            }, ensure_ascii=False)
        
        # 配置SP API
        config = AmazonSPConfig()
        config.validate()
        credentials = config.build_credentials()
        
        # 创建Catalog API客户端
        catalog_client = CatalogItems(credentials=credentials)
        
        # 处理市场ID
        marketplace_id = MARKETPLACE_MAPPING.get(marketplace)
        if not marketplace_id:
            return json.dumps({
                "success": False,
                "error": f"不支持的市场代码: {marketplace}",
                "details": {"marketplace": marketplace},
                "execution_time": datetime.now().isoformat()
            }, ensure_ascii=False)
        
        # 只请求salesRanks和vendorDetails作为included_data
        included_data_list = ['salesRanks', 'vendorDetails']
        
        logger.info(f"开始从数据库获取ASIN，市场: {marketplace} -> {marketplace_id}")
        
        # 从数据库获取去重后的ASIN
        unique_asins = get_unique_asins_from_db()
        
        if not unique_asins:
            return json.dumps({
                "success": False,
                "error": "数据库中没有找到有效的ASIN",
                "details": {
                    "company_id": company_id,
                    "store_id": store_id,
                    "marketplace": marketplace
                },
                "execution_time": datetime.now().isoformat()
            }, ensure_ascii=False)
        
        logger.info(f"开始处理 {len(unique_asins)} 个ASIN")
        
        # 处理每个ASIN
        processed_items = []
        failed_asins = []
        
        # 分批处理，每批处理20个ASIN（Amazon API允许的最大批量）
        batch_size = 20
        total_batches = (len(unique_asins) + batch_size - 1) // batch_size
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, len(unique_asins))
            batch_asins = unique_asins[start_idx:end_idx]
            
            logger.info(f"处理第 {batch_num + 1}/{total_batches} 批，ASIN范围: {start_idx + 1}-{end_idx}")
            
            # 每批处理完后存储到数据库
            batch_processed_items = []
            batch_failed_asins = []
            
            for i, asin in enumerate(batch_asins):
                try:
                    logger.info(f"处理第 {start_idx + i + 1}/{len(unique_asins)} 个ASIN: {asin}")
                    
                    # 调用亚马逊目录API
                    response = catalog_client.get_catalog_item(
                        asin=asin.strip(),
                        marketplaceIds=[marketplace_id],
                        includedData=included_data_list
                    )
                    
                    if response and hasattr(response, 'payload') and response.payload:
                        # 处理响应数据
                        item_data = response.payload
                        
                        # 提取vendorDetails信息
                        vendor_details = item_data.get('vendorDetails', [])
                        category_code = None
                        sub_category_code = None
                        
                        if vendor_details:
                            for vendor_detail in vendor_details:
                                if vendor_detail.get('marketplaceId') == marketplace_id:
                                    category_code = vendor_detail.get('categoryCode')
                                    sub_category_code = vendor_detail.get('subcategoryCode')
                                    break
                        
                        # 提取销售排名信息
                        sales_ranks = item_data.get('salesRanks', [])
                        ranks = item_data.get('ranks', [])
                        
                        # 解析排名数据
                        top_category_rank = None
                        sub_category_rank = None
                        category_title = None
                        sub_category_title = None
                        
                        # 优先使用salesRanks，如果没有则使用ranks
                        rank_data = sales_ranks if sales_ranks else ranks
                        
                        if rank_data:
                            # 处理嵌套的ranks结构
                            for marketplace_ranks in rank_data:
                                if marketplace_ranks.get('marketplaceId') == marketplace_id:
                                    inner_ranks = marketplace_ranks.get('ranks', [])
                                    
                                    # 分类排名
                                    classification_ranks = []
                                    subcategory_ranks = []
                                    display_group_ranks = []
                                    
                                    for rank_item in inner_ranks:
                                        # 根据title判断排名类型
                                        title = rank_item.get('title', '')
                                        if 'Patio, Lawn & Garden' in title or 'Lawn & Garden' in title:
                                            # 大类排名
                                            classification_ranks.append(rank_item)
                                        elif 'Deck Boxes' in title or 'Boxes' in title:
                                            # 小类排名
                                            subcategory_ranks.append(rank_item)
                                        else:
                                            # 其他类型排名
                                            display_group_ranks.append(rank_item)
                                    
                                    # 获取大类排名和标题
                                    if classification_ranks:
                                        top_category_rank = classification_ranks[0].get('rank')
                                        category_title = classification_ranks[0].get('title')
                                    
                                    # 获取小类排名和标题
                                    if subcategory_ranks:
                                        sub_category_rank = subcategory_ranks[0].get('rank')
                                        sub_category_title = subcategory_ranks[0].get('title')
                                    
                                    break
                        
                        # 构建处理后的商品数据
            processed_item = {
                            "asin": asin,
                            "category_code": category_code,
                            "sub_category_code": sub_category_code,
                            "category_title": category_title,
                            "sub_category_title": sub_category_title,
                            "top_category_rank": top_category_rank,
                            "sub_category_rank": sub_category_rank
                        }
                        
                        batch_processed_items.append(processed_item)
                        logger.info(f"✅ 成功处理ASIN {asin}")
                    else:
                        logger.warning(f"⚠️  ASIN {asin} API响应为空")
                        batch_failed_asins.append(asin)
                        
                except Exception as e:
                    logger.error(f"❌ 处理ASIN {asin} 时出错: {str(e)}")
                    batch_failed_asins.append(asin)
            
            # 每批处理完后立即存储到数据库
            if batch_processed_items:
                try:
                    # 将marketplace_id转换为市场缩写
                    marketplace_abbr = marketplace
                    logger.info(f"第 {batch_num + 1} 批处理完成，开始存储到数据库...")
                    
                    # 存储到数据库
                    batch_stored_count = store_catalog_rank_to_database(
                        batch_processed_items, 
                        marketplace_abbr, 
                        company_id, 
                        store_id
                    )
                    
                    logger.info(f"第 {batch_num + 1} 批成功存储 {batch_stored_count} 条记录到数据库")
                    
                    # 将成功的记录添加到总列表中
                    processed_items.extend(batch_processed_items)
                    
                except Exception as e:
                    logger.error(f"第 {batch_num + 1} 批存储到数据库失败: {str(e)}")
                    # 即使存储失败，也要记录处理成功的项目
                    processed_items.extend(batch_processed_items)
            
            # 记录失败的ASIN
            failed_asins.extend(batch_failed_asins)
            
            # 每批处理完后显示进度
            logger.info(f"第 {batch_num + 1}/{total_batches} 批处理完成，成功: {len(batch_processed_items)}, 失败: {len(batch_failed_asins)}")
        
        logger.info(f"所有ASIN处理完成，总计: {len(unique_asins)}, 成功: {len(processed_items)}, 失败: {len(failed_asins)}")
        
        # 最终统计存储数量
        try:
            # 查询数据库中的实际存储数量
            postgres_uri = os.getenv("POSTGRES_URI")
            if postgres_uri:
                with psycopg.connect(postgres_uri) as conn:
                    with conn.cursor() as cur:
                        cur.execute("""
                            SELECT COUNT(*) FROM amazon_data.catalog_rank_table 
                            WHERE company_id = %s AND store_id = %s AND marketplace = %s
                        """, (company_id, store_id, marketplace))
                        stored_count = cur.fetchone()[0]
                        logger.info(f"数据库中实际存储记录数: {stored_count}")
            else:
                stored_count = len(processed_items)
                logger.warning("无法查询数据库，使用处理记录数作为存储数")
        except Exception as e:
            logger.error(f"查询数据库存储数量失败: {str(e)}")
            stored_count = len(processed_items)
        
        # 构建返回结果
        result = {
            "success": True,
            "data_source": "GET_CATALOG_ITEMS",
            "operation_id": f"catalog_search_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "operation_time": datetime.now().isoformat(),
            "database_stored": stored_count,
            "marketplace": marketplace,
            "included_data": included_data_list,
            "execution_time": datetime.now().isoformat()
        }
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"获取商品目录失败: {str(e)}")
        return json.dumps({
            "success": False,
            "error": f"获取商品目录失败: {str(e)}",
            "details": {
                "marketplace": marketplace
            },
            "execution_time": datetime.now().isoformat()
        }, ensure_ascii=False)


def store_catalog_rank_to_database(processed_items: List[Dict], marketplace: str, company_id: str, store_id: str) -> int:
    """将处理后的商品排名数据存储到数据库"""
    try:
        # 获取数据库连接
        postgres_uri = os.getenv("POSTGRES_URI")
        if not postgres_uri:
            logger.error("POSTGRES_URI 环境变量未设置")
            return 0
        
        # 连接数据库
        with psycopg.connect(postgres_uri) as conn:
            with conn.cursor() as cur:
                # 准备插入数据
                insert_sql = """
                INSERT INTO amazon_data.catalog_rank_table (
                    company_id_store_id_asin, company_id, store_id, marketplace, asin,
                    category_code, sub_category_code, category_title, sub_category_title,
                    top_category_rank, sub_category_rank, data_load_time
                ) VALUES (
                    %(company_id_store_id_asin)s, %(company_id)s, %(store_id)s, %(marketplace)s, %(asin)s,
                    %(category_code)s, %(sub_category_code)s, %(category_title)s, %(sub_category_title)s,
                    %(top_category_rank)s, %(sub_category_rank)s, %(data_load_time)s
                )
                ON CONFLICT (company_id_store_id_asin) 
                DO UPDATE SET
                    category_code = EXCLUDED.category_code,
                    sub_category_code = EXCLUDED.sub_category_code,
                    category_title = EXCLUDED.category_title,
                    sub_category_title = EXCLUDED.sub_category_title,
                    top_category_rank = EXCLUDED.top_category_rank,
                    sub_category_rank = EXCLUDED.sub_category_rank,
                    data_load_time = EXCLUDED.data_load_time
                """
                
                # 批量插入数据
                values = []
                for item in processed_items:
                    asin = item.get('asin', '')
                    if not asin:
                        continue
                    
                    # 构建主键
                    primary_key = f"{company_id}_{store_id}_{asin}"
                    
                    values.append({
                        'company_id_store_id_asin': primary_key,
                        'company_id': company_id,
                        'store_id': store_id,
                        'marketplace': marketplace,
                        'asin': asin,
                        'category_code': item.get('category_code'),
                        'sub_category_code': item.get('sub_category_code'),
                        'category_title': item.get('category_title'),
                        'sub_category_title': item.get('sub_category_title'),
                        'top_category_rank': item.get('top_category_rank'),
                        'sub_category_rank': item.get('sub_category_rank'),
                        'data_load_time': datetime.now()
                    })
                
                if values:
                    cur.executemany(insert_sql, values)
                    conn.commit()
                    logger.info(f"成功存储 {len(values)} 条记录到数据库")
                    return len(values)
                else:
                    logger.warning("没有有效的数据需要存储")
                    return 0
                    
    except Exception as e:
        logger.error(f"数据库存储失败: {str(e)}")
        return 0


def main():
    """主函数，用于直接运行测试"""
    import sys
    import os
    
    # 添加项目根目录到路径
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
    
    print('🚀 Amazon Catalog Search Items 测试工具')
    print('=' * 60)
    print('选择测试模式:')
    print('1. 数据库连接测试')
    print('2. 真实工具调用测试')
    print('3. 批量ASIN处理测试')
    print()
    
    choice = input('请输入选择 (1, 2 或 3): ').strip()
    
    if choice == '1':
        return test_database_connection()
    elif choice == '2':
        return test_real_tool_call()
    elif choice == '3':
        return test_batch_asin_processing()
    else:
        print('❌ 无效选择')
        return False


def test_database_connection():
    """测试数据库连接和ASIN获取"""
    try:
        print('🔍 开始数据库连接测试...')
        print('=' * 50)
        
        # 测试数据库连接
        asins = get_unique_asins_from_db()
        
        if asins:
            print('✅ 数据库连接成功！')
            print('-' * 40)
            print(f'📊 总ASIN数量: {len(asins):,}')
            print(f'🔍 前10个ASIN: {asins[:10]}')
            print(f'🔍 后10个ASIN: {asins[-10:]}')
            
            # 统计ASIN格式
            valid_asins = [asin for asin in asins if asin.startswith('B') and len(asin) == 10]
            print(f'✅ 有效ASIN数量: {len(valid_asins):,}')
            
            if len(valid_asins) != len(asins):
                invalid_asins = [asin for asin in asins if not (asin.startswith('B') and len(asin) == 10)]
                print(f'⚠️  无效ASIN数量: {len(invalid_asins):,}')
                print(f'⚠️  无效ASIN示例: {invalid_asins[:5]}')
            
            print()
            print('🎯 数据库状态: 正常')
            print('💡 可以继续进行工具调用测试')
            return True
            
        else:
            print('❌ 数据库连接失败或没有找到ASIN')
            print('-' * 40)
            print('💡 可能的原因:')
            print('  - POSTGRES_URI 环境变量未设置')
            print('  - 数据库连接失败')
            print('  - listing_details_table 表为空')
            print('  - 表结构不匹配')
            return False
            
    except Exception as e:
        print(f'❌ 数据库测试失败: {e}')
        return False


def test_real_tool_call():
    """测试真实工具调用"""
    try:
        print('🔍 开始真实工具调用测试...')
        print('=' * 50)
        
        # 测试参数
        test_params = {
            'company_id': 'TEST_COMPANY_001',
            'store_id': 'TEST_STORE_001',
            'marketplace': 'US'
        }
        
        print(f'📋 测试参数:')
        print(f'  - company_id: {test_params["company_id"]}')
        print(f'  - store_id: {test_params["store_id"]}')
        print(f'  - marketplace: {test_params["marketplace"]}')
        print()
        
        print('🚀 调用工具...')
        print('⚠️  注意：这将处理数据库中的所有ASIN，可能需要较长时间')
        print()
        
        confirm = input('确认继续？(y/N): ').strip().lower()
        if confirm != 'y':
            print('❌ 测试已取消')
            return False
        
        # 调用工具
        result = amazon_catalog_search_items.invoke(test_params)
        
        # 解析结果
        try:
            data = json.loads(result)
            
            if data['success']:
                print()
                print('🎉 成功获取商品目录信息！')
                print('-' * 40)
                
                # 基本信息
                print(f'📊 总ASIN数量: {data.get("total_asins", 0):,}')
                print(f'✅ 成功处理: {data.get("successful_asins", 0):,}')
                print(f'❌ 处理失败: {data.get("failed_asins", 0):,}')
                print(f'💾 数据库存储: {data.get("database_stored", 0):,}')
                print(f'🌍 目标市场: {data.get("marketplace", "N/A")}')
                print(f'📅 执行时间: {data.get("execution_time", "N/A")}')
                
                # 显示处理的项目示例
                items = data.get('items', [])
                if items:
                    print()
                    print('📋 处理结果示例 (前5个):')
                    print('-' * 40)
                    for i, item in enumerate(items[:5], 1):
                        asin = item.get('asin', 'N/A')
                        cat_code = item.get('category_code', 'N/A')
                        sub_cat_code = item.get('sub_category_code', 'N/A')
                        cat_title = item.get('category_title', 'N/A')
                        sub_cat_title = item.get('sub_category_title', 'N/A')
                        top_rank = item.get('top_category_rank', 'N/A')
                        sub_rank = item.get('sub_category_rank', 'N/A')
                        
                        print(f'{i}. ASIN: {asin}')
                        print(f'   大类代码: {cat_code} | 大类名称: {cat_title}')
                        print(f'   小类代码: {sub_cat_code} | 小类名称: {sub_cat_title}')
                        print(f'   大类排名: {top_rank} | 小类排名: {sub_rank}')
                        print()
                
                # 显示失败的ASIN
                failed_items = data.get('failed_items', [])
                if failed_items:
                    print(f'❌ 失败的ASIN (前10个): {failed_items[:10]}')
                
                print()
                print('✅ 工具调用测试完成！')
                return True
                
            else:
                print()
                print('❌ 工具调用失败')
                print('-' * 40)
                print(f'错误信息: {data.get("error", "Unknown error")}')
                print(f'错误详情: {data.get("details", {})}')
                return False
                
        except json.JSONDecodeError as e:
            print(f'❌ 结果解析失败: {e}')
            print(f'📄 原始返回: {result[:200]}...')
            return False
            
    except KeyboardInterrupt:
        print()
        print('⚠️  操作被用户中断')
        return False
    except Exception as e:
        print(f'❌ 工具调用测试失败: {e}')
        return False


def test_batch_asin_processing():
    """测试批量ASIN处理逻辑"""
    try:
        print('🔍 开始批量ASIN处理测试...')
        print('=' * 50)
        
        # 获取ASIN列表
        print('📋 从数据库获取ASIN...')
        asins = get_unique_asins_from_db()
        
        if not asins:
            print('❌ 没有找到ASIN，无法进行测试')
            return False
        
        print(f'✅ 获取到 {len(asins):,} 个ASIN')
        
        # 模拟分批处理
        batch_size = 20
        total_batches = (len(asins) + batch_size - 1) // batch_size
        
        print(f'📊 分批处理配置:')
        print(f'  - 批次大小: {batch_size}')
        print(f'  - 总批次数: {total_batches}')
        print(f'  - 最后批次大小: {len(asins) % batch_size if len(asins) % batch_size > 0 else batch_size}')
        print()
        
        # 显示批次划分
        print('📋 批次划分示例:')
        print('-' * 40)
        for i in range(min(5, total_batches)):
            start_idx = i * batch_size
            end_idx = min(start_idx + batch_size, len(asins))
            batch_asins = asins[start_idx:end_idx]
            print(f'批次 {i+1}: ASIN {start_idx+1}-{end_idx} ({len(batch_asins)} 个)')
            print(f'  示例: {batch_asins[:3]}...')
            print()
        
        if total_batches > 5:
            print(f'... 还有 {total_batches - 5} 个批次')
        
        print('🎯 分批处理逻辑测试完成！')
        print('💡 实际处理时，每个批次会调用Amazon API获取分类信息')
        print('💡 每批最多处理20个ASIN（Amazon API限制）')
        return True
        
    except Exception as e:
        print(f'❌ 批量处理测试失败: {e}')
        return False


if __name__ == "__main__":
    sys.exit(0 if main() else 1)
