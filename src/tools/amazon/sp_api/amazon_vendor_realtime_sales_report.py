#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Vendor Real-time Sales Report Tool

独立的供应商实时销售报告工具，从拉取报告到解析数据的完整流程。
"""

from __future__ import annotations

import json
import logging
import os
import psycopg
import sys
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Annotated, Any, Dict, List, Optional, Union

from langchain_core.tools import tool
from src.tools.decorators import log_io

# 导入common模块
from src.tools.amazon.sp_api.common.reports import ReportsClient, ReportRequest, iso_utc
from src.tools.amazon.sp_api.common.sp_config import AmazonSPConfig

logger = logging.getLogger(__name__)

# 市场映射表：前端marketplace -> Amazon marketplace_ids
MARKETPLACE_MAPPING = {
    # 北美
    "US": "ATVPDKIKX0DER",   # 美国
    "CA": "A2EUQ1WTGCTBG2",  # 加拿大
    "MX": "A1AM78C64UM0Y8",  # 墨西哥
    "BR": "A2Q3Y263D00KWC",  # 巴西
    
    # 欧洲
    "UK": "A1F83G8C2ARO7P",  # 英国
    "DE": "A1PA6795UKMFR9",  # 德国
    "FR": "A13V1IB3VIYZZH",  # 法国
    "IT": "APJ6JRA9NG5V4",   # 意大利
    "ES": "A1RKKUPIHCS9HS",  # 西班牙
    "NL": "A1805IZSGTT6HS",  # 荷兰
    "SE": "A2NODRKZP88ZB9",  # 瑞典
    "PL": "A1C3SOZRARQ6R3",  # 波兰
    "TR": "A33AVAJ2PDY3EV",  # 土耳其
    "EG": "ARBP9OOSHTCHU",   # 埃及

    # 亚太
    "JP": "A1VC38T7YXB528",  # 日本
    "AU": "A39IBJ37TRP1C6",  # 澳大利亚
    "SG": "A19VAU5U5O7RUS",  # 新加坡
    "IN": "A21TJRUUN4KGV",   # 印度

    # 中东
    "AE": "A2VIGQ35RCS4UG",  # 阿联酋
    "SA": "A17E79C6D8DWNP",  # 沙特阿拉伯
}


@tool(description="获取亚马逊供应商实时销售报告数据。自动拉取最新72小时数据，处理报告创建、状态检查和数据解析。返回结构化的实时销售数据，包含小时级销售统计、ASIN表现等关键指标。")
@log_io
def amazon_vendor_realtime_sales_report(
    company_id: Annotated[str, "前端传递的公司ID"],
    store_id: Annotated[str, "前端传递的店铺ID"],
    marketplace: Annotated[str, "前端传递的市场代码，如US、UK、DE等"] = "US",
) -> str:
    """
    获取亚马逊供应商实时销售报告数据
    
    Args:
        company_id: 前端传递的公司ID
        store_id: 前端传递的店铺ID
        marketplace: 前端传递的市场代码（如US、UK、DE等）
        
    Returns:
        JSON格式的实时销售报告数据
    """
    try:
        # 参数验证
        if not company_id:
            return json.dumps({
                "success": False,
                "error": "缺少必要参数: company_id",
                "error_type": "MISSING_COMPANY_ID"
            }, ensure_ascii=False, indent=2)
        
        if not store_id:
            return json.dumps({
                "success": False,
                "error": "缺少必要参数: store_id",
                "error_type": "MISSING_STORE_ID"
            }, ensure_ascii=False, indent=2)
        
        if not marketplace:
            return json.dumps({
                "success": False,
                "error": "缺少必要参数: marketplace",
                "error_type": "MISSING_MARKETPLACE"
            }, ensure_ascii=False, indent=2)
        
        # 映射 marketplace 到 marketplace_ids
        if marketplace not in MARKETPLACE_MAPPING:
            return json.dumps({
                "success": False,
                "error": f"不支持的市场: {marketplace}",
                "error_type": "UNSUPPORTED_MARKETPLACE",
                "supported_marketplaces": list(MARKETPLACE_MAPPING.keys())
            }, ensure_ascii=False, indent=2)
        
        marketplace_ids = [MARKETPLACE_MAPPING[marketplace]]
        
        # 初始化配置
        config = AmazonSPConfig()
        reports_client = ReportsClient(config)
        
        # 对于实时销售报告，使用最近72小时数据（Amazon建议的时间窗口）
        now = datetime.now(timezone.utc)
        start_time = now - timedelta(hours=72)
        data_start_time = start_time.isoformat().replace("+00:00", "Z")
        data_end_time = now.isoformat().replace("+00:00", "Z")
        
        logger.info(f"创建报告类型: GET_VENDOR_REAL_TIME_SALES_REPORT")
        logger.info(f"市场ID: {marketplace}")
        logger.info(f"时间范围: {data_start_time} 到 {data_end_time}")
        
        report_request = ReportRequest(
            report_type="GET_VENDOR_REAL_TIME_SALES_REPORT",
            marketplace_ids=marketplace_ids,
            data_start_time=data_start_time,
            data_end_time=data_end_time
        )
        
        # 创建报告
        try:
            report_id = reports_client.create(report_request)
            logger.info(f"报告创建成功，报告ID: {report_id}")
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"报告创建失败: {str(e)}",
                "details": {
                    "report_type": "GET_VENDOR_REAL_TIME_SALES_REPORT",
                    "marketplace_ids": marketplace_ids
                }
            }, ensure_ascii=False)
        
        # 等待报告完成
        logger.info(f"等待报告完成，报告ID: {report_id}")
        try:
            document_id = reports_client.wait_done(report_id, timeout_seconds=300)
            logger.info(f"报告处理完成，文档ID: {document_id}")
        except Exception as e:
            # 打印详细的错误信息
            logger.error(f"报告处理失败，详细错误信息:")
            logger.error(f"  错误类型: {type(e).__name__}")
            logger.error(f"  错误信息: {str(e)}")
            
            # 尝试获取报告状态详情
            try:
                resp = reports_client.api.get_report(report_id)
                detail = resp.payload
                logger.error(f"  报告状态详情: {json.dumps(detail, indent=2, ensure_ascii=False)}")
                logger.error(f"  原始响应: {resp}")
            except Exception as detail_e:
                logger.error(f"  无法获取报告详情: {detail_e}")
            
            return json.dumps({
                "success": False,
                "error": f"报告处理超时或失败: {str(e)}",
                "details": {
                    "report_id": report_id,
                    "timeout": 300,
                    "error_type": type(e).__name__,
                    "full_error": str(e)
                }
            }, ensure_ascii=False)
        
        # 下载报告
        logger.info(f"下载报告文档: {document_id}")
        try:
            download_url = reports_client.get_download_url(document_id)
            logger.info(f"获取下载URL成功: {download_url}")
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"获取下载URL失败: {str(e)}",
                "details": {
                    "document_id": document_id
                }
            }, ensure_ascii=False)
        
        # 下载文件
        try:
            # 清理之前的报告文件
            reports_dir = Path("reports")
            if reports_dir.exists():
                for old_file in reports_dir.glob("vendor_realtime_sales_report_*.json"):
                    try:
                        old_file.unlink()
                        logger.info(f"删除旧文件: {old_file}")
                    except Exception as e:
                        logger.warning(f"删除旧文件失败: {old_file}, 错误: {e}")
            
            # 确保reports目录存在
            reports_dir.mkdir(exist_ok=True)
            
            # 生成文件名使用时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = reports_dir / f"vendor_realtime_sales_report_{timestamp}.json"
            reports_client.download_to_file(download_url, output_path)
            logger.info(f"报告下载完成: {output_path}")
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"报告下载失败: {str(e)}",
                "details": {
                    "document_id": document_id,
                    "download_url": download_url
                }
            }, ensure_ascii=False)
        
        # 解析报告数据
        logger.info(f"解析报告数据: {output_path}")
        parsed_data = parse_vendor_realtime_sales_report(str(output_path))
        
        if not parsed_data["success"]:
            # 清理临时文件
            try:
                if output_path.exists():
                    output_path.unlink()
                    logger.info(f"报告解析失败，清理临时文件: {output_path}")
            except Exception as cleanup_e:
                logger.warning(f"清理临时文件失败: {output_path}, 错误: {cleanup_e}")
            
            return json.dumps({
                "success": False,
                "error": f"报告解析失败: {parsed_data['error']}",
                "details": {
                    "file_path": str(output_path)
                }
            }, ensure_ascii=False)
        
        # 存储数据到数据库
        try:
            stored_count = store_realtime_sales_report_to_database(
                parsed_data["data"]["all_data"], 
                company_id, 
                store_id, 
                marketplace
            )
            logger.info(f"成功存储 {stored_count} 条实时销售报告记录到数据库")
        except Exception as e:
            logger.error(f"数据库存储失败: {e}")
            stored_count = 0
        
        # 返回成功结果
        result = {
            "success": True,
            "data_source": "AMAZON_VENDOR_REALTIME_SALES_REPORT",
            "operation_id": f"realtime_sales_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "operation_time": datetime.now(timezone.utc).isoformat(),
            "database_stored": stored_count,
            "report_type": "GET_VENDOR_REAL_TIME_SALES_REPORT",
            "report_id": report_id,
            "document_id": document_id,
            "time_range": {
                "start": parsed_data["data"]["date_range"]["start"],
                "end": parsed_data["data"]["date_range"]["end"]
            },
            "marketplace_ids": marketplace_ids,
            "file_path": str(output_path),
            "file_size": output_path.stat().st_size if output_path.exists() else 0,
            "data": parsed_data["data"],
            "execution_time": datetime.now().isoformat()
        }
        
        # 清理临时文件
        try:
            if output_path.exists():
                output_path.unlink()
                logger.info(f"清理临时文件: {output_path}")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {output_path}, 错误: {e}")
        
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"供应商实时销售报告工具执行失败: {e}")
        
        # 清理临时文件（如果存在）
        try:
            if 'output_path' in locals() and output_path.exists():
                output_path.unlink()
                logger.info(f"异常情况下清理临时文件: {output_path}")
        except Exception as cleanup_e:
            logger.warning(f"异常情况下清理临时文件失败: {cleanup_e}")
        
        return json.dumps({
            "success": False,
            "error": str(e),
            "details": {
                "report_type": "GET_VENDOR_REAL_TIME_SALES_REPORT",
                "marketplace_ids": marketplace_ids
            }
        }, ensure_ascii=False)


def parse_vendor_realtime_sales_report(file_path: str) -> Dict[str, Any]:
    """
    解析供应商实时销售报告数据
    
    Args:
        file_path: 报告文件路径
        
    Returns:
        解析后的数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        # 提取报告规格和数据
        report_spec = report_data.get("reportSpecification", {})
        report_data_list = report_data.get("reportData", [])
        
        if not report_data_list:
            return {
                "success": False,
                "error": "报告数据为空"
            }
        
        # 统计总数据
        total_records = len(report_data_list)
        total_revenue = sum(item.get("orderedRevenue", 0) for item in report_data_list)
        total_units = sum(item.get("orderedUnits", 0) for item in report_data_list)
        
        # 按ASIN分组统计
        asin_stats = {}
        # 按小时分组统计
        hour_stats = {}
        
        for item in report_data_list:
            asin = item.get("asin", "")
            # 提取小时信息（如果有）
            start_time = item.get("startTime", "")
            hour = ""
            if start_time:
                try:
                    dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                    hour = dt.strftime("%Y-%m-%d %H:00")
                except:
                    hour = "未知时间"
            
            if asin:
                if asin not in asin_stats:
                    asin_stats[asin] = {"units": 0, "revenue": 0}
                asin_stats[asin]["units"] += item.get("orderedUnits", 0)
                asin_stats[asin]["revenue"] += item.get("orderedRevenue", 0)
            
            if hour:
                if hour not in hour_stats:
                    hour_stats[hour] = {"units": 0, "revenue": 0}
                hour_stats[hour]["units"] += item.get("orderedUnits", 0)
                hour_stats[hour]["revenue"] += item.get("orderedRevenue", 0)
        
        # 转换为列表格式
        asin_summary = [
            {
                "asin": asin,
                "total_units": stats["units"],
                "total_revenue": stats["revenue"],
                "avg_price": stats["revenue"] / stats["units"] if stats["units"] > 0 else 0
            }
            for asin, stats in asin_stats.items()
        ]
        
        # 按销售额排序
        asin_summary.sort(key=lambda x: x["total_revenue"], reverse=True)
        
        # 小时统计
        hour_summary = [
            {
                "hour": hour,
                "total_units": stats["units"],
                "total_revenue": stats["revenue"],
                "avg_price": stats["revenue"] / stats["units"] if stats["units"] > 0 else 0
            }
            for hour, stats in hour_stats.items()
        ]
        
        # 按时间排序
        hour_summary.sort(key=lambda x: x["hour"])
        
        # 构建返回数据
        parsed_data = {
            "report_type": "GET_VENDOR_REAL_TIME_SALES_REPORT",
            "date_range": {
                "start": report_spec.get("dataStartTime"),
                "end": report_spec.get("dataEndTime")
            },
            "marketplace_ids": report_spec.get("marketplaceIds", []),
            "summary": {
                "total_records": total_records,
                "total_revenue": total_revenue,
                "total_units": total_units,
                "unique_asins": len(asin_stats),
                "unique_hours": len(hour_stats),
                "avg_order_value": total_revenue / total_units if total_units > 0 else 0
            },
            "top_performers": asin_summary[:10],  # 前10个表现最好的ASIN
            "hourly_breakdown": hour_summary,  # 小时级统计
            "all_data": report_data_list
        }
        
        return {
            "success": True,
            "data": parsed_data
        }
        
    except Exception as e:
        logger.error(f"解析供应商实时销售报告失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def store_realtime_sales_report_to_database(sales_data: List[Dict], company_id: str, store_id: str, marketplace: str) -> int:
    """
    将实时销售报告数据存储到数据库
    
    Args:
        sales_data: 销售数据列表
        company_id: 公司ID
        store_id: 店铺ID
        marketplace: 市场代码
        
    Returns:
        存储的记录数
    """
    if not sales_data:
        return 0
    
    postgres_uri = os.getenv("POSTGRES_URI")
    if not postgres_uri:
        raise ValueError("POSTGRES_URI 环境变量未设置")
    
    print(f"📊 开始处理 {len(sales_data)} 条实时销售数据...")
    
    # 存储到数据库
    try:
        with psycopg.connect(postgres_uri) as conn:
            with conn.cursor() as cur:
                stored_count = 0
                batch_size = 50  # 每批插入50条记录
                batch_data = []
                
                # 按日期和ASIN分组聚合数据
                aggregated_data = {}
                
                print(f"🔍 开始分析数据聚合...")
                print(f"📊 原始数据条数: {len(sales_data)}")
                
                for item in sales_data:
                    asin = item.get("asin", "")
                    start_time = item.get("startTime", "")
                    
                    # 只检查asin是否为空
                    if not asin:
                        continue
                    
                    # 提取日期和小时
                    try:
                        dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                        date_key = dt.date()
                        hour = dt.hour
                    except:
                        continue
                    
                    # 构建聚合键：date-asin
                    key = f"{date_key}-{asin}"
                    
                    if key not in aggregated_data:
                        aggregated_data[key] = {
                            "date": date_key,
                            "asin": asin,
                            "hours": {}  # 不预初始化24小时，只存储有数据的小时
                        }
                    
                    # 如果该小时还没有数据，初始化
                    if hour not in aggregated_data[key]["hours"]:
                        aggregated_data[key]["hours"][hour] = {"units": 0, "revenue": 0}
                    
                    # 累加该小时的数据
                    if 0 <= hour < 24:
                        aggregated_data[key]["hours"][hour]["units"] += item.get("orderedUnits", 0)
                        aggregated_data[key]["hours"][hour]["revenue"] += item.get("orderedRevenue", 0)
                
                print(f"📊 聚合后记录数: {len(aggregated_data)} (原始记录数: {len(sales_data)})")
                
                for key, data in aggregated_data.items():
                    # 构建主键：company_id-store_id-date-asin
                    company_id_store_id_date_asin = f"{company_id}-{store_id}-{data['date']}-{data['asin']}"
                    
                    # 构建24小时的数据
                    values = [company_id_store_id_date_asin, company_id, store_id, marketplace, data['date'], data['asin']]
                    
                    # 添加24小时的数据
                    for hour in range(24):
                        hour_data = data['hours'].get(hour) # 使用 .get() 获取小时数据，如果不存在则为 None
                        if hour_data:
                            values.extend([hour_data['units'], hour_data['revenue']])
                        else:
                            values.extend([None, None]) # 未出现的小时填充 None
                    
                    # 添加data_load_time
                    values.append(datetime.now(timezone.utc))
                    
                    batch_data.append(values)
                    
                    # 批量插入
                    if len(batch_data) >= batch_size:
                        placeholders = ', '.join(['%s'] * len(values))
                        cur.executemany(f"""
                            INSERT INTO amazon_data.realtime_sales_report_table (
                                company_id_store_id_date_asin, company_id, store_id, marketplace, date, asin,
                                hour_1_ordered_units, hour_1_ordered_revenue,
                                hour_2_ordered_units, hour_2_ordered_revenue,
                                hour_3_ordered_units, hour_3_ordered_revenue,
                                hour_4_ordered_units, hour_4_ordered_revenue,
                                hour_5_ordered_units, hour_5_ordered_revenue,
                                hour_6_ordered_units, hour_6_ordered_revenue,
                                hour_7_ordered_units, hour_7_ordered_revenue,
                                hour_8_ordered_units, hour_8_ordered_revenue,
                                hour_9_ordered_units, hour_9_ordered_revenue,
                                hour_10_ordered_units, hour_10_ordered_revenue,
                                hour_11_ordered_units, hour_11_ordered_revenue,
                                hour_12_ordered_units, hour_12_ordered_revenue,
                                hour_13_ordered_units, hour_13_ordered_revenue,
                                hour_14_ordered_units, hour_14_ordered_revenue,
                                hour_15_ordered_units, hour_15_ordered_revenue,
                                hour_16_ordered_units, hour_16_ordered_revenue,
                                hour_17_ordered_units, hour_17_ordered_revenue,
                                hour_18_ordered_units, hour_18_ordered_revenue,
                                hour_19_ordered_units, hour_19_ordered_revenue,
                                hour_20_ordered_units, hour_20_ordered_revenue,
                                hour_21_ordered_units, hour_21_ordered_revenue,
                                hour_22_ordered_units, hour_22_ordered_revenue,
                                hour_23_ordered_units, hour_23_ordered_revenue,
                                hour_24_ordered_units, hour_24_ordered_revenue,
                                data_load_time
                            ) VALUES ({placeholders})
                            ON CONFLICT (company_id_store_id_date_asin) DO UPDATE SET
                                -- 增量更新：只更新有数据的小时，保留已有的历史数据
                                hour_1_ordered_units = COALESCE(EXCLUDED.hour_1_ordered_units, realtime_sales_report_table.hour_1_ordered_units),
                                hour_1_ordered_revenue = COALESCE(EXCLUDED.hour_1_ordered_revenue, realtime_sales_report_table.hour_1_ordered_revenue),
                                hour_2_ordered_units = COALESCE(EXCLUDED.hour_2_ordered_units, realtime_sales_report_table.hour_2_ordered_units),
                                hour_2_ordered_revenue = COALESCE(EXCLUDED.hour_2_ordered_revenue, realtime_sales_report_table.hour_2_ordered_revenue),
                                hour_3_ordered_units = COALESCE(EXCLUDED.hour_3_ordered_units, realtime_sales_report_table.hour_3_ordered_units),
                                hour_3_ordered_revenue = COALESCE(EXCLUDED.hour_3_ordered_revenue, realtime_sales_report_table.hour_3_ordered_revenue),
                                hour_4_ordered_units = COALESCE(EXCLUDED.hour_4_ordered_units, realtime_sales_report_table.hour_4_ordered_units),
                                hour_4_ordered_revenue = COALESCE(EXCLUDED.hour_4_ordered_revenue, realtime_sales_report_table.hour_4_ordered_revenue),
                                hour_5_ordered_units = COALESCE(EXCLUDED.hour_5_ordered_units, realtime_sales_report_table.hour_5_ordered_units),
                                hour_5_ordered_revenue = COALESCE(EXCLUDED.hour_5_ordered_revenue, realtime_sales_report_table.hour_5_ordered_revenue),
                                hour_6_ordered_units = COALESCE(EXCLUDED.hour_6_ordered_units, realtime_sales_report_table.hour_6_ordered_units),
                                hour_6_ordered_revenue = COALESCE(EXCLUDED.hour_6_ordered_revenue, realtime_sales_report_table.hour_6_ordered_revenue),
                                hour_7_ordered_units = COALESCE(EXCLUDED.hour_7_ordered_units, realtime_sales_report_table.hour_7_ordered_units),
                                hour_7_ordered_revenue = COALESCE(EXCLUDED.hour_7_ordered_revenue, realtime_sales_report_table.hour_7_ordered_revenue),
                                hour_8_ordered_units = COALESCE(EXCLUDED.hour_8_ordered_units, realtime_sales_report_table.hour_8_ordered_units),
                                hour_8_ordered_revenue = COALESCE(EXCLUDED.hour_8_ordered_revenue, realtime_sales_report_table.hour_8_ordered_revenue),
                                hour_9_ordered_units = COALESCE(EXCLUDED.hour_9_ordered_units, realtime_sales_report_table.hour_9_ordered_units),
                                hour_9_ordered_revenue = COALESCE(EXCLUDED.hour_9_ordered_revenue, realtime_sales_report_table.hour_9_ordered_revenue),
                                hour_10_ordered_units = COALESCE(EXCLUDED.hour_10_ordered_units, realtime_sales_report_table.hour_10_ordered_units),
                                hour_10_ordered_revenue = COALESCE(EXCLUDED.hour_10_ordered_revenue, realtime_sales_report_table.hour_10_ordered_revenue),
                                hour_11_ordered_units = COALESCE(EXCLUDED.hour_11_ordered_units, realtime_sales_report_table.hour_11_ordered_units),
                                hour_11_ordered_revenue = COALESCE(EXCLUDED.hour_11_ordered_revenue, realtime_sales_report_table.hour_11_ordered_revenue),
                                hour_12_ordered_units = COALESCE(EXCLUDED.hour_12_ordered_units, realtime_sales_report_table.hour_12_ordered_units),
                                hour_12_ordered_revenue = COALESCE(EXCLUDED.hour_12_ordered_revenue, realtime_sales_report_table.hour_12_ordered_revenue),
                                hour_13_ordered_units = COALESCE(EXCLUDED.hour_13_ordered_units, realtime_sales_report_table.hour_13_ordered_units),
                                hour_13_ordered_revenue = COALESCE(EXCLUDED.hour_13_ordered_revenue, realtime_sales_report_table.hour_13_ordered_revenue),
                                hour_14_ordered_units = COALESCE(EXCLUDED.hour_14_ordered_units, realtime_sales_report_table.hour_14_ordered_units),
                                hour_14_ordered_revenue = COALESCE(EXCLUDED.hour_14_ordered_revenue, realtime_sales_report_table.hour_14_ordered_revenue),
                                hour_15_ordered_units = COALESCE(EXCLUDED.hour_15_ordered_units, realtime_sales_report_table.hour_15_ordered_units),
                                hour_15_ordered_revenue = COALESCE(EXCLUDED.hour_15_ordered_revenue, realtime_sales_report_table.hour_15_ordered_revenue),
                                hour_16_ordered_units = COALESCE(EXCLUDED.hour_16_ordered_units, realtime_sales_report_table.hour_16_ordered_units),
                                hour_16_ordered_revenue = COALESCE(EXCLUDED.hour_16_ordered_revenue, realtime_sales_report_table.hour_16_ordered_revenue),
                                hour_17_ordered_units = COALESCE(EXCLUDED.hour_17_ordered_units, realtime_sales_report_table.hour_17_ordered_units),
                                hour_17_ordered_revenue = COALESCE(EXCLUDED.hour_17_ordered_revenue, realtime_sales_report_table.hour_17_ordered_revenue),
                                hour_18_ordered_units = COALESCE(EXCLUDED.hour_18_ordered_units, realtime_sales_report_table.hour_18_ordered_units),
                                hour_18_ordered_revenue = COALESCE(EXCLUDED.hour_18_ordered_revenue, realtime_sales_report_table.hour_18_ordered_revenue),
                                hour_19_ordered_units = COALESCE(EXCLUDED.hour_19_ordered_units, realtime_sales_report_table.hour_19_ordered_units),
                                hour_19_ordered_revenue = COALESCE(EXCLUDED.hour_19_ordered_revenue, realtime_sales_report_table.hour_19_ordered_revenue),
                                hour_20_ordered_units = COALESCE(EXCLUDED.hour_20_ordered_units, realtime_sales_report_table.hour_20_ordered_units),
                                hour_20_ordered_revenue = COALESCE(EXCLUDED.hour_20_ordered_revenue, realtime_sales_report_table.hour_20_ordered_revenue),
                                hour_21_ordered_units = COALESCE(EXCLUDED.hour_21_ordered_units, realtime_sales_report_table.hour_21_ordered_units),
                                hour_21_ordered_revenue = COALESCE(EXCLUDED.hour_21_ordered_revenue, realtime_sales_report_table.hour_21_ordered_revenue),
                                hour_22_ordered_units = COALESCE(EXCLUDED.hour_22_ordered_units, realtime_sales_report_table.hour_22_ordered_units),
                                hour_22_ordered_revenue = COALESCE(EXCLUDED.hour_22_ordered_revenue, realtime_sales_report_table.hour_22_ordered_revenue),
                                hour_23_ordered_units = COALESCE(EXCLUDED.hour_23_ordered_units, realtime_sales_report_table.hour_23_ordered_units),
                                hour_23_ordered_revenue = COALESCE(EXCLUDED.hour_23_ordered_revenue, realtime_sales_report_table.hour_23_ordered_revenue),
                                hour_24_ordered_units = COALESCE(EXCLUDED.hour_24_ordered_units, realtime_sales_report_table.hour_24_ordered_units),
                                hour_24_ordered_revenue = COALESCE(EXCLUDED.hour_24_ordered_revenue, realtime_sales_report_table.hour_24_ordered_revenue),
                                data_load_time = NOW()
                        """, batch_data)
                        
                        stored_count += len(batch_data)
                        batch_data = []
                        print(f"📊 已存储 {stored_count} 条记录...")
                
                # 插入剩余的数据
                if batch_data:
                    placeholders = ', '.join(['%s'] * len(batch_data[0]))
                    cur.executemany(f"""
                        INSERT INTO amazon_data.realtime_sales_report_table (
                            company_id_store_id_date_asin, company_id, store_id, marketplace, date, asin,
                            hour_1_ordered_units, hour_1_ordered_revenue,
                            hour_2_ordered_units, hour_2_ordered_revenue,
                            hour_3_ordered_units, hour_3_ordered_revenue,
                            hour_4_ordered_units, hour_4_ordered_revenue,
                            hour_5_ordered_units, hour_5_ordered_revenue,
                            hour_6_ordered_units, hour_6_ordered_revenue,
                            hour_7_ordered_units, hour_7_ordered_revenue,
                            hour_8_ordered_units, hour_8_ordered_revenue,
                            hour_9_ordered_units, hour_9_ordered_revenue,
                            hour_10_ordered_units, hour_10_ordered_revenue,
                            hour_11_ordered_units, hour_11_ordered_revenue,
                            hour_12_ordered_units, hour_12_ordered_revenue,
                            hour_13_ordered_units, hour_13_ordered_revenue,
                            hour_14_ordered_units, hour_14_ordered_revenue,
                            hour_15_ordered_units, hour_15_ordered_revenue,
                            hour_16_ordered_units, hour_16_ordered_revenue,
                            hour_17_ordered_units, hour_17_ordered_revenue,
                            hour_18_ordered_units, hour_18_ordered_revenue,
                            hour_19_ordered_units, hour_19_ordered_revenue,
                            hour_20_ordered_units, hour_20_ordered_revenue,
                            hour_21_ordered_units, hour_21_ordered_revenue,
                            hour_22_ordered_units, hour_22_ordered_revenue,
                            hour_23_ordered_units, hour_23_ordered_revenue,
                            hour_24_ordered_units, hour_24_ordered_revenue,
                            data_load_time
                        ) VALUES ({placeholders})
                        ON CONFLICT (company_id_store_id_date_asin) DO UPDATE SET
                            hour_1_ordered_units = COALESCE(EXCLUDED.hour_1_ordered_units, realtime_sales_report_table.hour_1_ordered_units),
                            hour_1_ordered_revenue = COALESCE(EXCLUDED.hour_1_ordered_revenue, realtime_sales_report_table.hour_1_ordered_revenue),
                            hour_2_ordered_units = COALESCE(EXCLUDED.hour_2_ordered_units, realtime_sales_report_table.hour_2_ordered_units),
                            hour_2_ordered_revenue = COALESCE(EXCLUDED.hour_2_ordered_revenue, realtime_sales_report_table.hour_2_ordered_revenue),
                            hour_3_ordered_units = COALESCE(EXCLUDED.hour_3_ordered_units, realtime_sales_report_table.hour_3_ordered_units),
                            hour_3_ordered_revenue = COALESCE(EXCLUDED.hour_3_ordered_revenue, realtime_sales_report_table.hour_3_ordered_revenue),
                            hour_4_ordered_units = COALESCE(EXCLUDED.hour_4_ordered_units, realtime_sales_report_table.hour_4_ordered_units),
                            hour_4_ordered_revenue = COALESCE(EXCLUDED.hour_4_ordered_revenue, realtime_sales_report_table.hour_4_ordered_revenue),
                            hour_5_ordered_units = COALESCE(EXCLUDED.hour_5_ordered_units, realtime_sales_report_table.hour_5_ordered_units),
                            hour_5_ordered_revenue = COALESCE(EXCLUDED.hour_5_ordered_revenue, realtime_sales_report_table.hour_5_ordered_revenue),
                            hour_6_ordered_units = COALESCE(EXCLUDED.hour_6_ordered_units, realtime_sales_report_table.hour_6_ordered_units),
                            hour_6_ordered_revenue = COALESCE(EXCLUDED.hour_6_ordered_revenue, realtime_sales_report_table.hour_6_ordered_revenue),
                            hour_7_ordered_units = COALESCE(EXCLUDED.hour_7_ordered_units, realtime_sales_report_table.hour_7_ordered_units),
                            hour_7_ordered_revenue = COALESCE(EXCLUDED.hour_7_ordered_revenue, realtime_sales_report_table.hour_7_ordered_revenue),
                            hour_8_ordered_units = COALESCE(EXCLUDED.hour_8_ordered_units, realtime_sales_report_table.hour_8_ordered_units),
                            hour_8_ordered_revenue = COALESCE(EXCLUDED.hour_8_ordered_revenue, realtime_sales_report_table.hour_8_ordered_revenue),
                            hour_9_ordered_units = COALESCE(EXCLUDED.hour_9_ordered_units, realtime_sales_report_table.hour_9_ordered_units),
                            hour_9_ordered_revenue = COALESCE(EXCLUDED.hour_9_ordered_revenue, realtime_sales_report_table.hour_9_ordered_revenue),
                            hour_10_ordered_units = COALESCE(EXCLUDED.hour_10_ordered_units, realtime_sales_report_table.hour_10_ordered_units),
                            hour_10_ordered_revenue = COALESCE(EXCLUDED.hour_10_ordered_revenue, realtime_sales_report_table.hour_10_ordered_revenue),
                            hour_11_ordered_units = COALESCE(EXCLUDED.hour_11_ordered_units, realtime_sales_report_table.hour_11_ordered_units),
                            hour_11_ordered_revenue = COALESCE(EXCLUDED.hour_11_ordered_revenue, realtime_sales_report_table.hour_11_ordered_revenue),
                            hour_12_ordered_units = COALESCE(EXCLUDED.hour_12_ordered_units, realtime_sales_report_table.hour_12_ordered_units),
                            hour_12_ordered_revenue = COALESCE(EXCLUDED.hour_12_ordered_revenue, realtime_sales_report_table.hour_12_ordered_revenue),
                            hour_13_ordered_units = COALESCE(EXCLUDED.hour_13_ordered_units, realtime_sales_report_table.hour_13_ordered_units),
                            hour_13_ordered_revenue = COALESCE(EXCLUDED.hour_13_ordered_revenue, realtime_sales_report_table.hour_13_ordered_revenue),
                            hour_14_ordered_units = COALESCE(EXCLUDED.hour_14_ordered_units, realtime_sales_report_table.hour_14_ordered_units),
                            hour_14_ordered_revenue = COALESCE(EXCLUDED.hour_14_ordered_revenue, realtime_sales_report_table.hour_14_ordered_revenue),
                            hour_15_ordered_units = COALESCE(EXCLUDED.hour_15_ordered_units, realtime_sales_report_table.hour_15_ordered_units),
                            hour_15_ordered_revenue = COALESCE(EXCLUDED.hour_15_ordered_revenue, realtime_sales_report_table.hour_15_ordered_revenue),
                            hour_16_ordered_units = COALESCE(EXCLUDED.hour_16_ordered_units, realtime_sales_report_table.hour_16_ordered_units),
                            hour_16_ordered_revenue = COALESCE(EXCLUDED.hour_16_ordered_revenue, realtime_sales_report_table.hour_16_ordered_revenue),
                            hour_17_ordered_units = COALESCE(EXCLUDED.hour_17_ordered_units, realtime_sales_report_table.hour_17_ordered_units),
                            hour_17_ordered_revenue = COALESCE(EXCLUDED.hour_17_ordered_revenue, realtime_sales_report_table.hour_17_ordered_revenue),
                            hour_18_ordered_units = COALESCE(EXCLUDED.hour_18_ordered_units, realtime_sales_report_table.hour_18_ordered_units),
                            hour_18_ordered_revenue = COALESCE(EXCLUDED.hour_18_ordered_revenue, realtime_sales_report_table.hour_18_ordered_revenue),
                            hour_19_ordered_units = COALESCE(EXCLUDED.hour_19_ordered_units, realtime_sales_report_table.hour_19_ordered_units),
                            hour_19_ordered_revenue = COALESCE(EXCLUDED.hour_19_ordered_revenue, realtime_sales_report_table.hour_19_ordered_revenue),
                            hour_20_ordered_units = COALESCE(EXCLUDED.hour_20_ordered_units, realtime_sales_report_table.hour_20_ordered_units),
                            hour_20_ordered_revenue = COALESCE(EXCLUDED.hour_20_ordered_revenue, realtime_sales_report_table.hour_20_ordered_revenue),
                            hour_21_ordered_units = COALESCE(EXCLUDED.hour_21_ordered_units, realtime_sales_report_table.hour_21_ordered_units),
                            hour_21_ordered_revenue = COALESCE(EXCLUDED.hour_21_ordered_revenue, realtime_sales_report_table.hour_21_ordered_revenue),
                            hour_22_ordered_units = COALESCE(EXCLUDED.hour_22_ordered_units, realtime_sales_report_table.hour_22_ordered_units),
                            hour_22_ordered_revenue = COALESCE(EXCLUDED.hour_22_ordered_revenue, realtime_sales_report_table.hour_22_ordered_revenue),
                            hour_23_ordered_units = COALESCE(EXCLUDED.hour_23_ordered_units, realtime_sales_report_table.hour_23_ordered_units),
                            hour_23_ordered_revenue = COALESCE(EXCLUDED.hour_23_ordered_revenue, realtime_sales_report_table.hour_23_ordered_revenue),
                            hour_24_ordered_units = COALESCE(EXCLUDED.hour_24_ordered_units, realtime_sales_report_table.hour_24_ordered_units),
                            hour_24_ordered_revenue = COALESCE(EXCLUDED.hour_24_ordered_revenue, realtime_sales_report_table.hour_24_ordered_revenue),
                            data_load_time = NOW()
                    """, batch_data)
                    
                    stored_count += len(batch_data)
                
                conn.commit()
                print(f"✅ 成功存储 {stored_count} 条实时销售记录到数据库")
                return stored_count
                
    except Exception as e:
        print(f"❌ 数据库存储失败: {e}")
        raise e


def main():
    """主函数，用于直接运行测试"""
    import sys
    import os
    
    # 添加项目根目录到路径
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
    
    print('🚀 Amazon Vendor Real-time Sales Report 测试工具')
    print('=' * 60)
    print('选择测试模式:')
    print('1. Mock数据插入测试')
    print('2. 真实API调用测试')
    print()
    
    choice = input('请输入选择 (1 或 2): ').strip()
    
    if choice == '1':
        return test_mock_database_insert()
    elif choice == '2':
        return test_real_api_call()
    else:
        print('❌ 无效选择')
        return False


def test_mock_database_insert():
    """测试Mock数据插入"""
    try:
        print('🧪 开始Mock数据插入测试...')
        
        # 模拟实时销售数据
        mock_sales_data = [
            {
                "asin": "B0B9LCR8V1",
                "startTime": "2025-01-01T10:00:00Z",
                "endTime": "2025-01-01T11:00:00Z",
                "orderedUnits": 5,
                "orderedRevenue": 125.50,
                "currency": "USD"
            },
            {
                "asin": "B0B9LCR8V2",
                "startTime": "2025-01-01T10:00:00Z",
                "endTime": "2025-01-01T11:00:00Z",
                "orderedUnits": 3,
                "orderedRevenue": 89.99,
                "currency": "USD"
            }
        ]
        
        stored_count = store_realtime_sales_report_to_database(
            mock_sales_data,
            "TEST_COMPANY_001",
            "TEST_STORE_001",
            "US"
        )
        
        print(f'✅ Mock数据插入成功，存储了 {stored_count} 条记录')
        return 0
        
    except Exception as e:
        print(f'❌ Mock数据插入失败: {e}')
        return False


def test_real_api_call():
    """测试真实API调用"""
    try:
        print('🔍 开始真实API调用测试...')
        result = amazon_vendor_realtime_sales_report.func(
            company_id="TEST_COMPANY_001",
            store_id="TEST_STORE_001",
            marketplace="US"
        )
        data = json.loads(result)
        
        if data['success']:
            print()
            print('🎉 成功获取供应商实时销售报告！')
            print('-' * 40)
            
            # 基本信息
            operation_id = data.get('operation_id', 'N/A')
            operation_time = data.get('operation_time', 'N/A')
            database_stored = data.get('database_stored', 0)
            
            print(f'📄 操作ID: {operation_id}')
            print(f'⏰ 操作时间: {operation_time[:19]}' if operation_time != 'N/A' else '⏰ 操作时间: N/A')
            print(f'💾 存储记录数: {database_stored}')
            
            # 报告信息
            report_id = data.get('report_id', 'N/A')
            document_id = data.get('document_id', 'N/A')
            print(f'📋 报告ID: {report_id}')
            print(f'📄 文档ID: {document_id}')
            
            # 时间范围
            time_range = data.get('time_range', {})
            if time_range:
                start_time = time_range.get('start', 'N/A')
                end_time = time_range.get('end', 'N/A')
                print(f'📅 开始时间: {start_time[:19]}' if start_time != 'N/A' else '📅 开始时间: N/A')
                print(f'📅 结束时间: {end_time[:19]}' if end_time != 'N/A' else '📅 结束时间: N/A')
            
            # 数据统计
            if 'data' in data and 'all_data' in data['data']:
                all_data = data['data']['all_data']
                print(f'📊 总记录数: {len(all_data)}')
            
            print()
            print('✅ 实时销售报告获取完成！')
            return 0
            
        else:
            print()
            print('❌ 获取实时销售报告失败')
            print('-' * 40)
            print(f'错误信息: {data["error"]}')
            print(f'错误类型: {data.get("error_type", "UNKNOWN")}')
            
            return 1
            
    except Exception as e:
        print(f'❌ 真实API调用测试失败: {e}')
        return 1


if __name__ == "__main__":
    main()
