#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小通用 Reports 工具：创建、轮询、下载
"""

from __future__ import annotations

import json
import logging
import time
from dataclasses import dataclass
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from sp_api.api import Reports
from sp_api.base import SellingApiException

from .sp_config import AmazonSPConfig

logger = logging.getLogger(__name__)


@dataclass
class ReportRequest:
    report_type: str
    data_start_time: str
    data_end_time: str
    marketplace_ids: List[str]
    report_options: Optional[Dict[str, str]] = None


class ReportsClient:
    def __init__(self, config: Optional[AmazonSPConfig] = None, 
                 timeout: Optional[int] = None, max_retries: Optional[int] = None) -> None:
        self.config = config or AmazonSPConfig()
        self.config.validate()
        self.credentials = self.config.build_credentials()
        
        # 使用简单默认值
        self.timeout = timeout or 60
        self.max_retries = max_retries or 3
        
        # 创建带有重试机制的会话
        self.session = self._create_session()
        
        # 初始化 SP API 客户端
        self.api = Reports(credentials=self.credentials)

    def _create_session(self) -> requests.Session:
        """创建带有重试机制的 requests 会话"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=self.max_retries,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST"],
            backoff_factor=1,
        )
        
        # 配置适配器
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session

    def create(self, req: ReportRequest) -> str:
        """创建报告请求"""
        try:
            logger.info(f"创建报告: {req.report_type}")
            resp = self.api.create_report(
                reportType=req.report_type,
                dataStartTime=req.data_start_time,
                dataEndTime=req.data_end_time,
                marketplaceIds=req.marketplace_ids,
                reportOptions=req.report_options or None,
            )
            report_id = resp.payload.get("reportId")
            if not report_id:
                raise RuntimeError("创建报告失败，未返回 reportId")
            
            logger.info(f"报告创建成功: {report_id}")
            return report_id
            
        except SellingApiException as e:
            logger.error(f"SP API 错误详情:")
            logger.error(f"  错误代码: {getattr(e, 'code', 'N/A')}")
            logger.error(f"  错误信息: {str(e)}")
            if hasattr(e, 'response') and e.response:
                logger.error(f"  HTTP状态: {getattr(e.response, 'status_code', 'N/A')}")
                logger.error(f"  响应内容: {getattr(e.response, 'text', 'N/A')}")
            raise RuntimeError(f"创建报告失败: {e}")
        except Exception as e:
            logger.error(f"创建报告时发生未知错误: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            raise

    def wait_done(self, report_id: str, timeout_seconds: Optional[int] = None, poll_seconds: Optional[int] = None) -> str:
        """等待报告完成"""
        # 使用简单默认值
        timeout_seconds = timeout_seconds or 900  # 15分钟
        poll_seconds = poll_seconds or 15  # 15秒轮询一次
        
        deadline = time.time() + timeout_seconds
        attempt = 0
        
        logger.info(f"开始等待报告完成: {report_id}")
        
        while time.time() < deadline:
            try:
                attempt += 1
                logger.debug(f"检查报告状态 (尝试 {attempt}): {report_id}")
                
                resp = self.api.get_report(report_id)
                detail = resp.payload
                status = detail.get("processingStatus")
                
                logger.info(f"报告 {report_id} 状态检查:")
                logger.info(f"  状态: {status}")
                logger.info(f"  完整响应: {detail}")
                logger.info(f"  原始响应对象: {resp}")
                logger.info(f"  响应类型: {type(resp)}")
                logger.info(f"  响应属性: {dir(resp)}")
                
                if status == "DONE":
                    doc_id = detail.get("reportDocumentId")
                    if not doc_id:
                        logger.error(f"报告完成但缺少 reportDocumentId，完整详情: {detail}")
                        raise RuntimeError("报告完成但缺少 reportDocumentId")
                    logger.info(f"报告完成: {report_id} -> {doc_id}")
                    return doc_id
                    
                if status in {"CANCELLED", "FATAL"}:
                    logger.warning(f"报告状态为 {status}，跳过下载")
                    logger.info(f"  状态: {status}")
                    logger.info(f"  完整响应: {detail}")
                    error_msg = detail.get("dataEndTime", "未知错误")
                    # 使用专门的异常类型，避免被重试逻辑捕获
                    raise ValueError(f"报告处理失败: {status} - {error_msg}")
                    
                # 如果状态是 IN_QUEUE 或 IN_PROGRESS，继续等待
                if status in {"IN_QUEUE", "IN_PROGRESS"}:
                    logger.debug(f"报告正在处理中，等待 {poll_seconds} 秒后重试")
                    time.sleep(poll_seconds)
                    continue
                    
                # 未知状态，记录警告但继续等待
                logger.warning(f"未知的报告状态: {status}")
                time.sleep(poll_seconds)
                
            except SellingApiException as e:
                logger.error(f"检查报告状态时 SP API 错误详情:")
                logger.error(f"  错误代码: {getattr(e, 'code', 'N/A')}")
                logger.error(f"  错误信息: {str(e)}")
                if hasattr(e, 'response') and e.response:
                    logger.error(f"  HTTP状态: {getattr(e.response, 'status_code', 'N/A')}")
                    logger.error(f"  响应内容: {getattr(e.response, 'text', 'N/A')}")
                if attempt >= 3:  # 连续失败3次后抛出异常
                    raise RuntimeError(f"检查报告状态失败: {e}")
                time.sleep(poll_seconds * 2)  # 失败时等待更长时间
                continue
            except ValueError as e:
                # FATAL/CANCELLED 状态异常，不需要重试
                logger.error(f"报告状态异常: {e}")
                raise
            except Exception as e:
                logger.error(f"检查报告状态时发生未知错误: {e}")
                logger.error(f"错误类型: {type(e).__name__}")
                import traceback
                logger.error(f"错误堆栈: {traceback.format_exc()}")
                if attempt >= 3:
                    raise
                time.sleep(poll_seconds * 2)
                continue
        
        raise TimeoutError(f"等待报告完成超时: {report_id}")

    def get_download_url(self, document_id: str) -> str:
        """获取报告下载URL"""
        try:
            logger.info(f"获取下载URL: {document_id}")
            resp = self.api.get_report_document(document_id)
            doc = resp.payload
            logger.info(f"文档详情响应: {doc}")
            
            url = doc.get("url")
            if not url:
                logger.error(f"未获得下载URL，完整响应: {doc}")
                raise RuntimeError("未获得下载URL")
            
            logger.info(f"成功获取下载URL: {document_id}")
            return url
            
        except SellingApiException as e:
            logger.error(f"获取下载URL时 SP API 错误详情:")
            logger.error(f"  错误代码: {getattr(e, 'code', 'N/A')}")
            logger.error(f"  错误信息: {str(e)}")
            if hasattr(e, 'response') and e.response:
                logger.error(f"  HTTP状态: {getattr(e.response, 'status_code', 'N/A')}")
                logger.error(f"  响应内容: {getattr(e.response, 'text', 'N/A')}")
            raise RuntimeError(f"获取下载URL失败: {e}")
        except Exception as e:
            logger.error(f"获取下载URL时发生未知错误: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            raise

    def download_to_file(self, url: str, out_path: Path) -> Path:
        """下载报告文件"""
        try:
            logger.info(f"开始下载报告: {url} -> {out_path}")
            
            # 使用配置的会话进行下载
            r = self.session.get(url, timeout=self.timeout)
            r.raise_for_status()
            content = r.content
            
            # 处理 gzip 压缩
            if len(content) >= 2 and content[0] == 0x1F and content[1] == 0x8B:
                import gzip
                content = gzip.decompress(content)
                logger.debug("检测到 gzip 压缩，已解压")
            
            # 确保输出目录存在
            out_path.parent.mkdir(parents=True, exist_ok=True)
            out_path.write_bytes(content)
            
            logger.info(f"报告下载完成: {out_path} ({len(content)} 字节)")
            return out_path
            
        except requests.exceptions.Timeout as e:
            logger.error(f"下载超时: {e}")
            raise RuntimeError(f"下载报告超时: {e}")
        except requests.exceptions.ConnectionError as e:
            logger.error(f"下载连接错误: {e}")
            raise RuntimeError(f"下载报告连接错误: {e}")
        except requests.exceptions.RequestException as e:
            logger.error(f"下载请求错误: {e}")
            raise RuntimeError(f"下载报告失败: {e}")
        except Exception as e:
            logger.error(f"下载时发生未知错误: {e}")
            raise


def iso_utc(dt: datetime) -> str:
    """将 datetime 转换为 ISO UTC 格式"""
    return dt.astimezone(timezone.utc).replace(tzinfo=timezone.utc).isoformat().replace("+00:00", "Z")


