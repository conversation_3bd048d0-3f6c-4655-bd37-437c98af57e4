#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用 Amazon SP API 配置与凭据构造

职责：
- 读取、校验环境变量
- 构造 python-amazon-sp-api 所需凭据字典
"""

from __future__ import annotations

import os
from typing import Dict

from dotenv import load_dotenv


class AmazonSPConfig:
    """Amazon SP API 配置管理"""

    def __init__(self) -> None:
        load_dotenv()
        self.client_id: str | None = os.getenv("SP_API_CLIENT_ID")
        self.client_secret: str | None = os.getenv("SP_API_CLIENT_SECRET")
        self.refresh_token: str | None = os.getenv("SP_API_REFRESH_TOKEN")

    def validate(self) -> None:
        missing = [
            name
            for name, value in {
                "SP_API_CLIENT_ID": self.client_id,
                "SP_API_CLIENT_SECRET": self.client_secret,
                "SP_API_REFRESH_TOKEN": self.refresh_token,
            }.items()
            if not value
        ]
        if missing:
            raise ValueError(f"缺少必需环境变量: {', '.join(missing)}")

    def build_credentials(self) -> Dict[str, str]:
        creds = {
            "refresh_token": self.refresh_token,
            "lwa_app_id": self.client_id,
            "lwa_client_secret": self.client_secret,
            "aws_access_key": os.getenv("AWS_ACCESS_KEY"),
            "aws_secret_key": os.getenv("AWS_SECRET_KEY"),
            "role_arn": os.getenv("ROLE_ARN"),
        }
        return {k: v for k, v in creds.items() if v}


__all__ = ["AmazonSPConfig"]


