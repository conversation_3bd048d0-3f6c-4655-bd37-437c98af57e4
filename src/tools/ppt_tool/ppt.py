from langchain_core.tools import tool, InjectedToolCallId
from langgraph.types import Command
from langchain_core.messages import ToolMessage
from typing import Annotated
from src.graph.state import Todo
from ..decorators import log_io
from .prompt import PPT_DESCRIPTION

@tool(description=PPT_DESCRIPTION)
@log_io
def generate_ppt(
    ppt: Annotated[str, "ppt content"], 
    tool_call_id: Annotated[str, InjectedToolCallId]
) -> Command:
    return Command(
        update={
            "ppt": ppt,
            "messages": [
                ToolMessage(f"generate ppt {ppt}", tool_call_id=tool_call_id)
            ],
        }
    )