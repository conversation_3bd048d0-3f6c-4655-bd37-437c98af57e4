"""
CredentialTable class for managing credential data.
"""
import json
from typing import Dict, List, Optional, Any
import psycopg
from ..base import BaseTable


class CredentialTable(BaseTable):
    """
    Class for interacting with the credential table.
    """

    def create(
        self,
        type: str,
        store_id: str,
        company_id: str,
        params: Dict[str, Any],
        status: str = "unauth",
        platform: str = None
    ) -> int:
        """
        Create a new credential.

        Args:
            type: credential类型 (rpa/vc api/ads)
            store_id: 店铺id
            company_id: 公司id
            params: 鉴权参数
            status: 鉴权状态 (auth/unauth)
            platform: 平台 (amazon/shopify/1688)

        Returns:
            ID of the created credential
        """
        params_json = json.dumps(params) if params else None

        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO agent.credential (type, store_id, company_id, params, status, platform)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    RETURNING id
                    """,
                    (type, store_id, company_id, params_json, status, platform)
                )
                credential_id = cur.fetchone()[0]
                conn.commit()

        return credential_id

    def get(self, credential_id: int) -> Optional[Dict[str, Any]]:
        """
        Get a credential by ID.

        Args:
            credential_id: ID of the credential to retrieve

        Returns:
            Credential data as a dictionary, or None if not found
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, type, store_id, company_id, params, status, platform, 
                           create_time, update_time
                    FROM agent.credential
                    WHERE id = %s
                    """,
                    (credential_id,)
                )
                result = cur.fetchone()

                if result:
                    return {
                        "id": result[0],
                        "type": result[1],
                        "store_id": result[2],
                        "company_id": result[3],
                        "params": result[4] if isinstance(result[4], dict) else (json.loads(result[4]) if result[4] else None),
                        "status": result[5],
                        "platform": result[6],
                        "create_time": result[7],
                        "update_time": result[8]
                    }
                return None

    def update(
        self,
        credential_id: int,
        type: Optional[str] = None,
        store_id: Optional[str] = None,
        company_id: Optional[str] = None,
        params: Optional[Dict[str, Any]] = None,
        status: Optional[str] = None,
        platform: Optional[str] = None
    ) -> bool:
        """
        Update an existing credential using optimized static SQL.

        Args:
            credential_id: ID of the credential to update
            type: New credential type (optional)
            store_id: New store ID (optional)
            company_id: New company ID (optional)
            params: New credential parameters (optional)
            status: New credential status (optional)
            platform: New platform (optional)

        Returns:
            True if the update was successful, False otherwise
        """
        if not credential_id:
            return False

        params_json = json.dumps(params) if params is not None else None

        with psycopg.connect(self.db_url) as conn:
            try:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        UPDATE agent.credential 
                        SET 
                            type = COALESCE(%s, type),
                            store_id = COALESCE(%s, store_id),
                            company_id = COALESCE(%s, company_id),
                            params = COALESCE(%s, params),
                            status = COALESCE(%s, status),
                            platform = COALESCE(%s, platform),
                            update_time = CURRENT_TIMESTAMP
                        WHERE id = %s
                        """,
                        (type, store_id, company_id, params_json, status, platform, credential_id)
                    )
                    conn.commit()
                    return cur.rowcount > 0
            except Exception:
                conn.rollback()
                return False

    def update_by_store_type_platform(
        self,
        store_id: str,
        type: str,
        platform: str,
        company_id: Optional[str] = None,
        params: Optional[Dict[str, Any]] = None,
        status: Optional[str] = None
    ) -> bool:
        """
        Update a credential by store_id, type, and platform lookup.

        Args:
            store_id: ID of the store
            type: credential type
            platform: platform name
            company_id: New company ID (optional)
            params: New credential parameters (optional)
            status: New credential status (optional)

        Returns:
            True if the update was successful, False otherwise
        """
        params_json = json.dumps(params) if params is not None else None

        with psycopg.connect(self.db_url) as conn:
            try:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        UPDATE agent.credential 
                        SET 
                            company_id = COALESCE(%s, company_id),
                            params = COALESCE(%s, params),
                            status = COALESCE(%s, status),
                            update_time = CURRENT_TIMESTAMP
                        WHERE store_id = %s AND type = %s AND platform = %s
                        """,
                        (company_id, params_json, status, store_id, type, platform)
                    )
                    conn.commit()
                    return cur.rowcount > 0
            except Exception:
                conn.rollback()
                return False

    def get_by_store(self, store_id: str) -> List[Dict[str, Any]]:
        """
        Get all credentials for a specific store.

        Args:
            store_id: ID of the store

        Returns:
            List of credential data dictionaries
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, type, store_id, company_id, params, status, platform, 
                           create_time, update_time
                    FROM agent.credential
                    WHERE store_id = %s
                    ORDER BY create_time DESC
                    """,
                    (store_id,)
                )
                results = cur.fetchall()

                return [
                    {
                        "id": result[0],
                        "type": result[1],
                        "store_id": result[2],
                        "company_id": result[3],
                        "params": result[4] if isinstance(result[4], dict) else (json.loads(result[4]) if result[4] else None),
                        "status": result[5],
                        "platform": result[6],
                        "create_time": result[7],
                        "update_time": result[8]
                    }
                    for result in results
                ]

    def get_by_company_id(self, company_id: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get all credentials for a specific company.

        Args:
            company_id: ID of the company
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List of credential data dictionaries
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, type, store_id, company_id, params, status, platform, 
                           create_time, update_time
                    FROM agent.credential
                    WHERE company_id = %s
                    ORDER BY create_time DESC
                    LIMIT %s OFFSET %s
                    """,
                    (company_id, limit, offset)
                )
                results = cur.fetchall()

                return [
                    {
                        "id": result[0],
                        "type": result[1],
                        "store_id": result[2],
                        "company_id": result[3],
                        "params": result[4] if isinstance(result[4], dict) else (json.loads(result[4]) if result[4] else None),
                        "status": result[5],
                        "platform": result[6],
                        "create_time": result[7],
                        "update_time": result[8]
                    }
                    for result in results
                ]

    def get_by_store_type_platform(self, store_id: str, type: str, platform: str) -> Optional[Dict[str, Any]]:
        """
        Get a credential by store_id, type, and platform.

        Args:
            store_id: ID of the store
            type: credential type
            platform: platform name

        Returns:
            Credential data as a dictionary, or None if not found
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, type, store_id, company_id, params, status, platform, 
                           create_time, update_time
                    FROM agent.credential
                    WHERE store_id = %s AND type = %s AND platform = %s
                    ORDER BY create_time DESC
                    LIMIT 1
                    """,
                    (store_id, type, platform)
                )
                result = cur.fetchone()

                if result:
                    return {
                        "id": result[0],
                        "type": result[1],
                        "store_id": result[2],
                        "company_id": result[3],
                        "params": result[4] if isinstance(result[4], dict) else (json.loads(result[4]) if result[4] else None),
                        "status": result[5],
                        "platform": result[6],
                        "create_time": result[7],
                        "update_time": result[8]
                    }
                return None

    def delete(self, credential_id: int) -> bool:
        """
        Delete a credential.

        Args:
            credential_id: ID of the credential to delete

        Returns:
            True if the deletion was successful, False otherwise
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    "DELETE FROM agent.credential WHERE id = %s",
                    (credential_id,)
                )
                conn.commit()

                return cur.rowcount > 0