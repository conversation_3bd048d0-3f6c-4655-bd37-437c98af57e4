"""
ToolTable class for managing tool data.
"""
import json
import uuid
from typing import Dict, List, Optional, Any
import psycopg
from ..base import BaseTable


class ToolTable(BaseTable):
    """
    Class for interacting with the tool table.
    """

    def create(
        self,
        template_id: str,
        params: Dict[str, Any] = None
    ) -> str:
        """
        Create a new tool instance.

        Args:
            template_id: 工具模版id (foreign key)
            params: 工具参数or工具配置

        Returns:
            UUID of the created tool
        """
        tool_id = str(uuid.uuid4())
        params_json = json.dumps(params) if params else None

        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO agent.tool (id, template_id, params)
                    VALUES (%s, %s, %s)
                    """,
                    (tool_id, template_id, params_json)
                )
                conn.commit()

        return tool_id

    def get(self, tool_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a tool by ID.

        Args:
            tool_id: ID of the tool to retrieve

        Returns:
            Tool data as a dictionary, or None if not found
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, template_id, params, create_time, update_time
                    FROM agent.tool
                    WHERE id = %s
                    """,
                    (tool_id,)
                )
                result = cur.fetchone()

                if result:
                    return {
                        "id": result[0],
                        "template_id": result[1],
                        "params": result[2] if isinstance(result[2], dict) else (json.loads(result[2]) if result[2] else None),
                        "create_time": result[3],
                        "update_time": result[4]
                    }
                return None

    def get_with_template(self, tool_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a tool by ID with its template information.

        Args:
            tool_id: ID of the tool to retrieve

        Returns:
            Tool data with template info as a dictionary, or None if not found
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT t.id, t.template_id, t.params, t.create_time, t.update_time,
                           tt.name, tt.description, tt.logo_url, tt.version, tt.param as template_param
                    FROM agent.tool t
                    JOIN agent.tool_template tt ON t.template_id = tt.id
                    WHERE t.id = %s
                    """,
                    (tool_id,)
                )
                result = cur.fetchone()

                if result:
                    return {
                        "id": result[0],
                        "template_id": result[1],
                        "params": result[2] if isinstance(result[2], dict) else (json.loads(result[2]) if result[2] else None),
                        "create_time": result[3],
                        "update_time": result[4],
                        "template": {
                            "name": result[5],
                            "description": result[6],
                            "logo_url": result[7],
                            "version": result[8],
                            "param": result[9] if isinstance(result[9], dict) else (json.loads(result[9]) if result[9] else None)
                        }
                    }
                return None

    def get_by_template(self, template_id: str) -> List[Dict[str, Any]]:
        """
        Get all tools for a specific template.

        Args:
            template_id: ID of the template

        Returns:
            List of tool data dictionaries
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, template_id, params, create_time, update_time
                    FROM agent.tool
                    WHERE template_id = %s
                    ORDER BY create_time DESC
                    """,
                    (template_id,)
                )
                results = cur.fetchall()

                return [
                    {
                        "id": result[0],
                        "template_id": result[1],
                        "params": result[2] if isinstance(result[2], dict) else (json.loads(result[2]) if result[2] else None),
                        "create_time": result[3],
                        "update_time": result[4]
                    }
                    for result in results
                ]

    def update(
        self,
        tool_id: str,
        template_id: Optional[str] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update an existing tool.

        Args:
            tool_id: ID of the tool to update
            template_id: New template ID (optional)
            params: New parameters (optional)

        Returns:
            True if the update was successful, False otherwise
        """
        if not tool_id:
            return False

        update_parts = []
        params_list = []

        if template_id is not None:
            update_parts.append("template_id = %s")
            params_list.append(template_id)

        if params is not None:
            update_parts.append("params = %s")
            params_json = json.dumps(params) if params else None
            params_list.append(params_json)

        update_parts.append("update_time = CURRENT_TIMESTAMP")
        params_list.append(tool_id)

        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                query = f"""
                UPDATE agent.tool
                SET {", ".join(update_parts)}
                WHERE id = %s
                """
                cur.execute(query, params_list)
                conn.commit()

                return cur.rowcount > 0

    def delete(self, tool_id: str) -> bool:
        """
        Delete a tool.

        Args:
            tool_id: ID of the tool to delete

        Returns:
            True if the deletion was successful, False otherwise
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    "DELETE FROM agent.tool WHERE id = %s",
                    (tool_id,)
                )
                conn.commit()

                return cur.rowcount > 0

    def list_all(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        List all tools with pagination.

        Args:
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List of tool data dictionaries
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, template_id, params, create_time, update_time
                    FROM agent.tool
                    ORDER BY create_time DESC
                    LIMIT %s OFFSET %s
                    """,
                    (limit, offset)
                )
                results = cur.fetchall()

                return [
                    {
                        "id": result[0],
                        "template_id": result[1],
                        "params": result[2] if isinstance(result[2], dict) else (json.loads(result[2]) if result[2] else None),
                        "create_time": result[3],
                        "update_time": result[4]
                    }
                    for result in results
                ]