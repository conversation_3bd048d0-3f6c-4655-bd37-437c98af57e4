"""
MessageTable class for managing message data.
"""
import json
import uuid
from typing import Dict, List, Optional, Any
import psycopg
from ..base import BaseTable


class MessageTable(BaseTable):
    """
    Class for interacting with the message table.
    """

    def create(
        self,
        message_id: str,
        conversation_id: str,
        content: str,
        role: str,
        parent_message_id: Optional[str] = None,
        tool_calls: Optional[List[Dict[str, Any]]] = None,
        file_paths: Optional[List[str]] = None
    ) -> uuid.UUID:
        """
        Create a new message.

        Args:
            conversation_id: ID of the conversation
            content: Content of the message
            role: Role of the message sender (e.g., 'user', 'assistant', 'system')
            parent_message_id: ID of the parent message (optional)
            tool_calls: List of tool calls (optional)

        Returns:
            UUID of the created message
        """
        tool_calls_json = json.dumps(tool_calls) if tool_calls else None

        # Connect directly to the database
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO agent.message (
                        id, conversation_id, content, parent_message_id, role, tool_calls, file_paths
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """,
                    (message_id, conversation_id, content, parent_message_id, role, tool_calls_json, file_paths)
                )
                conn.commit()

        return message_id

    def update(
        self,
        message_id: str,
        content: Optional[str] = None,
        tool_calls: Optional[List[Dict[str, Any]]] = None
    ) -> bool:
        """
        Update an existing message.

        Args:
            message_id: ID of the message to update
            content: New content for the message (optional)
            tool_calls: New tool calls for the message (optional)

        Returns:
            True if the update was successful, False otherwise
        """
        if content is None and tool_calls is None:
            # Nothing to update
            return False

        # Build the update query dynamically based on what needs to be updated
        update_parts = []
        params = []

        if content is not None:
            update_parts.append("content = %s")
            params.append(content)

        if tool_calls is not None:
            update_parts.append("tool_calls = %s")
            tool_calls_json = json.dumps(tool_calls) if tool_calls else None
            params.append(tool_calls_json)

        # Always update the update_time
        update_parts.append("update_time = CURRENT_TIMESTAMP")

        # Add the message_id as the last parameter
        params.append(message_id)

        # Connect to the database and execute the update
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                query = f"""
                UPDATE agent.message
                SET {", ".join(update_parts)}
                WHERE id = %s
                """
                cur.execute(query, params)
                conn.commit()

                # Return True if a row was affected, False otherwise
                return cur.rowcount > 0

    def get(self, message_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a message by ID.

        Args:
            message_id: ID of the message to retrieve

        Returns:
            Message data as a dictionary, or None if not found
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, conversation_id, content, create_time, update_time,
                           parent_message_id, role, tool_calls, file_paths
                    FROM agent.message
                    WHERE id = %s
                    """,
                    (message_id,)
                )
                result = cur.fetchone()

                if result:
                    return {
                        "id": result[0],
                        "conversation_id": result[1],
                        "content": result[2],
                        "create_time": result[3],
                        "update_time": result[4],
                        "parent_message_id": result[5],
                        "role": result[6],
                        "tool_calls": result[7] if isinstance(result[7], (dict, list)) else (json.loads(result[7]) if result[7] else None),
                        "file_paths": result[8] if isinstance(result[8], list) else (json.loads(result[8]) if result[8] else None)
                    }
                return None