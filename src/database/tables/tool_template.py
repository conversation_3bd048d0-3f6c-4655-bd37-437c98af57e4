"""
ToolTemplateTable class for managing tool template data.
"""
import json
import uuid
from typing import Dict, List, Optional, Any
import psycopg
from ..base import BaseTable


class ToolTemplateTable(BaseTable):
    """
    Class for interacting with the tool_template table.
    """

    def create(
        self,
        name: str,
        description: str = None,
        logo_url: str = None,
        is_public: bool = True,
        version: str = None,
        param: Dict[str, Any] = None
    ) -> str:
        """
        Create a new tool template.

        Args:
            name: 工具名称
            description: 工具描述
            logo_url: 工具图标URL
            is_public: 是否对所有租户可见，默认true
            version: 工具版本
            param: 工具参数

        Returns:
            UUID of the created tool template
        """
        tool_id = str(uuid.uuid4())
        param_json = json.dumps(param) if param else None

        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO agent.tool_template (id, name, description, logo_url, is_public, version, param)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """,
                    (tool_id, name, description, logo_url, is_public, version, param_json)
                )
                conn.commit()

        return tool_id

    def get(self, tool_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a tool template by ID.

        Args:
            tool_id: ID of the tool template to retrieve

        Returns:
            Tool template data as a dictionary, or None if not found
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, name, description, logo_url, is_public, version, param, 
                           create_time, update_time
                    FROM agent.tool_template
                    WHERE id = %s
                    """,
                    (tool_id,)
                )
                result = cur.fetchone()

                if result:
                    return {
                        "id": result[0],
                        "name": result[1],
                        "description": result[2],
                        "logo_url": result[3],
                        "is_public": result[4],
                        "version": result[5],
                        "param": result[6] if isinstance(result[6], dict) else (json.loads(result[6]) if result[6] else None),
                        "create_time": result[7],
                        "update_time": result[8]
                    }
                return None

    def get_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get a tool template by name.

        Args:
            name: Name of the tool template

        Returns:
            Tool template data as a dictionary, or None if not found
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, name, description, logo_url, is_public, version, param, 
                           create_time, update_time
                    FROM agent.tool_template
                    WHERE name = %s
                    """,
                    (name,)
                )
                result = cur.fetchone()

                if result:
                    return {
                        "id": result[0],
                        "name": result[1],
                        "description": result[2],
                        "logo_url": result[3],
                        "is_public": result[4],
                        "version": result[5],
                        "param": result[6] if isinstance(result[6], dict) else (json.loads(result[6]) if result[6] else None),
                        "create_time": result[7],
                        "update_time": result[8]
                    }
                return None

    def get_public_tools(self) -> List[Dict[str, Any]]:
        """
        Get all public tool templates.

        Returns:
            List of public tool template data dictionaries
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, name, description, logo_url, is_public, version, param, 
                           create_time, update_time
                    FROM agent.tool_template
                    WHERE is_public = TRUE
                    ORDER BY create_time DESC
                    """,
                )
                results = cur.fetchall()

                return [
                    {
                        "id": result[0],
                        "name": result[1],
                        "description": result[2],
                        "logo_url": result[3],
                        "is_public": result[4],
                        "version": result[5],
                        "param": result[6] if isinstance(result[6], dict) else (json.loads(result[6]) if result[6] else None),
                        "create_time": result[7],
                        "update_time": result[8]
                    }
                    for result in results
                ]

    def update(
        self,
        tool_id: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        logo_url: Optional[str] = None,
        is_public: Optional[bool] = None,
        version: Optional[str] = None,
        param: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update an existing tool template.

        Args:
            tool_id: ID of the tool template to update
            name: New tool name (optional)
            description: New tool description (optional)
            logo_url: New logo URL (optional)
            is_public: New public visibility (optional)
            version: New version (optional)
            param: New parameters (optional)

        Returns:
            True if the update was successful, False otherwise
        """
        if not tool_id:
            return False

        update_parts = []
        params_list = []

        if name is not None:
            update_parts.append("name = %s")
            params_list.append(name)

        if description is not None:
            update_parts.append("description = %s")
            params_list.append(description)

        if logo_url is not None:
            update_parts.append("logo_url = %s")
            params_list.append(logo_url)

        if is_public is not None:
            update_parts.append("is_public = %s")
            params_list.append(is_public)

        if version is not None:
            update_parts.append("version = %s")
            params_list.append(version)

        if param is not None:
            update_parts.append("param = %s")
            param_json = json.dumps(param) if param else None
            params_list.append(param_json)

        update_parts.append("update_time = CURRENT_TIMESTAMP")
        params_list.append(tool_id)

        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                query = f"""
                UPDATE agent.tool_template
                SET {", ".join(update_parts)}
                WHERE id = %s
                """
                cur.execute(query, params_list)
                conn.commit()

                return cur.rowcount > 0

    def delete(self, tool_id: str) -> bool:
        """
        Delete a tool template.

        Args:
            tool_id: ID of the tool template to delete

        Returns:
            True if the deletion was successful, False otherwise
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    "DELETE FROM agent.tool_template WHERE id = %s",
                    (tool_id,)
                )
                conn.commit()

                return cur.rowcount > 0

    def list_all(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        List all tool templates with pagination.

        Args:
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List of tool template data dictionaries
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, name, description, logo_url, is_public, version, param, 
                           create_time, update_time
                    FROM agent.tool_template
                    ORDER BY create_time DESC
                    LIMIT %s OFFSET %s
                    """,
                    (limit, offset)
                )
                results = cur.fetchall()

                return [
                    {
                        "id": result[0],
                        "name": result[1],
                        "description": result[2],
                        "logo_url": result[3],
                        "is_public": result[4],
                        "version": result[5],
                        "param": result[6] if isinstance(result[6], dict) else (json.loads(result[6]) if result[6] else None),
                        "create_time": result[7],
                        "update_time": result[8]
                    }
                    for result in results
                ]