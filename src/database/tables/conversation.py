"""
ConversationTable class for managing conversation data.
"""
import uuid
from typing import Dict, List, Optional, Any
import psycopg
from ..base import BaseTable


class ConversationTable(BaseTable):
    """
    Class for interacting with the conversation table.
    """

    def create(self, user_id: str, company_id: str, title: str = None, status: str = 'in_progress', conversation_id: str = None, store_ids: List[str] = None) -> str:
        """
        Create a new conversation.

        Args:
            user_id: ID of the user who created the conversation
            company_id: ID of the company
            title: Title of the conversation (optional)
            status: Status of the conversation (in_progress/completed)
            conversation_id: Specific ID for the conversation (optional, will generate if not provided)
            store_ids: List of store IDs associated with the conversation (optional)

        Returns:
            UUID of the created conversation
        """
        if not conversation_id:
            conversation_id = str(uuid.uuid4())

        # Connect directly to the database
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO agent.conversation (id, user_id, company_id, title, status, store_ids)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    """,
                    (conversation_id, user_id, company_id, title, status, store_ids or [])
                )
                conn.commit()

        return conversation_id

    def get(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a conversation by ID.

        Args:
            conversation_id: ID of the conversation to retrieve

        Returns:
            Conversation data as a dictionary, or None if not found
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, user_id, title, create_time, update_time, status, company_id, store_ids
                    FROM agent.conversation
                    WHERE id = %s
                    """,
                    (conversation_id,)
                )
                result = cur.fetchone()

                if result:
                    return {
                        "id": result[0],
                        "user_id": result[1],
                        "title": result[2],
                        "create_time": result[3],
                        "update_time": result[4],
                        "status": result[5],
                        "company_id": result[6],
                        "store_ids": result[7] or []
                    }
                return None

    def update(
        self,
        conversation_id: str,
        title: Optional[str] = None,
        user_id: Optional[str] = None,
        status: Optional[str] = None,
        company_id: Optional[str] = None,
        store_ids: Optional[List[str]] = None
    ) -> bool:
        """
        Update an existing conversation.

        Args:
            conversation_id: ID of the conversation to update
            title: New title for the conversation (optional)
            user_id: New user ID for the conversation (optional)
            status: New status for the conversation (optional)
            company_id: New company ID for the conversation (optional)
            store_ids: New store IDs for the conversation (optional)

        Returns:
            True if the update was successful, False otherwise
        """
        if not conversation_id:
            return False

        # Build the update query dynamically based on what needs to be updated
        update_parts = []
        params = []

        if title is not None:
            update_parts.append("title = %s")
            params.append(title)

        if user_id is not None:
            update_parts.append("user_id = %s")
            params.append(user_id)

        if status is not None:
            update_parts.append("status = %s")
            params.append(status)

        if company_id is not None:
            update_parts.append("company_id = %s")
            params.append(company_id)

        if store_ids is not None:
            update_parts.append("store_ids = %s")
            params.append(store_ids)

        # Always update the update_time
        update_parts.append("update_time = CURRENT_TIMESTAMP")

        # Add the conversation_id as the last parameter
        params.append(conversation_id)

        # Connect to the database and execute the update
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                query = f"""
                UPDATE agent.conversation
                SET {", ".join(update_parts)}
                WHERE id = %s
                """
                cur.execute(query, params)
                conn.commit()

                # Return True if a row was affected, False otherwise
                return cur.rowcount > 0

    def get_by_company(self, company_id: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get conversations for a specific company.

        Args:
            company_id: ID of the company
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List of conversation data dictionaries
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, user_id, title, create_time, update_time, status, company_id, store_ids
                    FROM agent.conversation
                    WHERE company_id = %s
                    ORDER BY create_time DESC
                    LIMIT %s OFFSET %s
                    """,
                    (company_id, limit, offset)
                )
                results = cur.fetchall()

                return [
                    {
                        "id": result[0],
                        "user_id": result[1],
                        "title": result[2],
                        "create_time": result[3],
                        "update_time": result[4],
                        "status": result[5],
                        "company_id": result[6],
                        "store_ids": result[7] or []
                    }
                    for result in results
                ]

    def get_by_user(self, user_id: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get conversations for a specific user.

        Args:
            user_id: ID of the user
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List of conversation data dictionaries
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, user_id, title, create_time, update_time, status, company_id, store_ids
                    FROM agent.conversation
                    WHERE user_id = %s
                    ORDER BY create_time DESC
                    LIMIT %s OFFSET %s
                    """,
                    (user_id, limit, offset)
                )
                results = cur.fetchall()

                return [
                    {
                        "id": result[0],
                        "user_id": result[1],
                        "title": result[2],
                        "create_time": result[3],
                        "update_time": result[4],
                        "status": result[5],
                        "company_id": result[6],
                        "store_ids": result[7] or []
                    }
                    for result in results
                ]

    def get_by_status(self, status: str, company_id: str = None, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get conversations by status, optionally filtered by company.

        Args:
            status: Status of conversations to retrieve
            company_id: Optional company filter
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List of conversation data dictionaries
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                if company_id:
                    cur.execute(
                        """
                        SELECT id, user_id, title, create_time, update_time, status, company_id, store_ids
                        FROM agent.conversation
                        WHERE status = %s AND company_id = %s
                        ORDER BY create_time DESC
                        LIMIT %s OFFSET %s
                        """,
                        (status, company_id, limit, offset)
                    )
                else:
                    cur.execute(
                        """
                        SELECT id, user_id, title, create_time, update_time, status, company_id, store_ids
                        FROM agent.conversation
                        WHERE status = %s
                        ORDER BY create_time DESC
                        LIMIT %s OFFSET %s
                        """,
                        (status, limit, offset)
                    )
                results = cur.fetchall()

                return [
                    {
                        "id": result[0],
                        "user_id": result[1],
                        "title": result[2],
                        "create_time": result[3],
                        "update_time": result[4],
                        "status": result[5],
                        "company_id": result[6],
                        "store_ids": result[7] or []
                    }
                    for result in results
                ]

    def get_by_company_and_user(self, company_id: str, user_id: Optional[str] = None, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get conversations filtered by company_id and optionally by user_id.

        Args:
            company_id: ID of the company (required)
            user_id: ID of the user (optional filter)
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List of conversation data dictionaries
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                if user_id:
                    cur.execute(
                        """
                        SELECT id, user_id, title, create_time, update_time, status, company_id, store_ids
                        FROM agent.conversation
                        WHERE company_id = %s AND user_id = %s
                        ORDER BY create_time DESC
                        LIMIT %s OFFSET %s
                        """,
                        (company_id, user_id, limit, offset)
                    )
                else:
                    cur.execute(
                        """
                        SELECT id, user_id, title, create_time, update_time, status, company_id, store_ids
                        FROM agent.conversation
                        WHERE company_id = %s
                        ORDER BY create_time DESC
                        LIMIT %s OFFSET %s
                        """,
                        (company_id, limit, offset)
                    )
                results = cur.fetchall()

                return [
                    {
                        "id": result[0],
                        "user_id": result[1],
                        "title": result[2],
                        "create_time": result[3],
                        "update_time": result[4],
                        "status": result[5],
                        "company_id": result[6],
                        "store_ids": result[7] or []
                    }
                    for result in results
                ]

    def delete(self, conversation_id: str, company_id: str = None) -> bool:
        """
        Delete a conversation by ID, optionally filtered by company_id for security.

        Args:
            conversation_id: ID of the conversation to delete
            company_id: Optional company filter for authorization

        Returns:
            True if the conversation was deleted, False otherwise
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                if company_id:
                    cur.execute(
                        """
                        DELETE FROM agent.conversation
                        WHERE id = %s AND company_id = %s
                        """,
                        (conversation_id, company_id)
                    )
                else:
                    cur.execute(
                        """
                        DELETE FROM agent.conversation
                        WHERE id = %s
                        """,
                        (conversation_id,)
                    )
                conn.commit()
                return cur.rowcount > 0