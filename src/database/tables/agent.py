"""
AgentTable class for managing agent data.
"""
import uuid
from typing import Dict, List, Optional, Any
import psycopg
from ..base import BaseTable


class AgentTable(BaseTable):
    """
    Class for interacting with the agent table.
    """

    def create(
        self,
        name: str,
        role: str,
        company_id: str,
        agent_type: str = None,
        prompt: str = None,
        tools: List[str] = None,
        docs: List[str] = None,
        agent_id: str = None
    ) -> str:
        """
        Create a new agent.

        Args:
            name: Name of the agent
            role: Role of the agent (e.g., 'assistant')
            company_id: ID of the company
            agent_type: Type of the agent (optional)
            prompt: Agent prompt (optional)
            tools: List of tool IDs the agent uses (optional)
            docs: List of document IDs for agent knowledge base (optional)
            agent_id: Specific ID for the agent (optional, will generate if not provided)

        Returns:
            UUID of the created agent
        """
        if not agent_id:
            agent_id = str(uuid.uuid4())

        # Convert lists to arrays for PostgreSQL
        tools_array = tools if tools else []
        docs_array = docs if docs else []

        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO agent.agents (id, name, role, company_id, type, prompt, tools, docs)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """,
                    (agent_id, name, role, company_id, agent_type, prompt, tools_array, docs_array)
                )
                conn.commit()

        return agent_id

    def get(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """
        Get an agent by ID.

        Args:
            agent_id: ID of the agent to retrieve

        Returns:
            Agent data as a dictionary, or None if not found
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, name, role, company_id, type, prompt, tools, docs, create_time, update_time
                    FROM agent.agents
                    WHERE id = %s
                    """,
                    (agent_id,)
                )
                result = cur.fetchone()

                if result:
                    return {
                        "agent_id": result[0],
                        "name": result[1],
                        "role": result[2],
                        "company_id": result[3],
                        "type": result[4],
                        "prompt": result[5],
                        "tools": result[6] if result[6] else [],
                        "docs": result[7] if result[7] else [],
                        "create_time": result[8],
                        "update_time": result[9]
                    }
                return None

    def update(
        self,
        agent_id: str,
        name: Optional[str] = None,
        role: Optional[str] = None,
        agent_type: Optional[str] = None,
        prompt: Optional[str] = None,
        tools: Optional[List[str]] = None,
        docs: Optional[List[str]] = None
    ) -> bool:
        """
        Update an existing agent.

        Args:
            agent_id: ID of the agent to update
            name: New name for the agent (optional)
            role: New role for the agent (optional)
            agent_type: New type for the agent (optional)
            prompt: New prompt for the agent (optional)
            tools: New list of tool IDs (optional)
            docs: New list of document IDs (optional)

        Returns:
            True if the update was successful, False otherwise
        """
        if not agent_id:
            return False

        update_parts = []
        params = []

        if name is not None:
            update_parts.append("name = %s")
            params.append(name)

        if role is not None:
            update_parts.append("role = %s")
            params.append(role)

        if agent_type is not None:
            update_parts.append("type = %s")
            params.append(agent_type)

        if prompt is not None:
            update_parts.append("prompt = %s")
            params.append(prompt)

        if tools is not None:
            update_parts.append("tools = %s")
            params.append(tools)

        if docs is not None:
            update_parts.append("docs = %s")
            params.append(docs)

        if not update_parts:
            return False

        update_parts.append("update_time = CURRENT_TIMESTAMP")
        params.append(agent_id)

        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                query = f"""
                UPDATE agent.agents
                SET {", ".join(update_parts)}
                WHERE id = %s
                """
                cur.execute(query, params)
                conn.commit()

                return cur.rowcount > 0

    def get_by_company(self, company_id: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get agents for a specific company.

        Args:
            company_id: ID of the company
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List of agent data dictionaries
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, name, role, company_id, type, prompt, tools, docs, create_time, update_time
                    FROM agent.agents
                    WHERE company_id = %s
                    ORDER BY create_time DESC
                    LIMIT %s OFFSET %s
                    """,
                    (company_id, limit, offset)
                )
                results = cur.fetchall()

                return [
                    {
                        "agent_id": result[0],
                        "name": result[1],
                        "role": result[2],
                        "company_id": result[3],
                        "type": result[4],
                        "prompt": result[5],
                        "tools": result[6] if result[6] else [],
                        "docs": result[7] if result[7] else [],
                        "create_time": result[8],
                        "update_time": result[9]
                    }
                    for result in results
                ]

    def get_by_type(self, agent_type: str, company_id: str = None, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get agents by type, optionally filtered by company.

        Args:
            agent_type: Type of agents to retrieve
            company_id: Optional company filter
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List of agent data dictionaries
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                if company_id:
                    cur.execute(
                        """
                        SELECT id, name, role, company_id, type, prompt, tools, docs, create_time, update_time
                        FROM agent.agents
                        WHERE type = %s AND company_id = %s
                        ORDER BY create_time DESC
                        LIMIT %s OFFSET %s
                        """,
                        (agent_type, company_id, limit, offset)
                    )
                else:
                    cur.execute(
                        """
                        SELECT id, name, role, company_id, type, prompt, tools, docs, create_time, update_time
                        FROM agent.agents
                        WHERE type = %s
                        ORDER BY create_time DESC
                        LIMIT %s OFFSET %s
                        """,
                        (agent_type, limit, offset)
                    )
                results = cur.fetchall()

                return [
                    {
                        "agent_id": result[0],
                        "name": result[1],
                        "role": result[2],
                        "company_id": result[3],
                        "type": result[4],
                        "prompt": result[5],
                        "tools": result[6] if result[6] else [],
                        "docs": result[7] if result[7] else [],
                        "create_time": result[8],
                        "update_time": result[9]
                    }
                    for result in results
                ]

    def delete(self, agent_id: str) -> bool:
        """
        Delete an agent by ID.

        Args:
            agent_id: ID of the agent to delete

        Returns:
            True if the deletion was successful, False otherwise
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    "DELETE FROM agent.agents WHERE id = %s",
                    (agent_id,)
                )
                conn.commit()

                return cur.rowcount > 0