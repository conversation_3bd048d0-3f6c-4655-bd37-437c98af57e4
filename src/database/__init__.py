"""
Database module for OnePort Agent.

This module provides database table classes and schema definitions.
"""
import logging
import traceback

from .schema import create_tables
from .tables.conversation import ConversationTable
from .tables.message import MessageTable  
from .tables.credential import CredentialTable
from .tables.tool_template import ToolTemplateTable
from .tables.tool import ToolTable
from src.config import get_config

logger = logging.getLogger(__name__)

def init_database(config):
    """初始化数据库"""
    try:
        config = get_config()
    except RuntimeError:
        logger.warning("配置未初始化，跳过数据库系统初始化")
        return
    
    # Check if POSTGRES_URI is set
    if config.is_database_enabled():
        try:
            # Mask the connection string for logging
            masked_uri = config.postgres_uri.split("@")
            if len(masked_uri) > 1:
                masked_uri = f"...@{masked_uri[1]}"
            else:
                masked_uri = "..."

            logger.info(f"Initializing database with URI: {masked_uri}")
            create_tables(config.postgres_uri)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            # Log the full error traceback for debugging
            logger.error(traceback.format_exc())
    else:
        logger.warning("数据库功能未启用，跳过数据库初始化")

__all__ = [
    'create_tables',
    'init_database',
    'ConversationTable',
    'MessageTable', 
    'CredentialTable',
    'ToolTemplateTable',
    'ToolTable'
]