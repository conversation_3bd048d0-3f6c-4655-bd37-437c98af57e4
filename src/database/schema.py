"""
Database schema definitions and table creation.
"""
import logging
import psycopg

# SQL statement for creating schema
CREATE_SCHEMA_AGENT = """
CREATE SCHEMA IF NOT EXISTS agent;
"""

# SQL statements for creating tables
CREATE_CONVERSATION_TABLE = """
CREATE TABLE IF NOT EXISTS agent.conversation (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT NOT NULL,
    title TEXT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed')),
    company_id TEXT NOT NULL,
    store_ids TEXT[] DEFAULT '{}'::TEXT[]
);
"""

CREATE_MESSAGE_TABLE = """
CREATE TABLE IF NOT EXISTS agent.message (
    id VARCHAR(64) PRIMARY KEY,
    conversation_id VARCHAR(64) NOT NULL,
    content TEXT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    parent_message_id VARCHAR(64),
    role VARCHAR(50) NOT NULL,
    tool_calls JSONB,
    file_paths TEXT NULL,
    FOREIGN KEY (conversation_id) REFERENCES agent.conversation(id) ON DELETE CASCADE
);
"""

CREATE_AGENT_TABLE = """
CREATE TABLE IF NOT EXISTS agent.agents (
    id UUID PRIMARY KEY,
    type TEXT NULL,
    prompt TEXT NULL,
    tools TEXT[] DEFAULT '{}'::TEXT[],
    docs TEXT[] DEFAULT '{}'::TEXT[],
    company_id TEXT NOT NULL,
    name TEXT NULL,
    role TEXT NULL,
    model TEXT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
"""

CREATE_CREDENTIAL_TABLE = """
CREATE TABLE IF NOT EXISTS agent.credential (
    id SERIAL PRIMARY KEY,
    type TEXT NULL,
    store_id TEXT NOT NULL,
    company_id TEXT NOT NULL,
    params JSONB NULL,
    status TEXT NULL,
    platform TEXT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(store_id, type, platform)
);
"""

CREATE_TOOL_TEMPLATE_TABLE = """
CREATE TABLE IF NOT EXISTS agent.tool_template (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT NULL,
    logo_url TEXT NULL,
    is_public BOOLEAN DEFAULT TRUE,
    version TEXT NULL,
    param JSONB NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
"""

CREATE_TOOL_TABLE = """
CREATE TABLE IF NOT EXISTS agent.tool (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_id TEXT NOT NULL,
    params JSONB NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES agent.tool_template(id) ON DELETE CASCADE
);
"""

# Amazon数据相关表
CREATE_SCHEMA_AMAZON_DATA = """
CREATE SCHEMA IF NOT EXISTS amazon_data;
"""

CREATE_AMAZON_SALES_REPORT_TABLE = """
CREATE TABLE IF NOT EXISTS amazon_data.sales_report_table (
    company_id_store_id_date_asin VARCHAR(200) PRIMARY KEY,
    company_id VARCHAR(50) NOT NULL,
    store_id VARCHAR(50) NOT NULL,
    marketplace VARCHAR(10) NOT NULL,
    date TIMESTAMPTZ NOT NULL,
    asin VARCHAR(20) NOT NULL,
    ordered_units INTEGER NOT NULL DEFAULT 0,
    ordered_revenue DECIMAL(15,4) NOT NULL DEFAULT 0.00,
    average_selling_price DECIMAL(15,4) NOT NULL DEFAULT 0.00,
    data_load_time TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
"""



CREATE_AMAZON_LISTING_DETAILS_TABLE = """
CREATE TABLE IF NOT EXISTS amazon_data.listing_details_table (
    company_id_store_id_vendor_code_asin VARCHAR(200) PRIMARY KEY,
    company_id VARCHAR(50) NOT NULL,
    store_id VARCHAR(50) NOT NULL,
    marketplace VARCHAR(10) NOT NULL,
    vendor_code VARCHAR(50) NOT NULL,
    vc_mode VARCHAR(50),
    asin VARCHAR(20),
    sku VARCHAR(100),
    external_product_id JSONB,
    status VARCHAR(50),
    item_name TEXT,
    style JSONB,
    color JSONB,
    size JSONB,
    brand JSONB,
    created_date TIMESTAMPTZ,
    last_updated_date TIMESTAMPTZ,
    main_image JSONB,
    included_components JSONB,
    product_category_id VARCHAR(20),
    product_subcategory_id VARCHAR(20),
    cost_price JSONB,
    list_price JSONB,
    item_weight JSONB,
    item_depth_width_height JSONB,
    item_package_weight JSONB,
    item_package_dimensions JSONB,
    bullet_point JSONB,
    generic_keyword JSONB,
    full_info JSONB,
    data_load_time TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
"""

CREATE_AMAZON_REALTIME_SALES_REPORT_TABLE = """
CREATE TABLE IF NOT EXISTS amazon_data.realtime_sales_report_table (
    company_id_store_id_date_asin VARCHAR(200) PRIMARY KEY,
    company_id VARCHAR(50) NOT NULL,
    store_id VARCHAR(50) NOT NULL,
    marketplace VARCHAR(10) NOT NULL,
    date DATE NOT NULL,
    asin VARCHAR(20) NOT NULL,
    hour_1_ordered_units INTEGER DEFAULT 0,
    hour_1_ordered_revenue INTEGER DEFAULT 0,
    hour_2_ordered_units INTEGER DEFAULT 0,
    hour_2_ordered_revenue INTEGER DEFAULT 0,
    hour_3_ordered_units INTEGER DEFAULT 0,
    hour_3_ordered_revenue INTEGER DEFAULT 0,
    hour_4_ordered_units INTEGER DEFAULT 0,
    hour_4_ordered_revenue INTEGER DEFAULT 0,
    hour_5_ordered_units INTEGER DEFAULT 0,
    hour_5_ordered_revenue INTEGER DEFAULT 0,
    hour_6_ordered_units INTEGER DEFAULT 0,
    hour_6_ordered_revenue INTEGER DEFAULT 0,
    hour_7_ordered_units INTEGER DEFAULT 0,
    hour_7_ordered_revenue INTEGER DEFAULT 0,
    hour_8_ordered_units INTEGER DEFAULT 0,
    hour_8_ordered_revenue INTEGER DEFAULT 0,
    hour_9_ordered_units INTEGER DEFAULT 0,
    hour_9_ordered_revenue INTEGER DEFAULT 0,
    hour_10_ordered_units INTEGER DEFAULT 0,
    hour_10_ordered_revenue INTEGER DEFAULT 0,
    hour_11_ordered_units INTEGER DEFAULT 0,
    hour_11_ordered_revenue INTEGER DEFAULT 0,
    hour_12_ordered_units INTEGER DEFAULT 0,
    hour_12_ordered_revenue INTEGER DEFAULT 0,
    hour_13_ordered_units INTEGER DEFAULT 0,
    hour_13_ordered_revenue INTEGER DEFAULT 0,
    hour_14_ordered_units INTEGER DEFAULT 0,
    hour_14_ordered_revenue INTEGER DEFAULT 0,
    hour_15_ordered_units INTEGER DEFAULT 0,
    hour_15_ordered_revenue INTEGER DEFAULT 0,
    hour_16_ordered_units INTEGER DEFAULT 0,
    hour_16_ordered_revenue INTEGER DEFAULT 0,
    hour_17_ordered_units INTEGER DEFAULT 0,
    hour_17_ordered_revenue INTEGER DEFAULT 0,
    hour_18_ordered_units INTEGER DEFAULT 0,
    hour_18_ordered_revenue INTEGER DEFAULT 0,
    hour_19_ordered_units INTEGER DEFAULT 0,
    hour_19_ordered_revenue INTEGER DEFAULT 0,
    hour_20_ordered_units INTEGER DEFAULT 0,
    hour_20_ordered_revenue INTEGER DEFAULT 0,
    hour_21_ordered_units INTEGER DEFAULT 0,
    hour_21_ordered_revenue INTEGER DEFAULT 0,
    hour_22_ordered_units INTEGER DEFAULT 0,
    hour_22_ordered_revenue INTEGER DEFAULT 0,
    hour_23_ordered_units INTEGER DEFAULT 0,
    hour_23_ordered_revenue INTEGER DEFAULT 0,
    hour_24_ordered_units INTEGER DEFAULT 0,
    hour_24_ordered_revenue INTEGER DEFAULT 0,
    data_load_time TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
"""

CREATE_AMAZON_REALTIME_TRAFFIC_TABLE = """
CREATE TABLE IF NOT EXISTS amazon_data.realtime_traffic_table (
    company_id_store_id_date_asin VARCHAR(200) PRIMARY KEY,
    company_id VARCHAR(50) NOT NULL,
    store_id VARCHAR(50) NOT NULL,
    marketplace VARCHAR(10) NOT NULL,
    date DATE NOT NULL,
    asin VARCHAR(20) NOT NULL,
    hour_1_glance_views_data JSONB,
    hour_2_glance_views_data JSONB,
    hour_3_glance_views_data JSONB,
    hour_4_glance_views_data JSONB,
    hour_5_glance_views_data JSONB,
    hour_6_glance_views_data JSONB,
    hour_7_glance_views_data JSONB,
    hour_8_glance_views_data JSONB,
    hour_9_glance_views_data JSONB,
    hour_10_glance_views_data JSONB,
    hour_11_glance_views_data JSONB,
    hour_12_glance_views_data JSONB,
    hour_13_glance_views_data JSONB,
    hour_14_glance_views_data JSONB,
    hour_15_glance_views_data JSONB,
    hour_16_glance_views_data JSONB,
    hour_17_glance_views_data JSONB,
    hour_18_glance_views_data JSONB,
    hour_19_glance_views_data JSONB,
    hour_20_glance_views_data JSONB,
    hour_21_glance_views_data JSONB,
    hour_22_glance_views_data JSONB,
    hour_23_glance_views_data JSONB,
    hour_24_glance_views_data JSONB,
    data_load_time TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
"""

CREATE_AMAZON_COUPON_TABLE = """
CREATE TABLE IF NOT EXISTS amazon_data.coupon_table (
    company_id_store_id_coupon_id_asin VARCHAR(200) PRIMARY KEY,
    company_id VARCHAR(50) NOT NULL,
    store_id VARCHAR(50) NOT NULL,
    marketplace VARCHAR(10) NOT NULL,
    asin VARCHAR(20) NOT NULL,
    campaign_id VARCHAR(100) NOT NULL,
    coupon_id VARCHAR(100) NOT NULL,
    coupon_name VARCHAR(255) NOT NULL,
    start_date_time TIMESTAMPTZ,
    end_date_time TIMESTAMPTZ,
    discount_amount DOUBLE PRECISION DEFAULT 0.00,
    coupon_clips INTEGER DEFAULT 0,
    coupon_redemptions INTEGER DEFAULT 0,
    total_discount DOUBLE PRECISION DEFAULT 0.00,
    budget_spent DOUBLE PRECISION DEFAULT 0.00,
    budget_remaining DOUBLE PRECISION DEFAULT 0.00,
    budget_percentage_used DOUBLE PRECISION DEFAULT 0.00,
    currency_code VARCHAR(10),
    budget DOUBLE PRECISION DEFAULT 0.00,
    data_load_time TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
"""

CREATE_AMAZON_PROMOTION_TABLE = """
CREATE TABLE IF NOT EXISTS amazon_data.promotion_table (
    company_id_store_id_promotion_id_funding_agreement_id_asin VARCHAR(200) PRIMARY KEY,
    company_id VARCHAR(50) NOT NULL,
    store_id VARCHAR(50) NOT NULL,
    marketplace VARCHAR(10) NOT NULL,
    funding_agreement_id VARCHAR(100) NOT NULL,
    promotion_id VARCHAR(100),
    promotion_name TEXT,
    promotion_type VARCHAR(50) NOT NULL,
    asin VARCHAR(20) NOT NULL,
    start_date_time DATE,
    end_date_time DATE,
    status VARCHAR(50),
    units_sold INTEGER DEFAULT 0,
    amount_spent_currency_code VARCHAR(10),
    glance_view INTEGER DEFAULT 0,
    data_load_time TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
"""

CREATE_AMAZON_CATALOG_RANK_TABLE = """
CREATE TABLE IF NOT EXISTS amazon_data.catalog_rank_table (
    company_id_store_id_asin VARCHAR(200) PRIMARY KEY,
    company_id VARCHAR(50) NOT NULL,
    store_id VARCHAR(50) NOT NULL,
    marketplace VARCHAR(20) NOT NULL,
    asin VARCHAR(20) NOT NULL,
    category_code VARCHAR(100),
    sub_category_code VARCHAR(100),
    category_title VARCHAR(200),
    sub_category_title VARCHAR(200),
    top_category_rank INTEGER,
    sub_category_rank INTEGER,
    data_load_time TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
"""

CREATE_AMAZON_ASIN_OPERATION_PRIORITY_TABLE = """
CREATE TABLE IF NOT EXISTS amazon_data.asin_operation_priority_table (
    company_id_store_id_asin_sku VARCHAR(200) PRIMARY KEY,
    company_id VARCHAR(50) NOT NULL,
    store_id VARCHAR(50) NOT NULL,
    marketplace VARCHAR(20) NOT NULL,
    asin VARCHAR(20) NOT NULL,
    sku VARCHAR(100),
    asin_type VARCHAR(50),
    priority VARCHAR(50),
    operation_manager VARCHAR(100),
    data_load_time TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
"""

CREATE_AMAZON_SKU_BASIC_INFO_TABLE = """
CREATE TABLE IF NOT EXISTS amazon_data.sku_basic_info_table (
    company_id_store_id_sku VARCHAR(200) PRIMARY KEY,
    company_id VARCHAR(50) NOT NULL,
    store_id VARCHAR(50) NOT NULL,
    sku VARCHAR(100) NOT NULL,
    product_name_chinese VARCHAR(500),
    product_nickname_chinese VARCHAR(500),
    product_manager VARCHAR(100),
    data_load_time TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
"""


def create_tables(db_url: str) -> None:
    """
    Create all necessary tables in the PostgreSQL database.

    Args:
        db_url: PostgreSQL connection string
    """
    logger = logging.getLogger(__name__)

    if not db_url:
        logger.error("Database URL is empty or None")
        return

    logger.info(f"Connecting to database with URL: {db_url[:10]}...")

    # Use a direct connection instead of a pool for initialization
    try:
        # Connect directly to the database
        conn = psycopg.connect(db_url)
        conn.autocommit = False

        try:
            # Test the connection
            with conn.cursor() as cur:
                cur.execute("SELECT 1")
                result = cur.fetchone()
                logger.info(f"Database connection test: {result}")

            # Create agent schema
            with conn.cursor() as cur:
                try:
                    logger.info("Creating agent schema...")
                    cur.execute(CREATE_SCHEMA_AGENT)
                    conn.commit()
                    logger.info("Created agent schema successfully")
                except Exception as e:
                    conn.rollback()
                    logger.error(f"Failed to create agent schema: {str(e)}")
                    raise

            # Create conversation table
            with conn.cursor() as cur:
                try:
                    logger.info("Creating conversation table...")
                    cur.execute(CREATE_CONVERSATION_TABLE)
                    # Add indexes for common queries
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_conversation_company_id ON agent.conversation (company_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_conversation_user_id ON agent.conversation (user_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_conversation_status ON agent.conversation (status)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_conversation_create_time ON agent.conversation (create_time)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_conversation_company_user ON agent.conversation (company_id, user_id)")
                    conn.commit()
                    logger.info("Created conversation table and indexes successfully")
                except Exception as e:
                    conn.rollback()
                    logger.error(f"Failed to create conversation table: {str(e)}")
                    raise

            # Create message table
            # with conn.cursor() as cur:
            #     try:
            #         logger.info("Creating message table...")
            #         cur.execute(CREATE_MESSAGE_TABLE)
            #         # Add indexes for common queries
            #         cur.execute("CREATE INDEX IF NOT EXISTS idx_message_conversation_id ON agent.message (conversation_id)")
            #         cur.execute("CREATE INDEX IF NOT EXISTS idx_message_create_time ON agent.message (create_time)")
            #         cur.execute("CREATE INDEX IF NOT EXISTS idx_message_role ON agent.message (role)")
            #         cur.execute("CREATE INDEX IF NOT EXISTS idx_message_parent_id ON agent.message (parent_message_id)")
            #         cur.execute("CREATE INDEX IF NOT EXISTS idx_message_conversation_time ON agent.message (conversation_id, create_time)")
            #         conn.commit()
            #         logger.info("Created message table and indexes successfully")
            #     except Exception as e:
            #         conn.rollback()
            #         logger.error(f"Failed to create message table: {str(e)}")
            #         raise

            # Create agents table
            with conn.cursor() as cur:
                try:
                    logger.info("Creating agents table...")
                    cur.execute(CREATE_AGENT_TABLE)
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_agents_company_id ON agent.agents (company_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_agents_type ON agent.agents (type)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_agents_name ON agent.agents (name)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_agents_role ON agent.agents (role)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_agents_model ON agent.agents (model)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_agents_company_type ON agent.agents (company_id, type)")
                    conn.commit()
                    logger.info("Created agents table and indexes successfully")
                except Exception as e:
                    conn.rollback()
                    logger.error(f"Failed to create agents table: {str(e)}")
                    raise

            # Create credential table
            with conn.cursor() as cur:
                try:
                    logger.info("Creating credential table...")
                    cur.execute(CREATE_CREDENTIAL_TABLE)
                    # Add indexes for common queries
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_credential_store_id ON agent.credential (store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_credential_company_id ON agent.credential (company_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_credential_type ON agent.credential (type)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_credential_platform ON agent.credential (platform)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_credential_status ON agent.credential (status)")
                    conn.commit()
                    logger.info("Created credential table and indexes successfully")
                except Exception as e:
                    conn.rollback()
                    logger.error(f"Failed to create credential table: {str(e)}")
                    raise

            # Create tool_template table
            with conn.cursor() as cur:
                try:
                    logger.info("Creating tool_template table...")
                    cur.execute(CREATE_TOOL_TEMPLATE_TABLE)
                    # Add indexes for common queries
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_tool_template_name ON agent.tool_template (name)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_tool_template_is_public ON agent.tool_template (is_public)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_tool_template_version ON agent.tool_template (version)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_tool_template_name_version ON agent.tool_template (name, version)")
                    conn.commit()
                    logger.info("Created tool_template table and indexes successfully")
                except Exception as e:
                    conn.rollback()
                    logger.error(f"Failed to create tool_template table: {str(e)}")
                    raise

            # Create tool table
            with conn.cursor() as cur:
                try:
                    logger.info("Creating tool table...")
                    cur.execute(CREATE_TOOL_TABLE)
                    # Add indexes for common queries
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_tool_template_id ON agent.tool (template_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_tool_create_time ON agent.tool (create_time)")
                    conn.commit()
                    logger.info("Created tool table and indexes successfully")
                except Exception as e:
                    conn.rollback()
                    logger.error(f"Failed to create tool table: {str(e)}")
                    raise

            # Create Amazon data schema and tables
            with conn.cursor() as cur:
                try:
                    logger.info("Creating Amazon data schema and tables...")
                    cur.execute(CREATE_SCHEMA_AMAZON_DATA)
                    cur.execute(CREATE_AMAZON_SALES_REPORT_TABLE)
                    cur.execute(CREATE_AMAZON_LISTING_DETAILS_TABLE)
                    cur.execute(CREATE_AMAZON_REALTIME_SALES_REPORT_TABLE)
                    cur.execute(CREATE_AMAZON_REALTIME_TRAFFIC_TABLE)
                    cur.execute(CREATE_AMAZON_COUPON_TABLE)
                    cur.execute(CREATE_AMAZON_PROMOTION_TABLE)
                    cur.execute(CREATE_AMAZON_CATALOG_RANK_TABLE)
                    cur.execute(CREATE_AMAZON_ASIN_OPERATION_PRIORITY_TABLE)
                    cur.execute(CREATE_AMAZON_SKU_BASIC_INFO_TABLE)
                    
                    # Add indexes for Amazon sales_report_table
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sales_report_company_id ON amazon_data.sales_report_table (company_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sales_report_store_id ON amazon_data.sales_report_table (store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sales_report_marketplace ON amazon_data.sales_report_table (marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sales_report_date ON amazon_data.sales_report_table (date)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sales_report_asin ON amazon_data.sales_report_table (asin)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sales_report_company_store ON amazon_data.sales_report_table (company_id, store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sales_report_company_marketplace ON amazon_data.sales_report_table (company_id, marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sales_report_date_marketplace ON amazon_data.sales_report_table (date, marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sales_report_data_load_time ON amazon_data.sales_report_table (data_load_time)")
                    
                    # Add indexes for Amazon listing_details_table
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_listing_details_company_id ON amazon_data.listing_details_table (company_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_listing_details_store_id ON amazon_data.listing_details_table (store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_listing_details_marketplace ON amazon_data.listing_details_table (marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_listing_details_vendor_code ON amazon_data.listing_details_table (vendor_code)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_listing_details_data_load_time ON amazon_data.listing_details_table (data_load_time)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_listing_details_asin ON amazon_data.listing_details_table (asin)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_listing_details_sku ON amazon_data.listing_details_table (sku)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_listing_details_company_store ON amazon_data.listing_details_table (company_id, store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_listing_details_company_marketplace ON amazon_data.listing_details_table (company_id, marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_listing_details_vendor_marketplace ON amazon_data.listing_details_table (vendor_code, marketplace)")
                    
                    # Add indexes for Amazon realtime_sales_report_table
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_sales_report_company_id ON amazon_data.realtime_sales_report_table (company_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_sales_report_store_id ON amazon_data.realtime_sales_report_table (store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_sales_report_marketplace ON amazon_data.realtime_sales_report_table (marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_sales_report_date ON amazon_data.realtime_sales_report_table (date)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_sales_report_asin ON amazon_data.realtime_sales_report_table (asin)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_sales_report_data_load_time ON amazon_data.realtime_sales_report_table (data_load_time)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_sales_report_company_store ON amazon_data.realtime_sales_report_table (company_id, store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_sales_report_company_marketplace ON amazon_data.realtime_sales_report_table (company_id, marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_sales_report_date_marketplace ON amazon_data.realtime_sales_report_table (date, marketplace)")
                    
                    # Add indexes for Amazon realtime_traffic_table
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_traffic_company_id ON amazon_data.realtime_traffic_table (company_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_traffic_store_id ON amazon_data.realtime_traffic_table (store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_traffic_marketplace ON amazon_data.realtime_traffic_table (marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_traffic_date ON amazon_data.realtime_traffic_table (date)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_traffic_asin ON amazon_data.realtime_traffic_table (asin)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_traffic_data_load_time ON amazon_data.realtime_traffic_table (data_load_time)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_traffic_company_store ON amazon_data.realtime_traffic_table (company_id, store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_traffic_company_marketplace ON amazon_data.realtime_traffic_table (company_id, marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_realtime_traffic_date_marketplace ON amazon_data.realtime_traffic_table (date, marketplace)")
                    
                    # Add indexes for Amazon coupon_table
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_coupon_company_id ON amazon_data.coupon_table (company_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_coupon_store_id ON amazon_data.coupon_table (store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_coupon_marketplace ON amazon_data.coupon_table (marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_coupon_asin ON amazon_data.coupon_table (asin)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_coupon_coupon_name ON amazon_data.coupon_table (coupon_name)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_coupon_data_load_time ON amazon_data.coupon_table (data_load_time)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_coupon_company_store ON amazon_data.coupon_table (company_id, store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_coupon_company_marketplace ON amazon_data.coupon_table (company_id, marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_coupon_start_date_time ON amazon_data.coupon_table (start_date_time)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_coupon_end_date_time ON amazon_data.coupon_table (end_date_time)")
                    
                    # Add indexes for Amazon promotion_table
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_promotion_company_id ON amazon_data.promotion_table (company_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_promotion_store_id ON amazon_data.promotion_table (store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_promotion_marketplace ON amazon_data.promotion_table (marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_promotion_funding_agreement_id ON amazon_data.promotion_table (funding_agreement_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_promotion_promotion_id ON amazon_data.promotion_table (promotion_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_promotion_promotion_type ON amazon_data.promotion_table (promotion_type)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_promotion_asin ON amazon_data.promotion_table (asin)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_promotion_start_date_time ON amazon_data.promotion_table (start_date_time)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_promotion_end_date_time ON amazon_data.promotion_table (end_date_time)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_promotion_status ON amazon_data.promotion_table (status)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_promotion_data_load_time ON amazon_data.promotion_table (data_load_time)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_promotion_company_store ON amazon_data.promotion_table (company_id, store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_promotion_company_marketplace ON amazon_data.promotion_table (company_id, marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_promotion_funding_agreement_asin ON amazon_data.promotion_table (funding_agreement_id, asin)")
                    
                    # Add indexes for Amazon catalog_rank_table
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_catalog_rank_company_id ON amazon_data.catalog_rank_table (company_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_catalog_rank_store_id ON amazon_data.catalog_rank_table (store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_catalog_rank_marketplace ON amazon_data.catalog_rank_table (marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_catalog_rank_asin ON amazon_data.catalog_rank_table (asin)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_catalog_rank_category_code ON amazon_data.catalog_rank_table (category_code)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_catalog_rank_sub_category_code ON amazon_data.catalog_rank_table (sub_category_code)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_catalog_rank_category_title ON amazon_data.catalog_rank_table (category_title)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_catalog_rank_sub_category_title ON amazon_data.catalog_rank_table (sub_category_title)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_catalog_rank_top_category_rank ON amazon_data.catalog_rank_table (top_category_rank)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_catalog_rank_sub_category_rank ON amazon_data.catalog_rank_table (sub_category_rank)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_catalog_rank_data_load_time ON amazon_data.catalog_rank_table (data_load_time)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_catalog_rank_company_store ON amazon_data.catalog_rank_table (company_id, store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_catalog_rank_company_marketplace ON amazon_data.catalog_rank_table (company_id, marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_catalog_rank_asin_marketplace ON amazon_data.catalog_rank_table (asin, marketplace)")
                    
                    # Add indexes for Amazon asin_operation_priority_table
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_asin_operation_priority_company_id ON amazon_data.asin_operation_priority_table (company_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_asin_operation_priority_store_id ON amazon_data.asin_operation_priority_table (store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_asin_operation_priority_marketplace ON amazon_data.asin_operation_priority_table (marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_asin_operation_priority_asin ON amazon_data.asin_operation_priority_table (asin)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_asin_operation_priority_sku ON amazon_data.asin_operation_priority_table (sku)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_asin_operation_priority_asin_type ON amazon_data.asin_operation_priority_table (asin_type)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_asin_operation_priority_priority ON amazon_data.asin_operation_priority_table (priority)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_asin_operation_priority_operation_manager ON amazon_data.asin_operation_priority_table (operation_manager)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_asin_operation_priority_data_load_time ON amazon_data.asin_operation_priority_table (data_load_time)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_asin_operation_priority_company_store ON amazon_data.asin_operation_priority_table (company_id, store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_asin_operation_priority_company_marketplace ON amazon_data.asin_operation_priority_table (company_id, marketplace)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_asin_operation_priority_asin_marketplace ON amazon_data.asin_operation_priority_table (asin, marketplace)")
                    
                    # Add indexes for Amazon sku_basic_info_table
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sku_basic_info_company_id ON amazon_data.sku_basic_info_table (company_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sku_basic_info_store_id ON amazon_data.sku_basic_info_table (store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sku_basic_info_sku ON amazon_data.sku_basic_info_table (sku)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sku_basic_info_product_name_chinese ON amazon_data.sku_basic_info_table (product_name_chinese)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sku_basic_info_product_nickname_chinese ON amazon_data.sku_basic_info_table (product_nickname_chinese)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sku_basic_info_product_manager ON amazon_data.sku_basic_info_table (product_manager)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sku_basic_info_data_load_time ON amazon_data.sku_basic_info_table (data_load_time)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sku_basic_info_company_store ON amazon_data.sku_basic_info_table (company_id, store_id)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sku_basic_info_company_sku ON amazon_data.sku_basic_info_table (company_id, sku)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_sku_basic_info_store_sku ON amazon_data.sku_basic_info_table (store_id, sku)")
                    
                    conn.commit()
                    logger.info("Created Amazon data tables and indexes successfully")
                except Exception as e:
                    conn.rollback()
                    logger.error(f"Failed to create Amazon data tables: {str(e)}")
                    raise


        finally:
            # Close the connection
            conn.close()
            logger.info("Database connection closed")
    except Exception as e:
        logger.error(f"Database connection or table creation failed: {str(e)}")
        raise