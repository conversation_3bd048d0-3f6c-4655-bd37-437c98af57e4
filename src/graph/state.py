from typing import Literal, NotRequired, Annotated
from langgraph.prebuilt.chat_agent_executor import AgentState
from pydantic import BaseModel, Field

class Todo(BaseModel):
    """Todo item to track task progress."""
    
    content: str = Field(..., min_length=1, description="The task description or content")
    status: Literal["pending", "in_progress", "completed"] = Field(
        "pending", 
        description="Current status of the task"
    )
    priority: Literal["high", "medium", "low"] = Field(
        "medium", 
        description="Priority level of the task"
    )
    id: str = Field(..., min_length=1, description="Unique identifier for the task")

def file_reducer(l, r):
    if l is None:
        return r
    elif r is None:
        return l
    else:
        return {**l, **r}
    
class DeepAgentState(AgentState):
    todos: NotRequired[list[Todo]]
    files: Annotated[NotRequired[dict[str, str]], file_reducer]
    company_id: str
    user_id: str
    store_id: NotRequired[list[str]]
    report: NotRequired[str]
    ppt: NotRequired[str]