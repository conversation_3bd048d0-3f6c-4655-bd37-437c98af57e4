#!/usr/bin/env python3
"""
服务启动脚本
在服务启动时初始化配置并验证必需的环境变量
"""

import sys
import logging
from src.config import init_config, get_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def startup():
    """服务启动函数"""
    logger.info("开始启动 OnePort Agent 服务...")
    
    try:
        # 初始化配置
        logger.info("正在加载配置...")
        config = init_config()
        
        # 显示配置状态
        logger.info("配置加载完成，功能状态:")
        logger.info(f"  音频功能: {'启用' if config.is_audio_enabled() else '禁用'}")
        logger.info(f"  数据库功能: {'启用' if config.is_database_enabled() else '禁用'}")
        logger.info(f"  Redis功能: {'启用' if config.is_redis_enabled() else '禁用'}")
        logger.info(f"  S3存储功能: {'启用' if config.is_s3_enabled() else '禁用'}")
        logger.info(f"  LLM功能: {'启用' if config.is_llm_enabled() else '禁用'}")
        logger.info(f"  搜索引擎: {config.search_api}")
        
        # 初始化LLM模型
        logger.info("正在初始化LLM模型...")
        from src.llms import init_llm
        init_llm(config)
        logger.info("LLM模型初始化完成")
        
        # 初始化数据库
        logger.info("正在初始化数据库...")
        from src.database import init_database
        init_database(config)
        logger.info("数据库初始化完成")
        
        # 初始化存储系统
        logger.info("正在初始化存储系统...")
        from src.store import init_storage
        init_storage()
        logger.info("存储系统初始化完成")
        
        logger.info("OnePort Agent 服务启动成功!")
        return True
        
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        return False


def main():
    """主函数"""
    if not startup():
        logger.error("服务启动失败，退出程序")
        sys.exit(1)
    
    # 这里可以添加服务运行逻辑
    logger.info("服务正在运行...")


if __name__ == "__main__":
    main()