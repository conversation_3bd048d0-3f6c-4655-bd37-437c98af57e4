# 🚀 OnePort Agent

<div align="center">
  <img src="https://img.shields.io/badge/Python-3.13-blue.svg" alt="Python 3.13">
  <img src="https://img.shields.io/badge/FastAPI-0.115+-green.svg" alt="FastAPI">
  <img src="https://img.shields.io/badge/LangChain-0.3.25+-orange.svg" alt="LangChain">
  <img src="https://img.shields.io/badge/LangGraph-0.4.3+-purple.svg" alt="LangGraph">
  <img src="https://img.shields.io/badge/PostgreSQL-15.0+-blue.svg" alt="PostgreSQL">
  <img src="https://img.shields.io/badge/UV-0.1.0+-brightgreen.svg" alt="UV">
</div>

<p align="center">
  <b>一个强大的Agent助手框架，基于LangChain和LangGraph构建，提供智能对话、网络搜索和内容处理能力</b>
</p>

<p align="center">
  <a href="#核心功能">核心功能</a> •
  <a href="#快速开始">快速开始</a> •
  <a href="#api接口">API接口</a> •
  <a href="#数据库配置">数据库配置</a> •
  <a href="#项目结构">项目结构</a> •
  <a href="#贡献指南">贡献指南</a>
</p>

---

## 📖 项目概述

OnePort Agent 是一个集成了多种AI能力的助手框架，使用FastAPI提供Web服务接口，并由uv管理依赖。它可以帮助用户进行信息检索、内容生成、代码执行等任务，是构建智能应用的理想基础框架。

本项目采用模块化设计，支持多种大型语言模型(LLM)，并使用PostgreSQL数据库进行对话和消息存储，确保数据的持久化和可靠性。通过LangGraph的工作流引擎，OnePort Agent能够处理复杂的多步骤任务，提供更智能、更连贯的用户体验。

## ✨ 核心功能

- 🧠 **智能对话**：基于大型语言模型的自然对话能力，支持上下文理解和多轮对话
- 🔍 **多引擎搜索**：集成Tavily提供全面的信息检索能力
- 🕸️ **网页爬取**：支持多种爬取引擎（Jina、Crawl4ai、Firecrawl），自动提取和处理网页内容，支持HTML解析和内容清洗
- 💻 **代码执行**：支持Python代码的安全执行和结果返回，适用于数据分析和自动化任务
- 🔄 **流式响应**：支持流式API响应，提供实时反馈，增强用户交互体验
- 📊 **内容处理**：智能提取和转换各种格式的内容，包括文本、表格和图像
- 💾 **数据持久化**：使用PostgreSQL数据库存储对话和消息，确保数据的可靠性和可查询性
- 🔄 **工作流引擎**：基于LangGraph构建的工作流引擎，支持复杂的多步骤任务处理
- 🎙️ **多媒体生成**：支持生成播客、PPT等多媒体内容，扩展AI助手的应用场景

## 🛠️ 快速开始

### 前置要求

- Python 3.13+
- [uv](https://github.com/astral-sh/uv) 包管理工具
- PostgreSQL 15.0+

### 安装步骤

1. **克隆仓库**

```bash
git clone https://github.com/yourusername/oneport-agent.git
cd oneport-agent
```

2. **安装 uv** (如果尚未安装)

```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows (PowerShell)
irm https://astral.sh/uv/install.ps1 | iex
```

3. **创建并激活虚拟环境**

```bash
# 创建虚拟环境
uv venv

# 激活虚拟环境 (Linux/macOS)
source .venv/bin/activate

# 激活虚拟环境 (Windows)
.venv\Scripts\activate
```

4. **安装依赖**

```bash
uv pip install -e .
```

5. **配置环境变量**

创建`.env`文件并添加必要的配置：

```ini
# API Keys
OPENAI_API_KEY=your_openai_api_key_here

# 可选配置
TAVILY_API_KEY=your_tavily_api_key_here  # 如果使用Tavily搜索

# 爬虫配置
# jina or firecrawl
CRAWL_TYPE=jina
CRAWL_API_KEY=fc-your-jina-api-key  # 如果使用jina爬虫

# 搜索引擎配置
SEARCH_API=tavily

# 数据库配置
POSTGRES_URI=postgresql://username:password@localhost:5432/dbname
```

6. **设置PostgreSQL数据库**

确保您已安装并启动PostgreSQL服务，然后创建数据库：

```bash
# 创建数据库
createdb dbname

# 或者使用psql
psql -U postgres -c "CREATE DATABASE dbname;"
psql -U postgres -c "CREATE USER username WITH ENCRYPTED PASSWORD 'password';"
psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE dbname TO username;"
```

数据库表将在应用程序首次启动时自动创建。

### 启动服务

**启动API服务器**:

```bash
uv run server.py --port 8080
```

服务器启动后，您可以通过浏览器访问 http://localhost:8080/docs 查看API文档。

## 🔌 API接口

启动服务器后，可以访问以下端点：

| 端点 | 方法 | 描述 |
|------|------|------|
| `/v1/conversation` | POST | 与AI助手对话（流式响应） |
| `/v1/tts` | POST | 文本转语音服务 |
| `/v1/podcast/generate` | POST | 生成播客内容 |
| `/v1/ppt/generate` | POST | 生成PPT演示文稿 |
| `/v1/prose/generate` | POST | 生成文章内容 |
| `/docs` | GET | Swagger API文档 |
| `/redoc` | GET | ReDoc API文档 |

### 示例请求

```bash
# 对话请求
curl -X POST "http://localhost:8080/v1/conversation" \
     -H "Content-Type: application/json" \
     -d '{
       "messages": [{"role": "user", "content": "你好，请介绍一下自己"}],
       "conversation_id": "conv_123",
       "type": "deep_research",
       "auto_accepted_plan": true,
       "enable_background_investigation": true
     }'

# 生成播客
curl -X POST "http://localhost:8080/v1/podcast/generate" \
     -H "Content-Type: application/json" \
     -d '{"content": "这是一篇关于人工智能的文章..."}'
```

## 💾 数据库配置

OnePort Agent 使用 PostgreSQL 数据库存储对话和消息数据。数据库模型包括：

### 对话表 (conversation)

| 字段 | 类型 | 描述 |
|------|------|------|
| id | VARCHAR(64) | 主键，对话ID |
| create_time | TIMESTAMP | 创建时间 |
| update_time | TIMESTAMP | 更新时间 |
| title | TEXT | 对话标题 |
| user_id | TEXT | 用户ID |

### 消息表 (message)

| 字段 | 类型 | 描述 |
|------|------|------|
| id | VARCHAR(64) | 主键，消息ID |
| conversation_id | VARCHAR(64) | 外键，关联对话ID |
| content | TEXT | 消息内容 |
| create_time | TIMESTAMP | 创建时间 |
| update_time | TIMESTAMP | 更新时间 |
| parent_message_id | VARCHAR(64) | 父消息ID（用于线程结构） |
| role | VARCHAR(50) | 消息角色（user/assistant/system） |
| tool_calls | JSONB | 工具调用数据（JSON格式） |

数据库表会在应用程序首次启动时自动创建。您可以通过以下代码示例使用数据库功能：

```python
from src.store import ConversationTable, MessageTable

# 创建新对话
conversation_table = ConversationTable()
conversation_id = conversation_table.create(
    title="新对话",
    user_id="user123"
)

# 创建新消息
message_table = MessageTable()
message_id = message_table.create(
    message_id="msg_123",
    conversation_id=conversation_id,
    content="你好，AI助手！",
    role="user"
)

# 更新消息
message_table.update(
    message_id=message_id,
    content="更新后的内容"
)
```

## 🧪 运行测试

```bash
python -m pytest
```

## Langgraph dev启动
```bash
uvx --refresh --from "langgraph-cli[inmem]" --with-editable . --python 3.12 langgraph dev --allow-blocking
```

## 📚 项目结构

```
oneport-agent/
├── src/                  # 源代码目录
│   ├── agents/           # 智能代理定义
│   ├── config/           # 配置管理
│   ├── crawler/          # 网页爬取功能
│   ├── graph/            # LangGraph工作流定义
│   ├── llms/             # 语言模型接口
│   ├── podcast/          # 播客生成功能
│   ├── ppt/              # PPT生成功能
│   ├── prose/            # 文章生成功能
│   ├── prompts/          # 提示词模板
│   ├── server/           # FastAPI服务器
│   ├── store/            # 数据库存储模块
│   │   └── postgres/     # PostgreSQL数据库接口
│   ├── tools/            # 工具函数集合
│   └── utils/            # 通用工具函数
├── tests/                # 测试代码
├── logs/                 # 日志文件目录
├── .env                  # 环境变量配置
├── conf.yaml             # 应用配置文件
├── main.py               # 主应用入口
├── server.py             # API服务器入口
└── pyproject.toml        # 项目依赖定义
```
---

<p align="center">
  <b>OnePort Agent - 让AI能力触手可及</b>
</p>