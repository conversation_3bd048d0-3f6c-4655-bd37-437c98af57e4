[project]
name = "oneport-agent"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "numpy>=2.2.5",
    "pandas>=2.2.3",
    "pytest>=8.3.5",
    "fastapi>=0.115.12",
    "uvicorn>=0.34.2",
    "httpx>=0.28.1",
    "langchain-core>=0.3.59",
    "langgraph>=0.4.8",
    "jinja2>=3.1.6",
    "python-dotenv>=1.1.0",
    "langchain-mcp-adapters>=0.0.11",
    "markdownify>=1.1.0",
    "readabilipy>=0.3.0",
    "langchain-experimental>=0.3.4",
    "langchain-openai>=0.3.16",
    "json-repair>=0.44.1",
    "beautifulsoup4>=4.13.4",
    "html5lib>=1.1",
    "lxml>=5.4.0",
    "langchain>=0.3.25",
    "langchain-community>=0.3.24",
    "InquirerPy>=0.3.4",
    "psycopg>=3.2.9",
    "psycopg-pool>=3.2.6",
    "nest-asyncio>=1.6.0",
    "psycopg-c>=3.2.9",
    "psycopg-binary>=3.2.9",
    "langgraph-checkpoint-postgres>=2.0.21",
    "firecrawl-py>=1.16.0",
    "langgraph-checkpoint-redis>=0.0.6",
    "dashscope>=1.23.5",
    "openpyxl>=3.1.5",
    "tabulate>=0.9.0",
    "boto3>=1.35.0",
    "keepa>=1.3.14",
    "junglescout-client>=0.2.2",
    "lark-oapi>=1.4.19",
    "python-barcode[images]>=0.15.1",
    "python-amazon-sp-api>=1.9.47",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[[tool.uv.index]]
name = "aliyun"
url = "https://mirrors.aliyun.com/pypi/simple/"
default = true

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.pytest.ini_options]
testpaths = ["tests"]

[tool.ruff]
line-length = 88
target-version = "py313"
