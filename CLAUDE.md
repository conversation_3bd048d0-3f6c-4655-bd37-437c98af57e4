# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# Architecture Overview

OnePort Agent is a sophisticated AI agent framework built with Python that provides intelligent conversation, web search, content processing, and workflow automation capabilities.

## Core Components

### 1. Web Service Layer (FastAPI)
- **Entry Point**: `server.py` - Main server launcher
- **Application**: `src/server/app.py` - FastAPI application with v1 API routes
- **Routes**: Modular API endpoints in `src/server/routes/`
  - Streaming conversations, agent management, browser automation, credential management

### 2. LangGraph Workflow Engine
- **Core**: `src/graph/` - State-based workflow orchestration using LangGraph
- **Workflow Definition**: `src/workflow.py` - Main graph execution logic
- **Agent Types**: Coordinator, Planner, Reporter, Researcher, Coder, Amazon specialist
- **State Management**: Redis-backed checkpointing for conversation persistence

### 3. Database Layer (PostgreSQL)
- **Schema**: `src/database/` - Conversation and message tables with JSONB support
- **Tables**: Conversations, Messages, Credentials, Tools with parent-child message relationships
- **Connections**: Uses `psycopg` with connection pooling

### 4. Multi-LLM Integration
- **Agents**: `src/agents/agents.py` - Creates specialized agents with different LLM assignments
- **Supported Models**: OpenAI, DeepSeek, Gemini, Doubao with role-based model assignment
- **Configuration**: Multi-environment support (local `conf.yaml` and cloud Alibaba MSE)

### 5. Tool Ecosystem
- **Core Tools**: `src/tools/` - Web search, web crawling, Python REPL, barcode generation
- **MCP Integration**: Model Context Protocol for external tool connections
- **Amazon Integration**: Dedicated MCP server for e-commerce analytics via SP-API

## Key Patterns

### Streaming Architecture
- All conversations use Server-Sent Events (SSE) for real-time updates
- Messages are streamed as chunks and assembled on the client side
- Checkpoint system allows conversation resume and interruption

### Agent Factory Pattern
- Agents are created via factory with specific LLM assignments
- Each agent type has dedicated prompts and tool access
- Tools are dynamically assigned based on agent capabilities

### State Management
- LangGraph state includes messages, plans, observations, and configurations
- Redis-backed persistence with TTL management
- Conversation threading with parent-child message relationships

# Development Commands

## Setup
```bash
# Install dependencies
uv pip install -e .

# Create and activate virtual environment
uv venv
source .venv/bin/activate  # Linux/macOS
# or .venv\Scripts\activate  # Windows
```

## Running the Application
```bash
# Start development server with hot reload
uv run server.py --port 8080 --reload

# Start with LangGraph Studio for visual workflow debugging
uvx --refresh --from "langgraph-cli[inmem]" --with-editable . --python 3.12 langgraph dev --allow-blocking
```

## Testing and Quality
```bash
# Run tests
python -m pytest

# Run with specific test path
python -m pytest tests/

# Code formatting and linting (Ruff configured)
ruff check src/
ruff format src/
```

## Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up --build

# Includes both web server and Lark bot services via supervisord
```

## Database Operations
- PostgreSQL tables auto-create on first startup
- Database models in `src/database/tables/`
- Connection configuration via `POSTGRES_URI` environment variable

# Important File Locations

## Configuration
- `conf.yaml` - Local configuration file
- `.env` - Environment variables (API keys, database URLs)
- `pyproject.toml` - Project dependencies and tool configuration

## Core Application Files
- `server.py` - Main server entry point
- `src/server/app.py` - FastAPI application setup
- `src/graph/builder.py` - LangGraph workflow builders
- `src/agents/agents.py` - Agent factory and definitions

## Models and Routes
- `src/models/` - Pydantic models for API requests/responses
- `src/server/routes/` - API route definitions
- Use `ApiResponse.success()` and `ApiResponse.error()` for consistent responses

## Key Integrations
- Amazon SP-API integration via dedicated MCP server in `./amazon_mcp`
- Lark bot integration for enterprise messaging
- Multi-provider web crawling (Jina, Firecrawl, Crawl4AI)
- S3 storage for file and media handling

# Environment Variables

Required:
- `OPENAI_API_KEY` - OpenAI API access
- `POSTGRES_URI` - PostgreSQL database connection
- `REDIS_URI` - Redis for caching and checkpoints

Optional:
- `TAVILY_API_KEY` - Web search capability
- `CRAWL_API_KEY` - Web crawling (Jina/Firecrawl)
- `MSE_INSTANCE_ID` - Cloud configuration (Alibaba MSE)

# API Response Format

All API responses use the standardized `ApiResponse` format:
```python
# Success
ApiResponse.success(data=your_data, message="success")

# Error  
ApiResponse.error(code="500", message="error description")
```

# Multi-Language Support

The system includes Chinese and English prompt templates. When adding new prompts or agent definitions, ensure both language variants are provided where appropriate.