import argparse
import logging
import os
import sys
from logging.handlers import RotatingFileHandler
from src.config import init_config
from src.llms import init_llm
from src.store import init_storage
from src.database import init_database
import uvicorn

# 创建logs目录（如果不存在）
os.makedirs('logs', exist_ok=True)

# 配置日志
def setup_logging():
    log_file = 'logs/oneport-agent.log'
    fmt = "%(asctime)s - %(name)s:%(lineno)d - %(levelname)s - %(message)s"
    
    logging.basicConfig(
        level=logging.INFO,
        format=fmt,
        handlers=[
            logging.StreamHandler(),
            RotatingFileHandler(log_file, maxBytes=100*1024*1024, backupCount=10)
        ],
        force=True  # 强制重新配置，覆盖现有设置
    )
    
    # 禁用uvicorn的独立日志配置
    for name in ["uvicorn", "uvicorn.access", "fastapi"]:
        logging.getLogger(name).handlers.clear()
    
    return logging.getLogger(__name__)

logger = setup_logging()
logger.info(f"Logs will be written to: {os.path.abspath('logs/oneport-agent.log')}")

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run the OnePort Agent API server")
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload (default: True except on Windows)",
    )
    parser.add_argument(
        "--host",
        type=str,
        default="localhost",
        help="Host to bind the server to (default: localhost)",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to bind the server to (default: 8000)",
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default="info",
        choices=["debug", "info", "warning", "error", "critical"],
        help="Log level (default: info)",
    )

    args = parser.parse_args()

    # 初始化配置系统
    try:
        logger.info("正在初始化配置系统...")
        config = init_config()
        logger.info("配置系统初始化成功")
    except Exception as e:
        logger.error(f"配置系统初始化失败: {e}")
        sys.exit(1)

    # 初始化数据库
    try:
        logger.info("正在初始化数据库...")
        init_database(config)
        logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        sys.exit(1)

    # 初始化存储系统
    try:
        logger.info("正在初始化存储系统...")
        init_storage()
        logger.info("存储系统初始化完成")
    except Exception as e:
        logger.error(f"存储系统初始化失败: {e}")
        sys.exit(1)

    # 初始化LLM模型
    try:
        logger.info("正在初始化LLM模型...")
        init_llm()
        logger.info("LLM模型初始化完成")
    except Exception as e:
        logger.error(f"LLM模型初始化失败: {e}")
        sys.exit(1)

    # Determine reload setting
    reload = False

    # Command line arguments override defaults
    if args.reload:
        reload = True


    logger.info("Starting OnePort Agent API server")
    uvicorn.run(
        "src.server.app:app",
        host=args.host,
        port=args.port,
        reload=reload,
        log_level=args.log_level,
        log_config=None,  # 禁用uvicorn的日志配置，保持我们的配置
    )
