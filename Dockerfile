FROM ghcr.io/astral-sh/uv:python3.13-bookworm-slim

# 配置uv使用国内PyPI源
ENV UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
ENV UV_EXTRA_INDEX_URL=https://pypi.org/simple

# Install system dependencies for PostgreSQL and image processing
RUN apt-get update && apt-get install -y \
    libpq-dev \
    gcc \
    curl \
    supervisor \
    # Required for python-barcode[images]
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    # Required for lxml
    libxml2-dev \
    libxslt-dev \
    # Required for psycopg-c
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install uv.
COPY --from=ghcr.io/astral-sh/uv:latest /uv /bin/uv

WORKDIR /app

# Copy dependency files for better layer caching
COPY uv.lock pyproject.toml ./

# Pre-cache the application dependencies
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --locked --no-install-project

# Copy application source code
COPY . /app

# 确保logs目录存在
RUN mkdir -p /app/logs

# Copy .env.example to .env if .env doesn't exist
RUN if [ -f .env.example ] && [ ! -f .env ]; then cp .env.example .env; fi

# Install the project itself
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --locked --no-dev

EXPOSE 8000

# Run the application.
CMD ["supervisord", "-c", "/app/supervisord.conf"]

