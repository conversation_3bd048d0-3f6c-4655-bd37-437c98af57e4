#!/usr/bin/env python3
"""
条形码生成工具使用示例

此示例展示了如何使用 oneport_ai 中的条形码生成工具来创建各种类型的条形码。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import src.tools.barcode_generator as bg_module


def main():
    """条形码生成工具示例主函数"""
    
    print("=" * 60)
    print("条形码生成工具使用示例")
    print("=" * 60)
    
    # 1. 获取工具信息
    print("\n1. 获取条形码生成工具信息：")
    print("-" * 40)
    info_func = bg_module.get_barcode_info.func
    info = info_func()
    print(info)
    
    # 2. 生成不同格式的条形码
    print("\n\n2. 生成不同格式的条形码：")
    print("-" * 40)
    
    examples = [
        {
            "name": "Code128 条形码（文本）",
            "data": "Hello OnePort Agent!",
            "format": "code128",
            "output": "png"
        },
        {
            "name": "EAN13 商品条码",
            "data": "1234567890123",
            "format": "ean13", 
            "output": "png"
        },
        {
            "name": "Code39 工业条码",
            "data": "ONEPORT123",
            "format": "code39",
            "output": "svg"
        },
        {
            "name": "UPC-A 美国商品码",
            "data": "123456789012",
            "format": "upca",
            "output": "png"
        },
        {
            "name": "ISBN13 图书条码",
            "data": "9781234567890",
            "format": "isbn13",
            "output": "png"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. 生成 {example['name']}:")
        print(f"   数据: {example['data']}")
        print(f"   格式: {example['format']}")
        print(f"   输出: {example['output']}")
        
        try:
            barcode_func = bg_module.generate_barcode.func
            result = barcode_func(
                data=example['data'],
                format_type=example['format'],
                output_format=example['output'],
                with_text=True,
                font_size=12
            )
            print(f"   结果: {result}")
        except Exception as e:
            print(f"   错误: {str(e)}")
    
    # 3. 生成 Base64 格式的条形码（适用于网页显示）
    print("\n\n3. 生成 Base64 格式条形码（适用于网页显示）：")
    print("-" * 40)
    
    try:
        barcode_func = bg_module.generate_barcode.func
        base64_result = barcode_func(
            data="OnePort Agent Base64 Demo",
            format_type="code128",
            output_format="base64",
            with_text=True
        )
        
        if base64_result.startswith("data:image/png;base64,"):
            print("✅ Base64 条形码生成成功！")
            print(f"Base64 数据长度: {len(base64_result)} 字符")
            print("可以直接在HTML中使用:")
            print(f'<img src="{base64_result[:50]}..." alt="条形码" />')
        else:
            print(f"生成结果: {base64_result}")
            
    except Exception as e:
        print(f"生成 Base64 条形码时出错: {str(e)}")
    
    # 4. 自定义尺寸和样式
    print("\n\n4. 生成自定义样式的条形码：")
    print("-" * 40)
    
    try:
        barcode_func = bg_module.generate_barcode.func
        custom_result = barcode_func(
            data="CUSTOM-STYLE-123",
            format_type="code39",
            output_format="png",
            width=200,  # 自定义宽度
            height=50,  # 自定义高度
            with_text=True,
            font_size=14,
            output_path="workspace/custom_barcode.png"
        )
        print(f"自定义样式条形码结果: {custom_result}")
        
    except Exception as e:
        print(f"生成自定义样式条形码时出错: {str(e)}")
    
    print("\n" + "=" * 60)
    print("示例运行完成！")
    print("生成的条形码文件保存在 workspace/ 目录中。")
    print("=" * 60)


if __name__ == "__main__":
    main() 