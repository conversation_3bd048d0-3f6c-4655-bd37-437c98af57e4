#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
S3存储服务使用示例

此示例展示如何使用S3Storage类进行文件的上传、下载和删除操作。
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.store.storage import S3Storage, create_s3_storage, create_s3_storage_from_env


def example_basic_usage():
    """基本使用示例"""
    print("=== S3存储基本使用示例 ===")
    
    # 方式1: 直接创建实例
    storage = S3Storage(
        bucket_name="your-bucket-name",
        aws_access_key_id="your-access-key",
        aws_secret_access_key="your-secret-key",
        region_name="us-east-1"
    )
    
    # 创建临时文件用于测试
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("这是一个测试文件内容\n测试S3上传功能")
        temp_file_path = f.name
    
    try:
        # 1. 上传文件
        print("1. 上传文件...")
        s3_key = "test/example.txt"
        success = storage.upload_file(
            local_file_path=temp_file_path,
            s3_key=s3_key,
            metadata={"author": "oneport-agent", "purpose": "test"},
            content_type="text/plain"
        )
        print(f"上传结果: {'成功' if success else '失败'}")
        
        # 2. 检查文件是否存在
        print("2. 检查文件是否存在...")
        exists = storage.file_exists(s3_key)
        print(f"文件存在: {exists}")
        
        # 3. 获取文件信息
        print("3. 获取文件信息...")
        file_info = storage.get_file_info(s3_key)
        if file_info:
            print(f"文件大小: {file_info['size']} 字节")
            print(f"修改时间: {file_info['last_modified']}")
            print(f"内容类型: {file_info['content_type']}")
            print(f"元数据: {file_info['metadata']}")
        
        # 4. 下载文件
        print("4. 下载文件...")
        download_path = tempfile.mktemp(suffix='.txt')
        success = storage.download_file(s3_key, download_path)
        print(f"下载结果: {'成功' if success else '失败'}")
        
        if success and os.path.exists(download_path):
            with open(download_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"下载的文件内容: {content}")
            os.unlink(download_path)
        
        # 5. 列出文件
        print("5. 列出存储桶中的文件...")
        files = storage.list_files(prefix="test/", max_keys=10)
        print(f"找到 {len(files)} 个文件:")
        for file_info in files:
            print(f"  - {file_info['key']} ({file_info['size']} 字节)")
        
        # 6. 删除文件
        print("6. 删除文件...")
        success = storage.delete_file(s3_key)
        print(f"删除结果: {'成功' if success else '失败'}")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)


def example_factory_function():
    """使用工厂函数创建实例"""
    print("\n=== 使用工厂函数创建实例 ===")
    
    # 使用工厂函数创建实例
    storage = create_s3_storage(
        bucket_name="your-bucket-name",
        aws_access_key_id="your-access-key",
        aws_secret_access_key="your-secret-key",
        region_name="ap-southeast-1"  # 新加坡区域
    )
    
    print("S3存储实例创建成功")


def example_environment_variables():
    """使用环境变量配置"""
    print("\n=== 使用环境变量配置 ===")
    
    # 设置环境变量示例
    required_env_vars = [
        "AWS_S3_BUCKET_NAME",
        "AWS_ACCESS_KEY_ID",
        "AWS_SECRET_ACCESS_KEY",
        "AWS_DEFAULT_REGION",
    ]
    
    print("需要设置以下环境变量:")
    for var in required_env_vars:
        value = os.getenv(var, "未设置")
        print(f"  {var}: {value}")
    
    # 如果环境变量已设置，则创建实例
    if os.getenv("AWS_S3_BUCKET_NAME"):
        try:
            storage = create_s3_storage_from_env()
            print("从环境变量创建S3存储实例成功")
        except Exception as e:
            print(f"创建实例失败: {e}")
    else:
        print("请设置环境变量后再试")


def example_minio_compatibility():
    """MinIO兼容性示例"""
    print("\n=== MinIO兼容性示例 ===")
    
    # 使用MinIO或其他S3兼容服务
    storage = S3Storage(
        bucket_name="test-bucket",
        aws_access_key_id="minio-access-key",
        aws_secret_access_key="minio-secret-key",
        region_name="us-east-1",
        endpoint_url="http://localhost:9000"  # MinIO默认端点
    )
    
    print("MinIO兼容的S3存储实例创建成功")


def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    try:
        # 尝试连接不存在的存储桶
        storage = S3Storage(
            bucket_name="non-existent-bucket-12345",
            aws_access_key_id="invalid-key",
            aws_secret_access_key="invalid-secret"
        )
    except Exception as e:
        print(f"预期的错误: {e}")
    
    # 正确的配置但操作不存在的文件
    try:
        storage = S3Storage(
            bucket_name="your-bucket-name",
            aws_access_key_id="your-access-key",
            aws_secret_access_key="your-secret-key"
        )
        
        # 尝试下载不存在的文件
        success = storage.download_file("non-existent-file.txt", "/tmp/test.txt")
        print(f"下载不存在文件的结果: {success}")
        
        # 尝试获取不存在文件的信息
        info = storage.get_file_info("non-existent-file.txt")
        print(f"不存在文件的信息: {info}")
        
    except Exception as e:
        print(f"操作错误: {e}")


def example_global_storage():
    """使用全局S3存储实例示例"""
    print("\n=== 使用全局S3存储实例示例 ===")
    
    # 导入全局S3存储实例
    from src.store import s3_storage
    
    if s3_storage is None:
        print("全局S3存储实例未初始化")
        print("请检查以下环境变量是否正确设置:")
        print("- S3_AK (S3访问密钥)")
        print("- S3_SK (S3秘密密钥)")
        print("- S3_ENDPOINT (S3端点URL)")
        print("- S3_BUCKET_NAME (S3存储桶名称)")
        print("- S3_REGION (S3区域)")
        return
    
    print("全局S3存储实例已初始化，可以直接使用")
    
    # 使用全局实例进行操作
    try:
        # 1. 上传文本数据
        print("1. 使用全局实例上传文本数据...")
        test_text = "这是通过全局S3存储实例上传的测试文本"
        s3_key = "global-test/test.txt"
        
        success = s3_storage.upload_text(
            text=test_text,
            s3_key=s3_key,
            metadata={"source": "global-storage-example"}
        )
        print(f"上传结果: {'成功' if success else '失败'}")
        
        # 2. 检查文件是否存在
        print("2. 检查文件是否存在...")
        exists = s3_storage.file_exists(s3_key)
        print(f"文件存在: {exists}")
        
        # 3. 下载文本数据
        if exists:
            print("3. 下载文本数据...")
            downloaded_text = s3_storage.download_text(s3_key)
            print(f"下载的文本: {downloaded_text}")
        
        # 4. 上传JSON数据
        print("4. 上传JSON数据...")
        test_data = {
            "message": "这是通过全局S3存储实例上传的JSON数据",
            "timestamp": "2024-01-01T00:00:00Z",
            "source": "global-storage-example"
        }
        json_key = "global-test/data.json"
        
        success = s3_storage.upload_json(
            data=test_data,
            s3_key=json_key,
            indent=2
        )
        print(f"JSON上传结果: {'成功' if success else '失败'}")
        
        # 5. 下载JSON数据
        if success:
            print("5. 下载JSON数据...")
            downloaded_data = s3_storage.download_json(json_key)
            print(f"下载的JSON数据: {downloaded_data}")
        
        # 6. 列出全局测试文件
        print("6. 列出全局测试文件...")
        files = s3_storage.list_files(prefix="global-test/")
        print(f"找到 {len(files)} 个文件:")
        for file_info in files:
            print(f"  - {file_info['key']} ({file_info['size']} 字节)")
        
        # 7. 清理测试文件
        # print("7. 清理测试文件...")
        # cleanup_keys = [s3_key, json_key]
        # for key in cleanup_keys:
        #     if s3_storage.file_exists(key):
        #         success = s3_storage.delete_file(key)
        #         print(f"删除 {key}: {'成功' if success else '失败'}")
        
    except Exception as e:
        print(f"操作过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("S3存储服务使用示例")
    print("=" * 50)
    
    # 注意: 以下示例需要有效的AWS凭证才能正常运行
    print("注意: 运行此示例需要有效的AWS凭证和存储桶")
    print("请根据实际情况修改配置参数\n")
    
    # 运行各种示例
    try:
        # example_basic_usage()  # 需要有效凭证
        # example_factory_function()
        # example_environment_variables()
        # example_minio_compatibility()
        # example_error_handling()
        example_global_storage()  # 新增的全局存储示例
        
    except Exception as e:
        print(f"运行示例时发生错误: {e}")


if __name__ == "__main__":
    main() 