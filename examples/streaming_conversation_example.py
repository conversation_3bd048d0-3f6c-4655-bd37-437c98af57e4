#!/usr/bin/env python3
"""
流式对话 API 使用示例

这个示例展示了如何使用新的流式 create_conversation API
"""

import asyncio
import json
import httpx
from typing import AsyncGenerator

async def stream_conversation(
    base_url: str = "http://localhost:8080",
    company_id: str = "test_company",
    user_id: str = "test_user",
    message: str = "Hello, can you help me analyze some sales data?"
) -> AsyncGenerator[dict, None]:
    """
    创建流式对话并返回响应流
    
    Args:
        base_url: API 基础 URL
        company_id: 公司 ID
        user_id: 用户 ID  
        message: 用户消息
        
    Yields:
        dict: 流式响应数据
    """
    
    # 构建请求数据
    request_data = {
        "company_id": company_id,
        "messages": [
            {
                "role": "user",
                "content": message
            }
        ],
        "store_id": ["store1", "store2"],
        "file_paths": [],
        "enable_audio_output": False
    }
    
    # 请求头
    headers = {
        "X-User-ID": user_id,
        "Content-Type": "application/json"
    }
    
    print(f"🚀 Starting streaming conversation...")
    print(f"   Company ID: {company_id}")
    print(f"   User ID: {user_id}")
    print(f"   Message: {message}")
    print(f"   URL: {base_url}/conversation")
    print()
    
    try:
        async with httpx.AsyncClient(timeout=300.0) as client:
            async with client.stream(
                "POST",
                f"{base_url}/conversation",
                headers=headers,
                json=request_data
            ) as response:
                
                if response.status_code != 200:
                    print(f"❌ HTTP Error: {response.status_code}")
                    print(f"   Response: {await response.aread()}")
                    return
                
                print("📡 Connected to stream, receiving data...\n")
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        try:
                            # 解析 SSE 数据
                            data_str = line[6:].strip()
                            if data_str:
                                data = json.loads(data_str)
                                yield data
                                
                                # 如果是完成或错误消息，结束流
                                if data.get("type") in ["complete", "error"]:
                                    break
                                    
                        except json.JSONDecodeError as e:
                            print(f"⚠️  JSON decode error: {e}")
                            print(f"   Raw line: {line}")
                            
    except httpx.TimeoutException:
        print("⏰ Request timeout")
    except httpx.ConnectError:
        print("🔌 Connection error - is the server running?")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

async def demo_streaming_conversation():
    """演示流式对话功能"""
    
    print("=" * 60)
    print("🎯 Streaming Conversation Demo")
    print("=" * 60)
    
    # 配置
    base_url = "http://localhost:8080"
    company_id = "demo_company_123"
    user_id = "demo_user_456"
    message = "Hello! I need help analyzing customer data and generating insights. Can you coordinate with your data team?"
    
    # 统计信息
    message_count = 0
    status_updates = 0
    agent_responses = 0
    
    try:
        async for data in stream_conversation(base_url, company_id, user_id, message):
            message_count += 1
            message_type = data.get("type", "unknown")
            message_data = data.get("data", "")
            timestamp = data.get("timestamp", 0)
            
            # 格式化时间戳
            time_str = f"{timestamp:.3f}" if timestamp else "N/A"
            
            # 根据消息类型显示不同的图标和颜色
            if message_type == "status":
                status_updates += 1
                print(f"📋 [{time_str}] Status: {message_data}")
                
            elif message_type == "agent_response":
                agent_responses += 1
                print(f"🤖 [{time_str}] Agent Response:")
                
                # 尝试格式化 agent 响应
                if isinstance(message_data, dict):
                    if "content" in message_data:
                        print(f"   Content: {message_data['content']}")
                    else:
                        print(f"   Data: {json.dumps(message_data, indent=2)}")
                else:
                    print(f"   Data: {message_data}")
                    
            elif message_type == "complete":
                print(f"✅ [{time_str}] Task Completed: {message_data}")
                
            elif message_type == "error":
                print(f"❌ [{time_str}] Error: {message_data}")
                
            elif message_type == "heartbeat":
                print(f"💓 [{time_str}] Heartbeat")
                
            else:
                print(f"❓ [{time_str}] Unknown type '{message_type}': {message_data}")
            
            print()  # 空行分隔
            
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
    
    # 显示统计信息
    print("=" * 60)
    print("📊 Demo Statistics")
    print("=" * 60)
    print(f"Total messages received: {message_count}")
    print(f"Status updates: {status_updates}")
    print(f"Agent responses: {agent_responses}")
    print(f"Demo completed successfully! 🎉")

async def test_multiple_conversations():
    """测试多个并发对话"""
    
    print("\n" + "=" * 60)
    print("🔄 Multiple Conversations Test")
    print("=" * 60)
    
    # 创建多个对话任务
    tasks = []
    for i in range(3):
        task = asyncio.create_task(
            stream_conversation(
                company_id=f"company_{i}",
                user_id=f"user_{i}",
                message=f"Hello from conversation {i}! Can you help me with task {i}?"
            )
        )
        tasks.append(task)
    
    # 并发处理所有对话
    print(f"🚀 Starting {len(tasks)} concurrent conversations...")
    
    for i, task in enumerate(tasks):
        print(f"\n--- Conversation {i} ---")
        try:
            async for data in task:
                print(f"Conv{i}: {data['type']} - {data['data']}")
                if data.get("type") in ["complete", "error"]:
                    break
        except Exception as e:
            print(f"Conv{i}: Error - {e}")
    
    print("\n✅ All conversations completed!")

if __name__ == "__main__":
    print("🎬 Starting Streaming Conversation Examples\n")
    
    # 运行演示
    asyncio.run(demo_streaming_conversation())
    
    # 可选：测试多个对话（需要服务器支持）
    # asyncio.run(test_multiple_conversations())
    
    print("\n🏁 Examples completed!")
