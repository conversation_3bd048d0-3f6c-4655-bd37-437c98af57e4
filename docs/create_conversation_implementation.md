# Create Conversation 流式实现文档

## 概述

`create_conversation` 函数实现了一个完整的异步流式对话系统：

1. 根据 `ChatRequest` 中的 `company_id` 获取对应的 agents
2. 创建异步任务，使用 `create_deep_agent` 创建主 agent（Personal Assistant）并配置子 agents（Data Collector、Data Analyst）
3. 处理用户输入的 query 进行问答
4. 将 agent 生成的数据流式写入队列
5. 从队列中读取数据并以 Server-Sent Events (SSE) 格式流式返回给前端

## 架构设计

### 核心组件

1. **`create_conversation`**: 主入口函数，处理请求并启动异步任务
2. **`agent_task`**: 异步任务函数，创建 agent 并处理用户查询
3. **`stream_generator`**: 流式生成器，从队列读取数据并返回 SSE 格式
4. **`conversation_queues`**: 全局队列字典，存储每个对话的数据流

### 数据流

```
用户请求 → create_conversation → 启动 agent_task → 创建队列 → 流式返回
                    ↓                    ↓              ↓
               创建对话记录        创建 deep_agent    写入队列数据
                                      ↓              ↓
                               处理用户查询      stream_generator
                                      ↓              ↓
                               生成响应数据        SSE 格式输出
```

## 实现逻辑

### 1. 主函数：create_conversation

```python
@router.post("/conversation")
async def create_conversation(request: ChatRequest, x_user_id: str = Header(..., alias="X-User-ID")):
    # 1. 获取公司的 agents
    agent_table = AgentTable()
    agents_data = agent_table.get_by_company(company_id=request.company_id)

    # 2. 分类 agents
    main_agent = None
    sub_agents_data = []
    for agent_data in agents_data:
        if agent_data["role"] == "Personal Assistant":
            main_agent = agent_data
        elif agent_data["role"] in ["Data Collector", "Data Analyst"]:
            sub_agents_data.append(agent_data)

    # 3. 构建子 agents 配置
    subagents: List[SubAgent] = []
    for sub_agent_data in sub_agents_data:
        subagent: SubAgent = {
            "name": sub_agent_data["name"],
            "description": f"{sub_agent_data['role']} - {sub_agent_data.get('prompt', 'Specialized agent for data operations')}",
            "prompt": sub_agent_data.get("prompt", f"You are a {sub_agent_data['role']} agent. Help with data-related tasks."),
        }
        if sub_agent_data.get("tools"):
            subagent["tools"] = sub_agent_data["tools"]
        subagents.append(subagent)

    # 4. 创建对话记录
    conversation_id = conversation_table.create(...)

    # 5. 启动异步任务
    asyncio.create_task(agent_task(...))

    # 6. 返回流式响应
    return StreamingResponse(stream_generator(conversation_id), ...)
```

### 2. 异步任务：agent_task

```python
async def agent_task(conversation_id: str, request: ChatRequest, x_user_id: str, main_agent_data: dict, subagents_config: List[SubAgent]):
    # 1. 创建队列
    queue = asyncio.Queue()
    conversation_queues[conversation_id] = queue

    # 2. 发送状态更新
    await queue.put({"type": "status", "data": "Initializing agents..."})

    # 3. 创建主 agent 实例
    agent = async_create_deep_agent(
        tools=main_agent_tools,
        instructions=main_agent_instructions,
        subagents=subagents_config
    )

    # 4. 处理用户查询
    if request.messages:
        user_query = request.messages[-1].content
        config = {"configurable": {"thread_id": conversation_id}}

        # 5. 流式处理 agent 响应
        async for chunk in agent.astream({"messages": [{"role": "user", "content": user_query}]}, config=config):
            await queue.put({"type": "agent_response", "data": chunk})

    # 6. 完成任务
    await queue.put({"type": "complete", "data": "Agent task completed successfully"})
```

### 3. 流式生成器：stream_generator

```python
async def stream_generator(conversation_id: str):
    queue = conversation_queues[conversation_id]

    while True:
        try:
            item = await asyncio.wait_for(queue.get(), timeout=30.0)
            yield f"data: {json.dumps(item)}\n\n"  # SSE 格式

            if item.get("type") in ["complete", "error"]:
                break
        except asyncio.TimeoutError:
            yield f"data: {json.dumps({'type': 'heartbeat'})}\n\n"  # 心跳
```

## 默认 Agent 配置

系统预定义了三个默认 agents：

```python
DEFAULT_AGENT_LIST = [
    {
        "name": "Hifire",
        "role": "Personal Assistant",
        "tag": "official"
    },
    {
        "name": "Harvest", 
        "role": "Data Collector",
        "tag": "official"
    },
    {
        "name": "Neo",
        "role": "Data Analyst", 
        "tag": "official"
    }
]
```

## 流式响应格式

### Server-Sent Events (SSE) 格式

所有响应都以 SSE 格式返回：

```
data: {"type": "status", "data": "Initializing agents...", "timestamp": 1234567890.123}

data: {"type": "agent_response", "data": {"content": "Hello! How can I help you?"}, "timestamp": 1234567890.456}

data: {"type": "complete", "data": "Agent task completed successfully", "timestamp": 1234567890.789}
```

### 消息类型

- **`status`**: 任务状态更新
- **`agent_response`**: Agent 生成的响应数据
- **`complete`**: 任务完成
- **`error`**: 错误信息
- **`heartbeat`**: 心跳消息（保持连接）

## 队列管理

### 全局队列字典

```python
conversation_queues = {}  # {conversation_id: asyncio.Queue}
```

### 队列生命周期

1. **创建**: 在 `agent_task` 开始时创建
2. **使用**: 在任务执行过程中写入数据
3. **读取**: 通过 `stream_generator` 读取数据
4. **清理**: 在流结束时自动清理

## 错误处理

### 任务级错误

```python
try:
    # agent 任务逻辑
except Exception as e:
    await queue.put({"type": "error", "data": f"Agent task error: {str(e)}"})
```

### 流级错误

```python
try:
    # 流生成逻辑
except Exception as e:
    yield f"data: {json.dumps({'type': 'error', 'data': str(e)})}\n\n"
```

### 超时处理

- **队列读取超时**: 30秒，发送心跳消息
- **任务执行超时**: 可配置，默认无限制

## 使用示例

### 前端 JavaScript

```javascript
const eventSource = new EventSource('/conversation', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-User-ID': 'user123'
    },
    body: JSON.stringify({
        company_id: 'company123',
        messages: [
            {role: 'user', content: 'Hello, can you help me analyze some data?'}
        ]
    })
});

eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);

    switch(data.type) {
        case 'status':
            console.log('Status:', data.data);
            break;
        case 'agent_response':
            console.log('Agent response:', data.data);
            break;
        case 'complete':
            console.log('Task completed');
            eventSource.close();
            break;
        case 'error':
            console.error('Error:', data.data);
            eventSource.close();
            break;
    }
};
```

### Python 客户端

```python
import httpx
import json

async with httpx.AsyncClient() as client:
    async with client.stream(
        'POST',
        '/conversation',
        headers={'X-User-ID': 'user123'},
        json={
            'company_id': 'company123',
            'messages': [
                {'role': 'user', 'content': 'Hello, can you help me analyze some data?'}
            ]
        }
    ) as response:
        async for line in response.aiter_lines():
            if line.startswith('data: '):
                data = json.loads(line[6:])
                print(f"{data['type']}: {data['data']}")
```

## 性能考虑

### 内存管理

- 队列自动清理，避免内存泄漏
- 限制队列大小，防止内存溢出

### 并发处理

- 每个对话独立的队列和任务
- 支持多个并发对话

### 错误恢复

- 任务失败不影响其他对话
- 自动清理失败的队列

## 扩展功能

1. **持久化队列**: 使用 Redis 等外部队列系统
2. **负载均衡**: 分布式任务处理
3. **监控指标**: 任务执行时间、成功率等
4. **重试机制**: 失败任务自动重试
5. **优先级队列**: 重要任务优先处理
