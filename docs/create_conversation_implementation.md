# Create Conversation 实现文档

## 概述

`create_conversation` 函数实现了根据 `ChatRequest` 中的 `company_id` 获取对应的 agents，并创建一个以 Personal Assistant 为主 agent，Data Collector 和 Data Analyst 为子 agent 的对话系统。

## 实现逻辑

### 1. 获取公司的 Agents

```python
agent_table = AgentTable()
agents_data = agent_table.get_by_company(company_id=request.company_id)
```

根据 `company_id` 从数据库中获取该公司的所有 agents。

### 2. Agent 分类

```python
main_agent = None
sub_agents_data = []

for agent_data in agents_data:
    if agent_data["role"] == "Personal Assistant":
        main_agent = agent_data
    elif agent_data["role"] in ["Data Collector", "Data Analyst"]:
        sub_agents_data.append(agent_data)
```

- **主 Agent**: `Personal Assistant` 角色的 agent
- **子 Agents**: `Data Collector` 和 `Data Analyst` 角色的 agents

### 3. 构建子 Agent 配置

```python
subagents: List[SubAgent] = []
for sub_agent_data in sub_agents_data:
    subagent: SubAgent = {
        "name": sub_agent_data["name"],
        "description": f"{sub_agent_data['role']} - {sub_agent_data.get('prompt', 'Specialized agent for data operations')}",
        "prompt": sub_agent_data.get("prompt", f"You are a {sub_agent_data['role']} agent. Help with data-related tasks."),
    }
    if sub_agent_data.get("tools"):
        subagent["tools"] = sub_agent_data["tools"]
    subagents.append(subagent)
```

为每个子 agent 创建 `SubAgent` 配置，包括：
- `name`: Agent 名称
- `description`: Agent 描述（用于主 agent 决定何时调用）
- `prompt`: Agent 的系统提示词
- `tools`: Agent 可用的工具列表（可选）

### 4. 创建对话记录

```python
conversation_table = ConversationTable()
conversation_id = conversation_table.create(
    user_id=x_user_id,
    company_id=request.company_id,
    title="New Conversation",
    status="in_progress",
    conversation_id=request.conversation_id,
    store_ids=request.store_id
)
```

在数据库中创建新的对话记录。

## 默认 Agent 配置

系统预定义了三个默认 agents：

```python
DEFAULT_AGENT_LIST = [
    {
        "name": "Hifire",
        "role": "Personal Assistant",
        "tag": "official"
    },
    {
        "name": "Harvest", 
        "role": "Data Collector",
        "tag": "official"
    },
    {
        "name": "Neo",
        "role": "Data Analyst", 
        "tag": "official"
    }
]
```

## API 响应

成功创建对话后，返回以下数据：

```json
{
    "code": "200",
    "message": "Conversation created successfully",
    "data": {
        "conversation_id": "uuid-string",
        "main_agent": "Hifire",
        "sub_agents": ["Harvest", "Neo"],
        "agent_config": {
            "main_agent": { /* 主 agent 完整配置 */ },
            "subagents": [ /* 子 agents 配置列表 */ ],
            "instructions": "主 agent 指令",
            "tools": ["tool1", "tool2"]
        }
    }
}
```

## 错误处理

- **404**: 公司没有找到 agents 或缺少 Personal Assistant
- **500**: 数据库操作或其他系统错误

## 使用示例

```python
# 创建对话请求
request = ChatRequest(
    company_id="company_123",
    conversation_id=None,  # 可选，系统会自动生成
    store_id=["store1", "store2"],
    messages=[],
    file_paths=[],
    enable_audio_output=False
)

# 调用 API
response = await create_conversation(request, x_user_id="user_123")
```

## 后续扩展

1. **实际创建 Agent 实例**: 当前只是准备了配置，后续可以使用 `async_create_deep_agent` 创建实际的 agent 实例
2. **动态工具分配**: 根据对话内容动态分配工具给不同的 agents
3. **Agent 状态管理**: 跟踪和管理 agent 的执行状态
4. **自定义 Agent**: 支持用户自定义 agent 角色和配置
