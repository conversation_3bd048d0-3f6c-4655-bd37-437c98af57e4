# OnePort Agent 配置文件示例
# 复制此文件为 .env 并根据实际情况修改配置值

# ==================== 必需配置 ====================

# ==================== 可选配置 ====================

# 搜索引擎配置
TAVILY_API_KEY=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

# 数据存储配置
POSTGRES_URI=postgresql://postgres:<EMAIL>:5432/db
REDIS_URI=redis://:<EMAIL>:6379

# 音频配置 (Dashscope TTS)
DASHSCOPE_API_KEY="sk-b2fb927e10114a25b305e9d46613573f"
DASHSCOPE_VOICE_TYPE="longhan_v2"


# S3 存储配置
S3_AK="621d6e8710fa1b892bfe522272518ccd"
S3_SK="02c6a23643a5556dd4de42ed1cfef10bd1d9a5c04b9dbb8347feab5c85c149da"
S3_ENDPOINT="https://ejnpxeoiekvwsrowibjm.supabase.co/storage/v1/s3"
S3_REGION="us-west-1"
S3_BUCKET_NAME="chat-attachments"

# 爬虫配置
CRAWL_TYPE=jina
CRAWL_API_KEY=fc-aada1c86c1634595891278a2af7b9190

# LLM配置 - 统一AI网关
LLM_API_KEY="oneport"
LLM_BASE_URL="http://llm.tst.oneport.ink/v1"

# 基础配置
MAX_SEARCH_RESULTS=3

# ==================== 超时配置 ====================
# 代理执行超时设置
AGENT_EXECUTION_TIMEOUT=180
AGENT_RECURSION_LIMIT=50

# MCP 相关超时设置
MCP_CONNECTION_TIMEOUT=30
MCP_READ_TIMEOUT=60

# 流式响应超时设置
STREAM_TIMEOUT=300
STREAM_KEEPALIVE_INTERVAL=30

# 工具调用超时设置
TOOL_CALL_TIMEOUT=120

# 数据库操作超时设置
DB_OPERATION_TIMEOUT=30

# ==================== 监控配置 ====================
# LangSmith 跟踪和监控（可选）
LANGSMITH_TRACING=true
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
LANGSMITH_API_KEY="***************************************************"
LANGSMITH_PROJECT="oneport-agent"

# ==================== 其他配置 ====================


# ads 配置
CLIENT_ID="your_client_id"

