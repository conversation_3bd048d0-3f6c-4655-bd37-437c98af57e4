#!/usr/bin/env python3
"""
Simple test script for the auth/create endpoint
"""
import requests
import json

def test_auth_create_endpoint():
    url = "http://localhost:8000/auth/create"
    
    # Test data matching the API specification
    test_data = {
        "company_id": "test_company_123",
        "store_id": "test_store_456",
        "type": "api_key",
        "platform": "amazon",
        "params": {
            "api_key": "xxxx"
        }
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            print("✓ Test passed!")
        else:
            print("✗ Test failed!")
            
    except requests.exceptions.ConnectionError:
        print("Server is not running. Start the server first with: python server.py")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_auth_create_endpoint()