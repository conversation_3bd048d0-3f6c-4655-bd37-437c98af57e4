#!/usr/bin/env python3
"""
测试脚本：验证Fireworks API兼容性修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试导入是否成功"""
    try:
        from src.graph.nodes import planner_node
        from src.config.agents import AGENT_LLM_MAP
        from src.llms.llm import get_llm_by_type
        print("✅ 所有模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_llm_binding():
    """测试LLM绑定工具是否不再使用strict参数"""
    try:
        from src.llms.llm import get_llm_by_type
        from src.config.agents import AGENT_LLM_MAP
        
        # 获取planner使用的LLM
        llm = get_llm_by_type(AGENT_LLM_MAP["planner"])
        
        # 测试bind_tools方法（不使用strict参数）
        bound_llm = llm.bind_tools([])
        print("✅ LLM工具绑定成功（无strict参数）")
        return True
    except Exception as e:
        print(f"❌ LLM工具绑定失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 开始测试Fireworks API兼容性修复...")
    print()
    
    # 测试导入
    import_success = test_import()
    
    # 测试LLM绑定
    binding_success = test_llm_binding()
    
    print()
    if import_success and binding_success:
        print("🎉 所有测试通过！Fireworks API兼容性问题已修复")
        print("📝 修复内容：移除了bind_tools方法中的strict=True参数")
        return 0
    else:
        print("❌ 测试失败，请检查修复")
        return 1

if __name__ == "__main__":
    exit(main()) 