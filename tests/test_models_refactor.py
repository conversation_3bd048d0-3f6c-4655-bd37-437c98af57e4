#!/usr/bin/env python3
"""
测试模型重构后的结构
"""
import sys
import importlib

def test_new_model_imports():
    """测试新模型文件是否能正常导入"""
    print("测试新模型文件导入...")
    
    model_modules = [
        ("src.models.common_models", ["ApiResponse"]),
        ("src.models.chat_models", ["ContentItem", "ChatMessage", "ChatRequest"]),
        ("src.models.credential_models", ["CredentialCreateRequest", "CredentialResponse"]),
        ("src.models.agent_models", ["Agent", "AgentListResponse"]),
    ]
    
    all_passed = True
    for module_name, expected_classes in model_modules:
        try:
            module = importlib.import_module(module_name)
            print(f"✓ {module_name} - 模块导入成功")
            
            for class_name in expected_classes:
                if hasattr(module, class_name):
                    print(f"  ✓ {class_name} - 类存在")
                else:
                    print(f"  ✗ {class_name} - 类缺失")
                    all_passed = False
                    
        except Exception as e:
            print(f"✗ {module_name} - 模块导入失败: {e}")
            all_passed = False
    
    return all_passed

def test_direct_model_imports():
    """测试直接从新模型位置导入"""
    print("\n测试直接模型导入...")
    
    try:
        # Test direct imports from new model structure
        from src.models.common_models import ApiResponse
        from src.models.chat_models import ContentItem, ChatMessage, ChatRequest
        from src.models.credential_models import CredentialCreateRequest, CredentialResponse
        from src.models.agent_models import Agent, AgentListResponse
        
        print("✓ 所有模型可以直接从新位置导入")
        
        # 测试实例化
        api_response = ApiResponse.success(data={"test": "data"})
        chat_message = ChatMessage(role="user", content="test")
        agent = Agent(agent_id="test", name="test", type="assistant", role="test", tag="official")
        
        print("✓ 模型实例化正常")
        return True
        
    except Exception as e:
        print(f"✗ 直接模型导入测试失败: {e}")
        return False

def test_route_imports():
    """测试路由文件的模型导入"""
    print("\n测试路由文件的模型导入...")
    
    route_modules = [
        "src.server.routes.chat_routes",
        "src.server.routes.credential_routes", 
        "src.server.routes.agent_routes",
    ]
    
    all_passed = True
    for module_name in route_modules:
        try:
            importlib.import_module(module_name)
            print(f"✓ {module_name} - 路由模块导入成功")
        except Exception as e:
            print(f"✗ {module_name} - 路由模块导入失败: {e}")
            all_passed = False
            
    return all_passed

def test_app_structure():
    """测试整体应用结构"""
    print("\n测试整体应用结构...")
    
    try:
        from src.server.app import app
        routes = [route.path for route in app.routes]
        
        expected_routes = [
            "/v1/conversation",
            "/v1/credential/list",
            "/v1/credential/create", 
            "/v1/credential/get",
            "/v1/agent/list",
            "/health"
        ]
        
        missing_routes = []
        for expected in expected_routes:
            found = any(expected in route for route in routes)
            if not found:
                missing_routes.append(expected)
        
        if missing_routes:
            print(f"✗ 缺失的路由: {missing_routes}")
            return False
        else:
            print("✓ 所有预期路由存在")
            return True
            
    except Exception as e:
        print(f"✗ 应用结构测试失败: {e}")
        return False

def test_model_functionality():
    """测试模型功能"""
    print("\n测试模型功能...")
    
    try:
        from src.models.common_models import ApiResponse
        from src.models.agent_models import Agent, AgentListResponse
        from src.models.chat_models import ChatMessage, ContentItem
        
        # 测试ApiResponse功能
        success_response = ApiResponse.success(data={"test": "success"})
        error_response = ApiResponse.error(code="500", message="test error")
        
        assert success_response.code == "0"
        assert error_response.code == "500"
        print("✓ ApiResponse功能测试通过")
        
        # 测试Agent模型
        agent = Agent(
            agent_id="test-001",
            name="测试助手", 
            type="assistant",
            role="test",
            tag="official"
        )
        
        agent_list = AgentListResponse(
            total=1,
            agents=[agent]
        )
        
        assert agent_list.total == 1
        assert len(agent_list.agents) == 1
        print("✓ Agent模型功能测试通过")
        
        # 测试ChatMessage模型
        chat_msg = ChatMessage(role="user", content="Hello")
        content_item = ContentItem(type="text", text="test content")
        
        assert chat_msg.role == "user"
        assert content_item.type == "text"
        print("✓ Chat模型功能测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("数据模型重构测试")
    print("=" * 50)
    
    tests = [
        ("新模型导入测试", test_new_model_imports),
        ("直接模型导入测试", test_direct_model_imports),
        ("路由导入测试", test_route_imports),
        ("应用结构测试", test_app_structure),
        ("模型功能测试", test_model_functionality),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'=' * 20} {test_name} {'=' * 20}")
        result = test_func()
        results.append((test_name, result))
    
    print(f"\n{'=' * 50}")
    print("测试结果汇总:")
    all_passed = True
    for test_name, result in results:
        status = "通过" if result else "失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！模型重构成功！")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，需要检查重构")
        sys.exit(1)