#!/usr/bin/env python3
"""
Test cases for topic node functionality
"""

import pytest
import json
from unittest.mock import Mock, patch
from langchain_core.messages import HumanMessage
from langgraph.types import Command

from src.graph.new_nodes import topic_node, Topic
from src.graph.types import State


class TestTopicNode:
    """Test suite for topic_node function"""

    @pytest.fixture
    def mock_state(self):
        """Create a mock state object for testing"""
        return State(
            messages=[
                HumanMessage(content="Let's discuss machine learning algorithms"),
            ],
            locale="en-US"
        )

    @pytest.fixture
    def mock_state_chinese(self):
        """Create a mock state object with Chinese content"""
        return State(
            messages=[
                HumanMessage(content="我们来讨论一下人工智能的发展"),
            ],
            locale="zh-CN"
        )

    @pytest.fixture
    def mock_llm_response(self):
        """Create a mock LLM response"""
        mock_response = Mock()
        mock_response.model_dump_json.return_value = json.dumps({
            "isNewTopic": True,
            "title": "Machine Learning",
            "language": "en"
        }, indent=4)
        return mock_response

    @pytest.fixture
    def mock_llm_response_chinese(self):
        """Create a mock LLM response for Chinese content"""
        mock_response = Mock()
        mock_response.model_dump_json.return_value = json.dumps({
            "isNewTopic": True,
            "title": "人工智能",
            "language": "zh-cn"
        }, indent=4)
        return mock_response

    @pytest.fixture
    def mock_llm_response_not_new_topic(self):
        """Create a mock LLM response for non-new topic"""
        mock_response = Mock()
        mock_response.model_dump_json.return_value = json.dumps({
            "isNewTopic": False,
            "title": None,
            "language": "en"
        }, indent=4)
        return mock_response

    @patch('src.graph.new_nodes.apply_prompt_template')
    @patch('src.graph.new_nodes.get_llm_by_type')
    @pytest.mark.anyio
    async def test_topic_node_new_topic_english(self, mock_get_llm, mock_apply_prompt, mock_state, mock_llm_response):
        """Test topic node with new English topic"""
        # Setup mocks
        mock_llm = Mock()
        mock_llm.with_structured_output.return_value.invoke.return_value = mock_llm_response
        mock_get_llm.return_value = mock_llm
        mock_apply_prompt.return_value = ["test messages"]

        # Execute
        result = await topic_node(mock_state)

        # Assertions
        assert isinstance(result, Command)
        assert result.graph == "__end__"
        mock_apply_prompt.assert_called_once_with("topic", mock_state)
        mock_get_llm.assert_called_once()
        mock_llm.with_structured_output.assert_called_once_with(Topic, method="json_mode")

    @patch('src.graph.new_nodes.apply_prompt_template')
    @patch('src.graph.new_nodes.get_llm_by_type')
    @pytest.mark.anyio
    async def test_topic_node_new_topic_chinese(self, mock_get_llm, mock_apply_prompt, mock_state_chinese, mock_llm_response_chinese):
        """Test topic node with new Chinese topic"""
        # Setup mocks
        mock_llm = Mock()
        mock_llm.with_structured_output.return_value.invoke.return_value = mock_llm_response_chinese
        mock_get_llm.return_value = mock_llm
        mock_apply_prompt.return_value = ["test messages"]

        # Execute
        result = await topic_node(mock_state_chinese)

        # Assertions
        assert isinstance(result, Command)
        assert result.graph == "__end__"
        mock_apply_prompt.assert_called_once_with("topic", mock_state_chinese)
        mock_get_llm.assert_called_once()

    @patch('src.graph.new_nodes.apply_prompt_template')
    @patch('src.graph.new_nodes.get_llm_by_type')
    @pytest.mark.anyio
    async def test_topic_node_not_new_topic(self, mock_get_llm, mock_apply_prompt, mock_state, mock_llm_response_not_new_topic):
        """Test topic node when it's not a new topic"""
        # Setup mocks
        mock_llm = Mock()
        mock_llm.with_structured_output.return_value.invoke.return_value = mock_llm_response_not_new_topic
        mock_get_llm.return_value = mock_llm
        mock_apply_prompt.return_value = ["test messages"]

        # Execute
        result = await topic_node(mock_state)

        # Assertions
        assert isinstance(result, Command)
        assert result.graph == "__end__"

    @patch('src.graph.new_nodes.apply_prompt_template')
    @patch('src.graph.new_nodes.get_llm_by_type')
    @patch('src.graph.new_nodes.repair_json_output')
    @pytest.mark.anyio
    async def test_topic_node_with_content_field(self, mock_repair_json, mock_get_llm, mock_apply_prompt, mock_state):
        """Test topic node when response has content field"""
        # Setup mocks
        mock_llm = Mock()
        mock_response = Mock()
        mock_response.model_dump_json.return_value = json.dumps({
            "content": json.dumps({
                "isNewTopic": True,
                "title": "Deep Learning",
                "language": "en"
            })
        }, indent=4)
        mock_llm.with_structured_output.return_value.invoke.return_value = mock_response
        mock_get_llm.return_value = mock_llm
        mock_apply_prompt.return_value = ["test messages"]
        mock_repair_json.side_effect = lambda x: x

        # Execute
        result = await topic_node(mock_state)

        # Assertions
        assert isinstance(result, Command)
        assert result.graph == "__end__"
        assert mock_repair_json.call_count == 2

    @patch('src.graph.new_nodes.apply_prompt_template')
    @patch('src.graph.new_nodes.get_llm_by_type')
    @patch('src.graph.new_nodes.repair_json_output')
    @patch('src.graph.new_nodes.logger')
    @pytest.mark.anyio
    async def test_topic_node_json_decode_error(self, mock_logger, mock_repair_json, mock_get_llm, mock_apply_prompt, mock_state):
        """Test topic node when JSON decode error occurs"""
        # Setup mocks
        mock_llm = Mock()
        mock_response = Mock()
        mock_response.model_dump_json.return_value = "invalid json"
        mock_llm.with_structured_output.return_value.invoke.return_value = mock_response
        mock_get_llm.return_value = mock_llm
        mock_apply_prompt.return_value = ["test messages"]
        mock_repair_json.side_effect = json.JSONDecodeError("Invalid JSON", "test", 0)

        # Execute
        result = await topic_node(mock_state)

        # Assertions
        assert isinstance(result, Command)
        assert result.graph == "__end__"
        mock_logger.warning.assert_called_once_with("Topic response is not a valid JSON")

    @patch('src.graph.new_nodes.apply_prompt_template')
    @patch('src.graph.new_nodes.get_llm_by_type')
    @patch('src.graph.new_nodes.logger')
    @pytest.mark.anyio
    async def test_topic_node_logging(self, mock_logger, mock_get_llm, mock_apply_prompt, mock_state, mock_llm_response):
        """Test topic node logging functionality"""
        # Setup mocks
        mock_llm = Mock()
        mock_llm.with_structured_output.return_value.invoke.return_value = mock_llm_response
        mock_get_llm.return_value = mock_llm
        mock_apply_prompt.return_value = ["test messages"]

        # Execute
        await topic_node(mock_state)

        # Assertions
        mock_logger.info.assert_called_once_with("topic generating.")
        mock_logger.debug.assert_called_once()


class TestTopicModel:
    """Test suite for Topic pydantic model"""

    def test_topic_model_creation(self):
        """Test Topic model creation with valid data"""
        topic = Topic(
            isNewTopic=True,
            title="Test Topic",
            language="en"
        )
        assert topic.isNewTopic is True
        assert topic.title == "Test Topic"
        assert topic.language == "en"

    def test_topic_model_serialization(self):
        """Test Topic model JSON serialization"""
        topic = Topic(
            isNewTopic=False,
            title="",
            language="zh-cn"
        )
        json_output = topic.model_dump_json()
        parsed = json.loads(json_output)
        
        assert parsed["isNewTopic"] is False
        assert parsed["title"] == ""
        assert parsed["language"] == "zh-cn"

    def test_topic_model_validation(self):
        """Test Topic model field validation"""
        with pytest.raises(Exception):
            Topic(
                isNewTopic="invalid",  # Should be boolean
                title="Test",
                language="en"
            )

    def test_topic_model_required_fields(self):
        """Test Topic model with missing required fields"""
        with pytest.raises(Exception):
            Topic(
                isNewTopic=True
                # Missing title and language
            )


if __name__ == "__main__":
    pytest.main([__file__])