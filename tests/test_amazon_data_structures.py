"""
Amazon数据结构测试

测试Amazon报告数据结构的功能和正确性。
"""

import json
import pytest
from src.tools.amazon_data_structures import (
    VendorSalesReport,
    VendorInventoryReport,
    SalesRecord,
    InventoryRecord,
    ReportSpecification,
    ReportOptions,
    AmazonReportParser,
    create_sales_report_from_json,
    create_inventory_report_from_json,
    analyze_asin_sales,
    analyze_asin_inventory
)


class TestSalesReportStructures:
    """测试销售报告数据结构"""
    
    def test_sales_record_creation(self):
        """测试销售记录创建"""
        record = SalesRecord(
            startTime="2024-01-01T00:00:00Z",
            endTime="2024-01-01T23:59:59Z",
            asin="B0123456789",
            orderedUnits=50,
            orderedRevenue=2500
        )
        
        assert record.asin == "B0123456789"
        assert record.orderedUnits == 50
        assert record.orderedRevenue == 2500
    
    def test_sales_record_from_dict(self):
        """测试从字典创建销售记录"""
        data = {
            "startTime": "2024-01-01T00:00:00Z",
            "endTime": "2024-01-01T23:59:59Z",
            "asin": "B0123456789",
            "orderedUnits": 50,
            "orderedRevenue": 2500
        }
        
        record = SalesRecord.from_dict(data)
        assert record.asin == "B0123456789"
        assert record.orderedUnits == 50
        assert record.orderedRevenue == 2500
    
    def test_vendor_sales_report_creation(self):
        """测试供应商销售报告创建"""
        sample_data = {
            "reportSpecification": {
                "reportType": "GET_VENDOR_REAL_TIME_SALES_REPORT",
                "dataStartTime": "2024-01-01T00:00:00Z",
                "dataEndTime": "2024-01-31T23:59:59Z",
                "marketplaceIds": ["ATVPDKIKX0DER"],
                "reportOptions": {
                    "currencyCode": "USD"
                }
            },
            "reportData": [
                {
                    "startTime": "2024-01-01T00:00:00Z",
                    "endTime": "2024-01-01T23:59:59Z",
                    "asin": "B0123456789",
                    "orderedUnits": 50,
                    "orderedRevenue": 2500
                }
            ]
        }
        
        report = VendorSalesReport.from_dict(sample_data)
        assert report.reportSpecification.reportType == "GET_VENDOR_REAL_TIME_SALES_REPORT"
        assert len(report.reportData) == 1
        assert report.reportData[0].asin == "B0123456789"


class TestAmazonReportParser:
    """测试Amazon报告解析器"""
    
    def test_parse_sales_report(self):
        """测试解析销售报告"""
        sample_data = {
            "reportSpecification": {
                "reportType": "GET_VENDOR_REAL_TIME_SALES_REPORT",
                "dataStartTime": "2024-01-01T00:00:00Z",
                "dataEndTime": "2024-01-31T23:59:59Z",
                "marketplaceIds": ["ATVPDKIKX0DER"],
                "reportOptions": {"currencyCode": "USD"}
            },
            "reportData": [
                {
                    "startTime": "2024-01-01T00:00:00Z",
                    "endTime": "2024-01-01T23:59:59Z",
                    "asin": "B0123456789",
                    "orderedUnits": 50,
                    "orderedRevenue": 2500
                }
            ]
        }
        
        json_data = json.dumps(sample_data)
        report = AmazonReportParser.parse_sales_report(json_data)
        
        assert isinstance(report, VendorSalesReport)
        assert len(report.reportData) == 1
        assert report.reportData[0].asin == "B0123456789"
    
    def test_filter_by_asin(self):
        """测试按ASIN过滤"""
        records = [
            SalesRecord("2024-01-01T00:00:00Z", "2024-01-01T23:59:59Z", "B0123456789", 50, 2500),
            SalesRecord("2024-01-01T00:00:00Z", "2024-01-01T23:59:59Z", "B0987654321", 30, 1200),
            SalesRecord("2024-01-02T00:00:00Z", "2024-01-02T23:59:59Z", "B0123456789", 75, 3750)
        ]
        
        filtered = AmazonReportParser.filter_by_asin(records, "B0123456789")
        assert len(filtered) == 2
        assert all(record.asin == "B0123456789" for record in filtered)
    
    def test_format_sales_summary(self):
        """测试格式化销售摘要"""
        records = [
            SalesRecord("2024-01-01T00:00:00Z", "2024-01-01T23:59:59Z", "B0123456789", 50, 2500),
            SalesRecord("2024-01-02T00:00:00Z", "2024-01-02T23:59:59Z", "B0123456789", 75, 3750)
        ]
        
        summary = AmazonReportParser.format_sales_summary(records)
        
        assert summary["total_units"] == 125
        assert summary["total_revenue"] == 6250
        assert summary["average_price"] == 50.0
        assert summary["record_count"] == 2


class TestConvenienceFunctions:
    """测试便捷函数"""
    
    def test_create_sales_report_from_json(self):
        """测试从JSON创建销售报告"""
        sample_data = {
            "reportSpecification": {
                "reportType": "GET_VENDOR_REAL_TIME_SALES_REPORT",
                "dataStartTime": "2024-01-01T00:00:00Z",
                "dataEndTime": "2024-01-31T23:59:59Z",
                "marketplaceIds": ["ATVPDKIKX0DER"],
                "reportOptions": {"currencyCode": "USD"}
            },
            "reportData": [
                {
                    "startTime": "2024-01-01T00:00:00Z",
                    "endTime": "2024-01-01T23:59:59Z",
                    "asin": "B0123456789",
                    "orderedUnits": 50,
                    "orderedRevenue": 2500
                }
            ]
        }
        
        json_data = json.dumps(sample_data)
        report = create_sales_report_from_json(json_data)
        
        assert isinstance(report, VendorSalesReport)
        assert report.reportSpecification.reportType == "GET_VENDOR_REAL_TIME_SALES_REPORT"
    
    def test_analyze_asin_sales(self):
        """测试分析ASIN销售数据"""
        sample_data = {
            "reportSpecification": {
                "reportType": "GET_VENDOR_REAL_TIME_SALES_REPORT",
                "dataStartTime": "2024-01-01T00:00:00Z",
                "dataEndTime": "2024-01-31T23:59:59Z",
                "marketplaceIds": ["ATVPDKIKX0DER"],
                "reportOptions": {"currencyCode": "USD"}
            },
            "reportData": [
                {
                    "startTime": "2024-01-01T00:00:00Z",
                    "endTime": "2024-01-01T23:59:59Z",
                    "asin": "B0123456789",
                    "orderedUnits": 50,
                    "orderedRevenue": 2500
                },
                {
                    "startTime": "2024-01-02T00:00:00Z",
                    "endTime": "2024-01-02T23:59:59Z",
                    "asin": "B0123456789",
                    "orderedUnits": 75,
                    "orderedRevenue": 3750
                }
            ]
        }
        
        report = VendorSalesReport.from_dict(sample_data)
        analysis = analyze_asin_sales(report, "B0123456789")
        
        assert analysis["asin"] == "B0123456789"
        assert analysis["summary"]["total_units"] == 125
        assert analysis["summary"]["total_revenue"] == 6250
        assert analysis["summary"]["average_price"] == 50.0
        assert analysis["summary"]["record_count"] == 2
        assert len(analysis["detailed_records"]) == 2


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"]) 