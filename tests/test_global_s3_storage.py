#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局S3存储实例测试

测试全局S3存储实例的初始化和基本功能
"""

import pytest
import tempfile
import json
from pathlib import Path

# 导入全局S3存储实例
from src.store import s3_storage


class TestGlobalS3Storage:
    """全局S3存储实例测试类"""
    
    def test_global_storage_initialization(self):
        """测试全局存储实例是否正确初始化"""
        # 全局存储实例应该已经初始化（如果配置正确）
        if s3_storage is not None:
            assert hasattr(s3_storage, 'bucket_name')
            assert hasattr(s3_storage, 's3_client')
            print(f"全局S3存储实例已初始化，存储桶: {s3_storage.bucket_name}")
        else:
            print("全局S3存储实例未初始化，跳过测试")
            pytest.skip("全局S3存储实例未初始化")
    
    def test_upload_and_download_text(self):
        """测试文本上传和下载功能"""
        if s3_storage is None:
            pytest.skip("全局S3存储实例未初始化")
        
        # 测试数据
        test_text = "这是全局S3存储的测试文本\n包含中文字符和换行符"
        s3_key = "test/global-storage-text.txt"
        
        try:
            # 上传文本
            success = s3_storage.upload_text(
                text=test_text,
                s3_key=s3_key,
                metadata={"test": "global-storage", "type": "text"}
            )
            assert success, "文本上传失败"
            
            # 验证文件存在
            exists = s3_storage.file_exists(s3_key)
            assert exists, "上传的文件不存在"
            
            # 下载文本
            downloaded_text = s3_storage.download_text(s3_key)
            assert downloaded_text == test_text, "下载的文本内容不匹配"
            
            # 获取文件信息
            file_info = s3_storage.get_file_info(s3_key)
            assert file_info is not None, "无法获取文件信息"
            assert file_info['size'] > 0, "文件大小应该大于0"
            assert 'test' in file_info['metadata'], "元数据不正确"
            
        finally:
            # 清理测试文件
            if s3_storage.file_exists(s3_key):
                s3_storage.delete_file(s3_key)
    
    def test_upload_and_download_json(self):
        """测试JSON上传和下载功能"""
        if s3_storage is None:
            pytest.skip("全局S3存储实例未初始化")
        
        # 测试数据
        test_data = {
            "message": "这是全局S3存储的测试JSON数据",
            "numbers": [1, 2, 3, 4, 5],
            "nested": {
                "key": "value",
                "chinese": "中文测试"
            }
        }
        s3_key = "test/global-storage-data.json"
        
        try:
            # 上传JSON
            success = s3_storage.upload_json(
                data=test_data,
                s3_key=s3_key,
                indent=2
            )
            assert success, "JSON上传失败"
            
            # 验证文件存在
            exists = s3_storage.file_exists(s3_key)
            assert exists, "上传的JSON文件不存在"
            
            # 下载JSON
            downloaded_data = s3_storage.download_json(s3_key)
            assert downloaded_data == test_data, "下载的JSON数据不匹配"
            
        finally:
            # 清理测试文件
            if s3_storage.file_exists(s3_key):
                s3_storage.delete_file(s3_key)
    
    def test_upload_and_download_binary_data(self):
        """测试二进制数据上传和下载功能"""
        if s3_storage is None:
            pytest.skip("全局S3存储实例未初始化")
        
        # 创建测试二进制数据
        test_data = b"This is binary test data\x00\x01\x02\x03"
        s3_key = "test/global-storage-binary.dat"
        
        try:
            # 上传二进制数据
            success = s3_storage.upload_data(
                data=test_data,
                s3_key=s3_key,
                content_type="application/octet-stream"
            )
            assert success, "二进制数据上传失败"
            
            # 验证文件存在
            exists = s3_storage.file_exists(s3_key)
            assert exists, "上传的二进制文件不存在"
            
            # 下载二进制数据
            downloaded_data = s3_storage.download_data(s3_key)
            assert downloaded_data == test_data, "下载的二进制数据不匹配"
            
        finally:
            # 清理测试文件
            if s3_storage.file_exists(s3_key):
                s3_storage.delete_file(s3_key)
    
    def test_file_operations(self):
        """测试文件操作功能"""
        if s3_storage is None:
            pytest.skip("全局S3存储实例未初始化")
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("这是临时文件的内容\n用于测试文件上传功能")
            temp_file_path = f.name
        
        s3_key = "test/global-storage-file.txt"
        
        try:
            # 上传文件
            success = s3_storage.upload_file(
                local_file_path=temp_file_path,
                s3_key=s3_key,
                metadata={"source": "temp-file", "test": "file-upload"}
            )
            assert success, "文件上传失败"
            
            # 验证文件存在
            exists = s3_storage.file_exists(s3_key)
            assert exists, "上传的文件不存在"
            
            # 下载文件
            download_path = tempfile.mktemp(suffix='.txt')
            success = s3_storage.download_file(s3_key, download_path)
            assert success, "文件下载失败"
            
            # 验证下载的文件内容
            with open(download_path, 'r', encoding='utf-8') as f:
                downloaded_content = f.read()
            
            with open(temp_file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            assert downloaded_content == original_content, "下载的文件内容不匹配"
            
        finally:
            # 清理文件
            import os
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
            if 'download_path' in locals() and os.path.exists(download_path):
                os.unlink(download_path)
            if s3_storage.file_exists(s3_key):
                s3_storage.delete_file(s3_key)
    
    def test_list_files(self):
        """测试文件列表功能"""
        if s3_storage is None:
            pytest.skip("全局S3存储实例未初始化")
        
        # 上传几个测试文件
        test_files = [
            ("test/list-test/file1.txt", "文件1的内容"),
            ("test/list-test/file2.txt", "文件2的内容"),
            ("test/list-test/subdir/file3.txt", "文件3的内容")
        ]
        
        try:
            # 上传测试文件
            for s3_key, content in test_files:
                success = s3_storage.upload_text(content, s3_key)
                assert success, f"上传文件 {s3_key} 失败"
            
            # 列出文件
            files = s3_storage.list_files(prefix="test/list-test/")
            assert len(files) >= len(test_files), "列出的文件数量不正确"
            
            # 验证文件键
            file_keys = [f['key'] for f in files]
            for s3_key, _ in test_files:
                assert s3_key in file_keys, f"文件 {s3_key} 未在列表中找到"
            
        finally:
            # 清理测试文件
            for s3_key, _ in test_files:
                if s3_storage.file_exists(s3_key):
                    s3_storage.delete_file(s3_key)


if __name__ == "__main__":
    # 直接运行测试
    test_instance = TestGlobalS3Storage()
    
    print("=== 全局S3存储实例测试 ===")
    
    try:
        test_instance.test_global_storage_initialization()
        print("✓ 全局存储实例初始化测试通过")
        
        test_instance.test_upload_and_download_text()
        print("✓ 文本上传下载测试通过")
        
        test_instance.test_upload_and_download_json()
        print("✓ JSON上传下载测试通过")
        
        test_instance.test_upload_and_download_binary_data()
        print("✓ 二进制数据上传下载测试通过")
        
        test_instance.test_file_operations()
        print("✓ 文件操作测试通过")
        
        test_instance.test_list_files()
        print("✓ 文件列表测试通过")
        
        print("\n所有测试通过！全局S3存储实例工作正常。")
        
    except Exception as e:
        print(f"\n测试失败: {e}")
        import traceback
        traceback.print_exc() 