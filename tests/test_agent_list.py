#!/usr/bin/env python3
"""
Simple test script for the agent list endpoint
"""
import httpx
import asyncio
import json


async def test_agent_list_endpoint():
    """Test the /v1/agent/list endpoint"""
    base_url = "http://localhost:8000"  # Adjust if server runs on different port
    url = f"{base_url}/v1/agent/list"
    
    test_cases = [
        {
            "name": "Basic request with required params",
            "params": {"company_id": "test-company-001"},
            "expected_status": 200
        },
        {
            "name": "Request with pagination",
            "params": {"company_id": "test-company-001", "limit": 2, "offset": 0},
            "expected_status": 200
        },
        {
            "name": "Request with larger offset",
            "params": {"company_id": "test-company-001", "limit": 2, "offset": 2},
            "expected_status": 200
        },
        {
            "name": "Missing company_id - should fail",
            "params": {"limit": 10, "offset": 0},
            "expected_status": 400
        }
    ]
    
    async with httpx.AsyncClient() as client:
        for test_case in test_cases:
            print(f"\nTesting: {test_case['name']}")
            print(f"Params: {test_case['params']}")
            
            try:
                response = await client.get(url, params=test_case["params"])
                print(f"Status: {response.status_code}")
                
                if response.status_code == test_case["expected_status"]:
                    print("✓ Status code matches expected")
                else:
                    print(f"✗ Expected {test_case['expected_status']}, got {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"Response structure: {list(data.keys())}")
                    if "data" in data and data["data"]:
                        agent_data = data["data"]
                        print(f"Total agents: {agent_data.get('total', 'N/A')}")
                        print(f"Agents returned: {len(agent_data.get('agents', []))}")
                        if agent_data.get('agents'):
                            first_agent = agent_data['agents'][0]
                            print(f"First agent: {first_agent}")
                else:
                    print(f"Error response: {response.text}")
                    
            except Exception as e:
                print(f"✗ Request failed: {e}")


if __name__ == "__main__":
    print("Agent List API Test")
    print("=" * 40)
    print("Note: Make sure the server is running on localhost:8000")
    print("Start server with: python server.py")
    print()
    
    asyncio.run(test_agent_list_endpoint())