#!/usr/bin/env python3
"""
测试 python_repl_tool 修复的脚本
验证是否能正确处理包含 markdown 代码块的代码
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.tools.python_repl import python_repl_tool, clean_code_input


def test_clean_code_input():
    """测试代码清理函数"""
    print("=== 测试代码清理函数 ===")
    
    # 测试用例1: 包含 ```python 的代码块
    test_code_1 = '''```python
import requests
print("Hello World")
```'''
    
    cleaned_1 = clean_code_input(test_code_1)
    print(f"测试用例1:")
    print(f"原始代码:\n{test_code_1}")
    print(f"清理后代码:\n{cleaned_1}")
    print(f"清理成功: {cleaned_1 == 'import requests\\nprint(\"Hello World\")'}")
    print()
    
    # 测试用例2: 包含 ``` 的代码块
    test_code_2 = '''```
import json
data = {"test": "value"}
print(json.dumps(data))
```'''
    
    cleaned_2 = clean_code_input(test_code_2)
    print(f"测试用例2:")
    print(f"原始代码:\n{test_code_2}")
    print(f"清理后代码:\n{cleaned_2}")
    print()
    
    # 测试用例3: 正常代码（无 markdown）
    test_code_3 = '''import os
print(os.getcwd())'''
    
    cleaned_3 = clean_code_input(test_code_3)
    print(f"测试用例3:")
    print(f"原始代码:\n{test_code_3}")
    print(f"清理后代码:\n{cleaned_3}")
    print(f"保持不变: {cleaned_3 == test_code_3}")
    print()


def test_python_repl_tool():
    """测试 python_repl_tool 函数"""
    print("=== 测试 python_repl_tool ===")
    
    # 测试用例1: 包含 markdown 的简单代码
    test_code = '''```python
print("测试 Python REPL 工具")
result = 2 + 3
print(f"2 + 3 = {result}")
```'''
    
    print("测试包含 markdown 代码块的执行:")
    result = python_repl_tool(test_code)
    print(f"执行结果:\n{result}")
    print()
    
    # 测试用例2: 正常代码
    normal_code = '''print("正常代码测试")
import math
print(f"π 的值: {math.pi}")'''
    
    print("测试正常代码的执行:")
    result = python_repl_tool(normal_code)
    print(f"执行结果:\n{result}")
    print()


def test_amazon_data_structures():
    """测试 Amazon 数据结构导入"""
    print("=== 测试 Amazon 数据结构导入 ===")
    
    test_code = '''```python
try:
    from src.tools.amazon_data_structures import (
        VendorSalesReport,
        VendorInventoryReport,
        create_sales_report_from_json,
        analyze_asin_sales
    )
    print("✓ Amazon 数据结构导入成功")
    print(f"VendorSalesReport: {VendorSalesReport}")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
```'''
    
    result = python_repl_tool(test_code)
    print(f"导入测试结果:\n{result}")


if __name__ == "__main__":
    print("开始测试 python_repl_tool 修复...")
    print("=" * 50)
    
    test_clean_code_input()
    test_python_repl_tool()
    test_amazon_data_structures()
    
    print("=" * 50)
    print("测试完成！") 