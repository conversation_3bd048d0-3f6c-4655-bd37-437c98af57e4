"""
测试条形码生成工具的S3上传功能
"""

import json
import pytest
import os
from pathlib import Path
from unittest.mock import patch, MagicMock

# 确保能导入我们的工具
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.tools.barcode_generator import generate_barcode, get_barcode_info
from src.server.app import parse_tool_result as app_parse_tool_result


def test_barcode_generation_json_format():
    """测试条形码生成工具返回JSON格式"""
    # 测试生成Code128条形码
    result = generate_barcode(
        data="TEST123", 
        format_type="code128", 
        output_format="png",
        upload_to_s3=False,  # 不上传到S3，避免需要S3配置
        return_json=True
    )
    
    # 验证返回的是JSON格式
    assert result.startswith('{') and result.endswith('}')
    
    # 解析JSON
    result_data = json.loads(result)
    
    # 验证JSON结构
    assert "message" in result_data
    assert "file_paths" in result_data
    assert "format" in result_data
    assert "data" in result_data
    assert "storage_location" in result_data
    assert "output_format" in result_data
    
    # 验证数据内容
    assert result_data["format"] == "code128"
    assert result_data["data"] == "TEST123"
    assert result_data["storage_location"] == "本地"
    assert result_data["output_format"] == "png"
    assert len(result_data["file_paths"]) == 1
    assert result_data["file_paths"][0].endswith(".png")


def test_barcode_generation_traditional_format():
    """测试条形码生成工具返回传统文本格式"""
    result = generate_barcode(
        data="TEST456", 
        format_type="code128", 
        output_format="png",
        upload_to_s3=False,
        return_json=False
    )
    
    # 验证返回的是文本格式
    assert not result.startswith('{')
    assert "Barcode generated successfully" in result
    assert "TEST456" not in result  # 文本格式不包含原始数据


def test_barcode_generation_base64_format():
    """测试条形码生成Base64格式"""
    result = generate_barcode(
        data="TEST789", 
        format_type="code128", 
        output_format="base64",
        return_json=True
    )
    
    # 解析JSON
    result_data = json.loads(result)
    
    # 验证JSON结构
    assert "base64_data" in result_data
    assert result_data["base64_data"].startswith("data:image/png;base64,")
    assert len(result_data["file_paths"]) == 0  # base64格式不产生文件


@patch('src.tools.barcode_generator.s3_storage')
def test_barcode_generation_with_s3_upload(mock_s3_storage):
    """测试条形码生成并上传到S3"""
    # 模拟S3存储实例
    mock_s3_instance = MagicMock()
    mock_s3_instance.upload_file.return_value = True
    mock_s3_instance.get_public_url.return_value = "https://example-bucket.s3.amazonaws.com/barcodes/test_barcode.png"
    mock_s3_storage = mock_s3_instance
    
    result = generate_barcode(
        data="TESTS3", 
        format_type="code128", 
        output_format="png",
        upload_to_s3=True,
        return_json=True
    )
    
    # 解析JSON
    result_data = json.loads(result)
    
    # 验证S3上传相关信息
    assert result_data["storage_location"] == "S3"
    assert len(result_data["file_paths"]) == 1
    assert result_data["file_paths"][0].startswith("https://")


def test_app_parse_tool_result():
    """测试app.py中的工具结果解析器"""
    
    # 测试JSON格式的工具结果
    json_result = json.dumps({
        "message": "条形码生成成功",
        "file_paths": ["https://example.com/barcode.png"],
        "format": "code128",
        "data": "TEST123"
    }, ensure_ascii=False)
    
    parsed = app_parse_tool_result(json_result)
    
    assert parsed["message"] == "条形码生成成功"
    assert parsed["file_paths"] == ["https://example.com/barcode.png"]
    assert parsed["metadata"]["format"] == "code128"
    assert parsed["metadata"]["data"] == "TEST123"
    
    # 测试非JSON格式的工具结果
    text_result = "Simple text result"
    parsed = app_parse_tool_result(text_result)
    
    assert parsed["message"] == "Simple text result"
    assert parsed["file_paths"] == []
    assert parsed["metadata"] == {}


def test_get_barcode_info():
    """测试获取条形码信息函数"""
    info = get_barcode_info()
    
    # 验证信息包含必要内容
    assert "条形码生成工具信息" in info
    assert "S3存储状态" in info
    assert "支持的条形码格式" in info
    assert "Code128" in info
    assert "返回格式" in info
    assert "示例用法" in info


def test_file_cleanup():
    """测试生成的本地文件是否存在（用于验证功能）"""
    # 生成一个条形码文件（不上传S3）
    result = generate_barcode(
        data="CLEANUP_TEST", 
        format_type="code128", 
        output_format="png",
        upload_to_s3=False,
        return_json=True
    )
    
    # 解析结果
    result_data = json.loads(result)
    file_path = result_data["file_paths"][0]
    
    # 验证文件存在
    assert Path(file_path).exists()
    
    # 清理测试文件
    Path(file_path).unlink()


if __name__ == "__main__":
    pytest.main([__file__]) 