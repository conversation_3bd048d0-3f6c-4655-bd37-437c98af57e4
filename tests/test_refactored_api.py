#!/usr/bin/env python3
"""
测试重构后的API结构
"""
import sys
import importlib

def test_imports():
    """测试所有模块是否能正常导入"""
    print("测试模块导入...")
    
    modules_to_test = [
        "src.server.app",
        "src.server.routes.chat_routes",
        "src.server.routes.credential_routes", 
        "src.server.routes.agent_routes",
        "src.server.routes.browser_routes"
    ]
    
    for module in modules_to_test:
        try:
            importlib.import_module(module)
            print(f"✓ {module} - 导入成功")
        except Exception as e:
            print(f"✗ {module} - 导入失败: {e}")
            return False
    
    return True

def test_app_structure():
    """测试应用结构"""
    print("\n测试应用结构...")
    
    try:
        from src.server.app import app, v1_router
        print("✓ FastAPI应用实例创建成功")
        print("✓ v1路由器创建成功")
        
        # 检查路由是否正确注册
        routes = [route.path for route in app.routes]
        expected_routes = [
            "/v1/conversation",
            "/v1/credential/list", 
            "/v1/credential/create",
            "/v1/credential/get",
            "/v1/agent/list",
            "/v1/browser/use",
            "/health"
        ]
        
        print(f"\n注册的路由: {len(routes)} 个")
        for route in routes:
            if hasattr(route, 'path'):
                print(f"  - {route.path}")
        
        # 检查关键路由是否存在
        missing_routes = []
        for expected in expected_routes:
            found = any(expected in route for route in routes)
            if found:
                print(f"✓ {expected} - 路由存在")
            else:
                print(f"✗ {expected} - 路由缺失")
                missing_routes.append(expected)
        
        if missing_routes:
            print(f"\n缺失的路由: {missing_routes}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 应用结构测试失败: {e}")
        return False

def test_route_modules():
    """测试路由模块的结构"""
    print("\n测试路由模块结构...")
    
    route_modules = [
        ("src.server.routes.chat_routes", ["router"]),
        ("src.server.routes.credential_routes", ["router"]),
        ("src.server.routes.agent_routes", ["router"]),
        ("src.server.routes.browser_routes", ["router"])
    ]
    
    all_passed = True
    
    for module_name, expected_attrs in route_modules:
        try:
            module = importlib.import_module(module_name)
            print(f"\n检查模块: {module_name}")
            
            for attr in expected_attrs:
                if hasattr(module, attr):
                    print(f"  ✓ {attr} 属性存在")
                else:
                    print(f"  ✗ {attr} 属性缺失")
                    all_passed = False
                    
        except Exception as e:
            print(f"✗ {module_name} 模块检查失败: {e}")
            all_passed = False
    
    return all_passed

if __name__ == "__main__":
    print("重构后API结构测试")
    print("=" * 50)
    
    tests = [
        ("模块导入测试", test_imports),
        ("应用结构测试", test_app_structure), 
        ("路由模块测试", test_route_modules)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'=' * 20} {test_name} {'=' * 20}")
        result = test_func()
        results.append((test_name, result))
    
    print(f"\n{'=' * 50}")
    print("测试结果汇总:")
    all_passed = True
    for test_name, result in results:
        status = "通过" if result else "失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！重构成功！")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，需要检查重构")
        sys.exit(1)