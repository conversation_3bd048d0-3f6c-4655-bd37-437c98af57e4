#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
S3存储服务单元测试

使用mock来测试S3Storage类的各种功能，不需要真实的AWS凭证。
"""

import os
import tempfile
import pytest
from unittest.mock import Mock, patch, MagicMock
from botocore.exceptions import ClientError, NoCredentialsError

from src.store.storage import S3Storage, create_s3_storage, create_s3_storage_from_env


class TestS3Storage:
    """S3Storage类的单元测试"""
    
    @patch('src.store.storage.boto3.Session')
    def test_init_success(self, mock_session):
        """测试成功初始化"""
        # 设置mock
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        
        # 创建实例
        storage = S3Storage(
            bucket_name="test-bucket",
            aws_access_key_id="test-key",
            aws_secret_access_key="test-secret"
        )
        
        # 验证
        assert storage.bucket_name == "test-bucket"
        assert storage.s3_client == mock_client
        mock_client.head_bucket.assert_called_once_with(Bucket="test-bucket")
    
    @patch('src.store.storage.boto3.Session')
    def test_init_no_credentials_error(self, mock_session):
        """测试凭证错误"""
        mock_session.side_effect = NoCredentialsError()
        
        with pytest.raises(NoCredentialsError):
            S3Storage(bucket_name="test-bucket")
    
    @patch('src.store.storage.boto3.Session')
    def test_init_bucket_not_found(self, mock_session):
        """测试存储桶不存在"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.side_effect = ClientError(
            {'Error': {'Code': '404'}}, 'head_bucket'
        )
        
        with pytest.raises(ClientError):
            S3Storage(bucket_name="non-existent-bucket")
    
    @patch('src.store.storage.boto3.Session')
    def test_upload_file_success(self, mock_session):
        """测试成功上传文件"""
        # 设置mock
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        mock_client.upload_file.return_value = None
        
        storage = S3Storage(bucket_name="test-bucket")
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False) as f:
            f.write(b"test content")
            temp_file = f.name
        
        try:
            # 测试上传
            result = storage.upload_file(temp_file, "test/file.txt")
            
            # 验证
            assert result is True
            mock_client.upload_file.assert_called_once()
            
        finally:
            os.unlink(temp_file)
    
    @patch('src.store.storage.boto3.Session')
    def test_upload_file_not_found(self, mock_session):
        """测试上传不存在的文件"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        
        storage = S3Storage(bucket_name="test-bucket")
        
        # 测试上传不存在的文件
        result = storage.upload_file("non-existent-file.txt", "test/file.txt")
        assert result is False
    
    @patch('src.store.storage.boto3.Session')
    def test_upload_data_success(self, mock_session):
        """测试成功上传数据"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        mock_client.upload_fileobj.return_value = None
        
        storage = S3Storage(bucket_name="test-bucket")
        
        # 测试上传数据
        test_data = b"test binary data"
        result = storage.upload_data(test_data, "test/data.bin")
        
        assert result is True
        mock_client.upload_fileobj.assert_called_once()
    
    @patch('src.store.storage.boto3.Session')
    def test_upload_text_success(self, mock_session):
        """测试成功上传文本"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        mock_client.upload_fileobj.return_value = None
        
        storage = S3Storage(bucket_name="test-bucket")
        
        # 测试上传文本
        test_text = "这是测试文本内容"
        result = storage.upload_text(test_text, "test/text.txt")
        
        assert result is True
        mock_client.upload_fileobj.assert_called_once()
    
    @patch('src.store.storage.boto3.Session')
    def test_upload_json_success(self, mock_session):
        """测试成功上传JSON"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        mock_client.upload_fileobj.return_value = None
        
        storage = S3Storage(bucket_name="test-bucket")
        
        # 测试上传JSON
        test_data = {"name": "test", "value": 123}
        result = storage.upload_json(test_data, "test/data.json")
        
        assert result is True
        mock_client.upload_fileobj.assert_called_once()
    
    @patch('src.store.storage.boto3.Session')
    def test_download_file_success(self, mock_session):
        """测试成功下载文件"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        mock_client.download_file.return_value = None
        
        storage = S3Storage(bucket_name="test-bucket")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            download_path = os.path.join(temp_dir, "downloaded_file.txt")
            result = storage.download_file("test/file.txt", download_path)
            
            assert result is True
            mock_client.download_file.assert_called_once_with(
                "test-bucket", "test/file.txt", download_path
            )
    
    @patch('src.store.storage.boto3.Session')
    def test_download_file_not_found(self, mock_session):
        """测试下载不存在的文件"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        mock_client.download_file.side_effect = ClientError(
            {'Error': {'Code': 'NoSuchKey'}}, 'download_file'
        )
        
        storage = S3Storage(bucket_name="test-bucket")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            download_path = os.path.join(temp_dir, "downloaded_file.txt")
            result = storage.download_file("non-existent-file.txt", download_path)
            
            assert result is False
    
    @patch('src.store.storage.boto3.Session')
    def test_download_data_success(self, mock_session):
        """测试成功下载数据"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        
        # 模拟下载数据
        test_data = b"test binary data"
        def mock_download_fileobj(bucket, key, fileobj):
            fileobj.write(test_data)
        
        mock_client.download_fileobj.side_effect = mock_download_fileobj
        
        storage = S3Storage(bucket_name="test-bucket")
        
        result = storage.download_data("test/data.bin")
        
        assert result == test_data
        mock_client.download_fileobj.assert_called_once()
    
    @patch('src.store.storage.boto3.Session')
    def test_download_text_success(self, mock_session):
        """测试成功下载文本"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        
        # 模拟下载文本数据
        test_text = "这是测试文本内容"
        test_data = test_text.encode('utf-8')
        def mock_download_fileobj(bucket, key, fileobj):
            fileobj.write(test_data)
        
        mock_client.download_fileobj.side_effect = mock_download_fileobj
        
        storage = S3Storage(bucket_name="test-bucket")
        
        result = storage.download_text("test/text.txt")
        
        assert result == test_text
        mock_client.download_fileobj.assert_called_once()
    
    @patch('src.store.storage.boto3.Session')
    def test_download_json_success(self, mock_session):
        """测试成功下载JSON"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        
        # 模拟下载JSON数据
        test_json = {"name": "test", "value": 123}
        test_data = '{"name": "test", "value": 123}'.encode('utf-8')
        def mock_download_fileobj(bucket, key, fileobj):
            fileobj.write(test_data)
        
        mock_client.download_fileobj.side_effect = mock_download_fileobj
        
        storage = S3Storage(bucket_name="test-bucket")
        
        result = storage.download_json("test/data.json")
        
        assert result == test_json
        mock_client.download_fileobj.assert_called_once()
    
    @patch('src.store.storage.boto3.Session')
    def test_download_data_not_found(self, mock_session):
        """测试下载不存在的数据"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        mock_client.download_fileobj.side_effect = ClientError(
            {'Error': {'Code': 'NoSuchKey'}}, 'download_fileobj'
        )
        
        storage = S3Storage(bucket_name="test-bucket")
        
        result = storage.download_data("non-existent-file.txt")
        
        assert result is None
    
    @patch('src.store.storage.boto3.Session')
    def test_delete_file_success(self, mock_session):
        """测试成功删除文件"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        mock_client.delete_object.return_value = {}
        
        storage = S3Storage(bucket_name="test-bucket")
        
        result = storage.delete_file("test/file.txt")
        
        assert result is True
        mock_client.delete_object.assert_called_once_with(
            Bucket="test-bucket", Key="test/file.txt"
        )
    
    @patch('src.store.storage.boto3.Session')
    def test_file_exists_true(self, mock_session):
        """测试文件存在检查 - 存在"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        mock_client.head_object.return_value = {}
        
        storage = S3Storage(bucket_name="test-bucket")
        
        result = storage.file_exists("test/file.txt")
        
        assert result is True
        mock_client.head_object.assert_called_once_with(
            Bucket="test-bucket", Key="test/file.txt"
        )
    
    @patch('src.store.storage.boto3.Session')
    def test_file_exists_false(self, mock_session):
        """测试文件存在检查 - 不存在"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        mock_client.head_object.side_effect = ClientError(
            {'Error': {'Code': '404'}}, 'head_object'
        )
        
        storage = S3Storage(bucket_name="test-bucket")
        
        result = storage.file_exists("non-existent-file.txt")
        
        assert result is False
    
    @patch('src.store.storage.boto3.Session')
    def test_get_file_info_success(self, mock_session):
        """测试获取文件信息 - 成功"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        mock_client.head_object.return_value = {
            'ContentLength': 1024,
            'LastModified': '2024-01-01T00:00:00Z',
            'ContentType': 'text/plain',
            'Metadata': {'author': 'test'},
            'ETag': '"abc123"'
        }
        
        storage = S3Storage(bucket_name="test-bucket")
        
        result = storage.get_file_info("test/file.txt")
        
        assert result is not None
        assert result['size'] == 1024
        assert result['content_type'] == 'text/plain'
        assert result['metadata'] == {'author': 'test'}
    
    @patch('src.store.storage.boto3.Session')
    def test_get_file_info_not_found(self, mock_session):
        """测试获取文件信息 - 文件不存在"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        mock_client.head_object.side_effect = ClientError(
            {'Error': {'Code': '404'}}, 'head_object'
        )
        
        storage = S3Storage(bucket_name="test-bucket")
        
        result = storage.get_file_info("non-existent-file.txt")
        
        assert result is None
    
    @patch('src.store.storage.boto3.Session')
    def test_list_files_success(self, mock_session):
        """测试列出文件 - 成功"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        mock_client.list_objects_v2.return_value = {
            'Contents': [
                {
                    'Key': 'test/file1.txt',
                    'Size': 1024,
                    'LastModified': '2024-01-01T00:00:00Z',
                    'ETag': '"abc123"'
                },
                {
                    'Key': 'test/file2.txt',
                    'Size': 2048,
                    'LastModified': '2024-01-02T00:00:00Z',
                    'ETag': '"def456"'
                }
            ]
        }
        
        storage = S3Storage(bucket_name="test-bucket")
        
        result = storage.list_files(prefix="test/", max_keys=10)
        
        assert len(result) == 2
        assert result[0]['key'] == 'test/file1.txt'
        assert result[0]['size'] == 1024
        assert result[1]['key'] == 'test/file2.txt'
        assert result[1]['size'] == 2048
    
    @patch('src.store.storage.boto3.Session')
    def test_list_files_empty(self, mock_session):
        """测试列出文件 - 空结果"""
        mock_client = Mock()
        mock_session.return_value.client.return_value = mock_client
        mock_client.head_bucket.return_value = {}
        mock_client.list_objects_v2.return_value = {}
        
        storage = S3Storage(bucket_name="test-bucket")
        
        result = storage.list_files()
        
        assert result == []


class TestFactoryFunctions:
    """工厂函数测试"""
    
    @patch('src.store.storage.S3Storage')
    def test_create_s3_storage(self, mock_s3_storage):
        """测试create_s3_storage工厂函数"""
        mock_instance = Mock()
        mock_s3_storage.return_value = mock_instance
        
        result = create_s3_storage(
            bucket_name="test-bucket",
            aws_access_key_id="test-key",
            aws_secret_access_key="test-secret"
        )
        
        assert result == mock_instance
        mock_s3_storage.assert_called_once_with(
            bucket_name="test-bucket",
            aws_access_key_id="test-key",
            aws_secret_access_key="test-secret",
            region_name="us-east-1",
            endpoint_url=None
        )
    
    @patch('src.store.storage.S3Storage')
    @patch.dict(os.environ, {
        'AWS_S3_BUCKET_NAME': 'test-bucket',
        'AWS_ACCESS_KEY_ID': 'test-key',
        'AWS_SECRET_ACCESS_KEY': 'test-secret',
        'AWS_DEFAULT_REGION': 'us-west-2'
    })
    def test_create_s3_storage_from_env_success(self, mock_s3_storage):
        """测试从环境变量创建S3存储实例 - 成功"""
        mock_instance = Mock()
        mock_s3_storage.return_value = mock_instance
        
        result = create_s3_storage_from_env()
        
        assert result == mock_instance
        mock_s3_storage.assert_called_once_with(
            bucket_name="test-bucket",
            aws_access_key_id="test-key",
            aws_secret_access_key="test-secret",
            region_name="us-west-2",
            endpoint_url=None
        )
    
    @patch.dict(os.environ, {}, clear=True)
    def test_create_s3_storage_from_env_missing_bucket(self):
        """测试从环境变量创建S3存储实例 - 缺少存储桶名称"""
        with pytest.raises(ValueError, match="环境变量 AWS_S3_BUCKET_NAME 必须设置"):
            create_s3_storage_from_env()


if __name__ == "__main__":
    pytest.main([__file__]) 