# Repository Guidelines

## Project Structure & Module Organization
- Source code: `src/` — core modules in `agents/`, `graph/`, `server/`, `store/postgres/`, `crawler/`, `llms/`, `podcast/`, `ppt/`, `prose/`, `prompts/`, `tools/`, `utils/`.
- Tests: `tests/` with pytest suites mirroring `src/` structure.
- Entrypoints: `server.py` (FastAPI), optional CLI/dev via LangGraph. Logs in `logs/`.

## Build, Test, and Development Commands
- Setup env: `uv venv && source .venv/bin/activate && uv pip install -e .`
- Run API: `uv run server.py --port 8080` (see `/docs`).
- Tests: `python -m pytest -q` (configured via `pyproject.toml`).
- LangGraph dev: `uvx --refresh --from "langgraph-cli[inmem]" --with-editable . --python 3.12 langgraph dev --allow-blocking`.
- Lint/format: Ruff is configured; if installed, run `ruff check .` and `ruff format .`.

## Coding Style & Naming Conventions
- Language: Python 3.13. Line length 88 (`tool.ruff`).
- Naming: `lower_snake_case` for modules/functions/variables, `PascalCase` for classes, `UPPER_SNAKE_CASE` for constants.
- Type hints required for public functions; keep functions small and focused.
- Docstrings: concise one-line summary plus param/return notes where useful.

## Testing Guidelines
- Framework: pytest. Place tests under `tests/` using `test_*.py` naming.
- Aim for fast, deterministic unit tests; mock network/DB I/O.
- Add tests with new features and bug fixes; reproduce before fixing.
- Run `pytest -q` locally before opening a PR.

## Commit & Pull Request Guidelines
- Commit messages: follow Conventional Commits seen in history (e.g., `feat: ...`, `fix: ...`, `chore: ...`).
- PRs must include: clear description, linked issue(s), steps to verify. For API changes, include example request/response and update docs if needed.
- Ensure all tests pass and code lints cleanly; no unrelated changes.

## Security & Configuration Tips
- Do not commit secrets. Use `.env` (see `.env.example`). Common keys: `OPENAI_API_KEY`, `TAVILY_API_KEY`, `CRAWL_TYPE`, `POSTGRES_URI`.
- Prefer local Postgres during development; tables initialize on first run.

## Agent-Specific Notes
- Adding an agent: implement under `src/agents/`, wire into workflows in `src/graph/`, place prompts in `src/prompts/`, and register tools in `src/tools/`. Isolate side effects behind interfaces in `src/store/`.

